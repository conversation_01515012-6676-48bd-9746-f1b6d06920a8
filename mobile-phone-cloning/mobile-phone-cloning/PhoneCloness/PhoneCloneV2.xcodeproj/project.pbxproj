// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		0405864224AC12AD003B11AE /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0405864124AC12AD003B11AE /* StoreKit.framework */; };
		0C5832C6249F6FE40012BAB0 /* BSAccountModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0C5832C5249F6FE40012BAB0 /* BSAccountModel.m */; };
		13F6B3D98508CF9E050A77F1 /* Pods_PhoneCloneV2.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6900953C8B1569EFE6C0752D /* Pods_PhoneCloneV2.framework */; };
		A514D6512B58C21300C207FC /* QRcodeView.m in Sources */ = {isa = PBXBuildFile; fileRef = A514D64F2B58C21300C207FC /* QRcodeView.m */; };
		A514D6572B58C28800C207FC /* PopAnimationTool.m in Sources */ = {isa = PBXBuildFile; fileRef = A514D6532B58C28700C207FC /* PopAnimationTool.m */; };
		A514D6582B58C28800C207FC /* PopView.m in Sources */ = {isa = PBXBuildFile; fileRef = A514D6542B58C28700C207FC /* PopView.m */; };
		A52E87112B576C8900708A0F /* startLinkViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A52E870F2B576C8800708A0F /* startLinkViewController.m */; };
		A52E87162B576CBD00708A0F /* MySegmentedControl.m in Sources */ = {isa = PBXBuildFile; fileRef = A52E87142B576CBD00708A0F /* MySegmentedControl.m */; };
		A52E871A2B576FE700708A0F /* UILabel+createLabels.m in Sources */ = {isa = PBXBuildFile; fileRef = A52E87192B576FE700708A0F /* UILabel+createLabels.m */; };
		A52E871D2B57702200708A0F /* ZYELabel.m in Sources */ = {isa = PBXBuildFile; fileRef = A52E871B2B57702200708A0F /* ZYELabel.m */; };
		A52E87202B57704300708A0F /* UIColor+Hex.m in Sources */ = {isa = PBXBuildFile; fileRef = A52E871F2B57704300708A0F /* UIColor+Hex.m */; };
		A52E87252B57711300708A0F /* UIButton+CenterImageAndTitle.m in Sources */ = {isa = PBXBuildFile; fileRef = A52E87212B57711300708A0F /* UIButton+CenterImageAndTitle.m */; };
		A52E87262B57711300708A0F /* UIButton+Create.m in Sources */ = {isa = PBXBuildFile; fileRef = A52E87222B57711300708A0F /* UIButton+Create.m */; };
		A52E87292B57716400708A0F /* ZYEButton.m in Sources */ = {isa = PBXBuildFile; fileRef = A52E87282B57716400708A0F /* ZYEButton.m */; };
		A5D274C22B54C5B900552859 /* BSConnectManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D274C12B54C5B800552859 /* BSConnectManager.m */; };
		A5D274C52B54C5F400552859 /* tokenTool.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D274C42B54C5F400552859 /* tokenTool.m */; };
		A5D274C82B55196D00552859 /* BSNewSendServer.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D274C72B55196D00552859 /* BSNewSendServer.m */; };
		A5D7178F2B4F867800AE995C /* httpNework.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D7178E2B4F867800AE995C /* httpNework.m */; };
		A5D717A22B4F86B800AE995C /* MyHTTPConnection.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D717922B4F86B800AE995C /* MyHTTPConnection.m */; };
		A5D717A32B4F86B800AE995C /* MyHTTPResponse.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D717942B4F86B800AE995C /* MyHTTPResponse.m */; };
		A5D717A42B4F86B800AE995C /* MyCustomDataResponse.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D717952B4F86B800AE995C /* MyCustomDataResponse.m */; };
		A5D717A52B4F86B800AE995C /* WebGelistManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D717992B4F86B800AE995C /* WebGelistManager.m */; };
		A5D717A62B4F86B800AE995C /* NewScaner.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D7179E2B4F86B800AE995C /* NewScaner.m */; };
		A5D717A72B4F86B800AE995C /* scannner.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D7179F2B4F86B800AE995C /* scannner.m */; };
		A5D717A82B4F86B800AE995C /* DHIPAdress.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D717A02B4F86B800AE995C /* DHIPAdress.m */; };
		A5D717AB2B4F871A00AE995C /* PCObjcect.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D717AA2B4F871A00AE995C /* PCObjcect.m */; };
		A5D717D12B4F8A1000AE995C /* FileManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D717CF2B4F8A0F00AE995C /* FileManager.m */; };
		A5D717D62B4F8B5A00AE995C /* File.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D717D52B4F8B5A00AE995C /* File.m */; };
		A5D717D92B4F8BCB00AE995C /* CalendarListEventInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D717D82B4F8BCB00AE995C /* CalendarListEventInfo.m */; };
		A5DCC9F72B5F9D66004151F8 /* UIImageView+Create.m in Sources */ = {isa = PBXBuildFile; fileRef = A5DCC9E62B5F9D66004151F8 /* UIImageView+Create.m */; };
		A5DCC9F82B5F9D66004151F8 /* MyAdManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A5DCC9EA2B5F9D66004151F8 /* MyAdManager.m */; };
		A5DCC9F92B5F9D66004151F8 /* UpdateManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A5DCC9EB2B5F9D66004151F8 /* UpdateManager.m */; };
		A5DCC9FA2B5F9D66004151F8 /* UIView+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = A5DCC9ED2B5F9D66004151F8 /* UIView+Extension.m */; };
		A5DCC9FC2B5F9D66004151F8 /* ZYEUpdateView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5DCC9F02B5F9D66004151F8 /* ZYEUpdateView.m */; };
		A5E272C42B834B78008BF227 /* BSLinkView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5E272C32B834B77008BF227 /* BSLinkView.m */; };
		A5E272C72B834E2D008BF227 /* BSLinkButton.m in Sources */ = {isa = PBXBuildFile; fileRef = A5E272C62B834E2D008BF227 /* BSLinkButton.m */; };
		A5E272CA2B835328008BF227 /* UIView+cornerRadius.m in Sources */ = {isa = PBXBuildFile; fileRef = A5E272C82B835328008BF227 /* UIView+cornerRadius.m */; };
		C40BBF15243B2DAA00061B3D /* EventKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C40BBF14243B2DAA00061B3D /* EventKit.framework */; };
		C40BBF17243B2DD700061B3D /* EventKitUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C40BBF16243B2DD700061B3D /* EventKitUI.framework */; };
		C416403B2438A9CA0079BCB8 /* BSDeviceInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = C416403A2438A9CA0079BCB8 /* BSDeviceInfo.m */; };
		C416403E2438ACE40079BCB8 /* UIDevice+Screen.m in Sources */ = {isa = PBXBuildFile; fileRef = C416403D2438ACE40079BCB8 /* UIDevice+Screen.m */; };
		C459F5FE243A27B000169E98 /* BSTransferFileProgressVC.m in Sources */ = {isa = PBXBuildFile; fileRef = C459F5FC243A27B000169E98 /* BSTransferFileProgressVC.m */; };
		C459F5FF243A27B000169E98 /* BSTransferFileProgressVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = C459F5FD243A27B000169E98 /* BSTransferFileProgressVC.xib */; };
		C462B2FB2434EEC0007EDF29 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = C462B2FA2434EEC0007EDF29 /* AppDelegate.m */; };
		C462B3032434EEC1007EDF29 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = C462B3022434EEC1007EDF29 /* Assets.xcassets */; };
		C462B3062434EEC1007EDF29 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = C462B3042434EEC1007EDF29 /* LaunchScreen.storyboard */; };
		C462B3092434EEC1007EDF29 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = C462B3082434EEC1007EDF29 /* main.m */; };
		C467ECB124388F7200FBC375 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = C467ECB324388F7200FBC375 /* Localizable.strings */; };
		C480B23D2434FC2900637D3A /* BSBaseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = C480B23C2434FC2900637D3A /* BSBaseViewController.m */; };
		C480B2402434FCE000637D3A /* BSNavigationController.m in Sources */ = {isa = PBXBuildFile; fileRef = C480B23F2434FCE000637D3A /* BSNavigationController.m */; };
		C49824532438D172003C94B0 /* NSTimer+timerBlock.m in Sources */ = {isa = PBXBuildFile; fileRef = C498244F2438D172003C94B0 /* NSTimer+timerBlock.m */; };
		C49824542438D172003C94B0 /* ZZCircleProgress.m in Sources */ = {isa = PBXBuildFile; fileRef = C49824522438D172003C94B0 /* ZZCircleProgress.m */; };
		C4A374E62439DA3A00FA82D9 /* BSPeerModel.m in Sources */ = {isa = PBXBuildFile; fileRef = C4A374E52439DA3A00FA82D9 /* BSPeerModel.m */; };
		C4D8E6B02438888900199E15 /* BSMeVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = C4D8E6B22438888900199E15 /* BSMeVC.xib */; };
		C4E8AB422436F136004B4331 /* BSTabBarController.m in Sources */ = {isa = PBXBuildFile; fileRef = C4E8AB412436F136004B4331 /* BSTabBarController.m */; };
		C4ECC6C524373AD3004C60C9 /* JXCategoryTitleVerticalZoomCellModel.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC66E24373AD2004C60C9 /* JXCategoryTitleVerticalZoomCellModel.m */; };
		C4ECC6C624373AD3004C60C9 /* JXCategoryTitleVerticalZoomCell.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC66F24373AD2004C60C9 /* JXCategoryTitleVerticalZoomCell.m */; };
		C4ECC6C724373AD3004C60C9 /* JXCategoryTitleVerticalZoomView.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC67024373AD2004C60C9 /* JXCategoryTitleVerticalZoomView.m */; };
		C4ECC6C824373AD3004C60C9 /* JXCategoryIndicatorLineView.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC67724373AD3004C60C9 /* JXCategoryIndicatorLineView.m */; };
		C4ECC6C924373AD3004C60C9 /* JXCategoryIndicatorTriangleView.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC67824373AD3004C60C9 /* JXCategoryIndicatorTriangleView.m */; };
		C4ECC6CA24373AD3004C60C9 /* JXCategoryIndicatorRainbowLineView.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC67924373AD3004C60C9 /* JXCategoryIndicatorRainbowLineView.m */; };
		C4ECC6CB24373AD3004C60C9 /* JXCategoryIndicatorImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC67B24373AD3004C60C9 /* JXCategoryIndicatorImageView.m */; };
		C4ECC6CC24373AD3004C60C9 /* JXCategoryIndicatorBallView.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC67C24373AD3004C60C9 /* JXCategoryIndicatorBallView.m */; };
		C4ECC6CD24373AD3004C60C9 /* JXCategoryIndicatorComponentView.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC67D24373AD3004C60C9 /* JXCategoryIndicatorComponentView.m */; };
		C4ECC6CE24373AD3004C60C9 /* JXCategoryIndicatorDotLineView.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC67F24373AD3004C60C9 /* JXCategoryIndicatorDotLineView.m */; };
		C4ECC6CF24373AD3004C60C9 /* JXCategoryIndicatorBackgroundView.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC68424373AD3004C60C9 /* JXCategoryIndicatorBackgroundView.m */; };
		C4ECC6D024373AD3004C60C9 /* JXCategoryIndicatorCellModel.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC68624373AD3004C60C9 /* JXCategoryIndicatorCellModel.m */; };
		C4ECC6D124373AD3004C60C9 /* JXCategoryIndicatorCell.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC68724373AD3004C60C9 /* JXCategoryIndicatorCell.m */; };
		C4ECC6D224373AD3004C60C9 /* JXCategoryIndicatorView.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC68824373AD3004C60C9 /* JXCategoryIndicatorView.m */; };
		C4ECC6D324373AD3004C60C9 /* JXCategoryDotCell.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC68A24373AD3004C60C9 /* JXCategoryDotCell.m */; };
		C4ECC6D424373AD3004C60C9 /* JXCategoryDotView.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC68C24373AD3004C60C9 /* JXCategoryDotView.m */; };
		C4ECC6D524373AD3004C60C9 /* JXCategoryDotCellModel.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC68F24373AD3004C60C9 /* JXCategoryDotCellModel.m */; };
		C4ECC6D624373AD3004C60C9 /* JXCategoryTitleImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC69124373AD3004C60C9 /* JXCategoryTitleImageView.m */; };
		C4ECC6D724373AD3004C60C9 /* JXCategoryTitleImageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC69224373AD3004C60C9 /* JXCategoryTitleImageCell.m */; };
		C4ECC6D824373AD3004C60C9 /* JXCategoryTitleImageCellModel.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC69524373AD3004C60C9 /* JXCategoryTitleImageCellModel.m */; };
		C4ECC6D924373AD3004C60C9 /* JXCategoryTitleCellModel.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC69B24373AD3004C60C9 /* JXCategoryTitleCellModel.m */; };
		C4ECC6DA24373AD3004C60C9 /* JXCategoryTitleCell.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC69C24373AD3004C60C9 /* JXCategoryTitleCell.m */; };
		C4ECC6DB24373AD3004C60C9 /* JXCategoryTitleView.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC69D24373AD3004C60C9 /* JXCategoryTitleView.m */; };
		C4ECC6DC24373AD3004C60C9 /* JXCategoryImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC6A024373AD3004C60C9 /* JXCategoryImageView.m */; };
		C4ECC6DD24373AD3004C60C9 /* JXCategoryImageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC6A124373AD3004C60C9 /* JXCategoryImageCell.m */; };
		C4ECC6DE24373AD3004C60C9 /* JXCategoryImageCellModel.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC6A524373AD3004C60C9 /* JXCategoryImageCellModel.m */; };
		C4ECC6DF24373AD3004C60C9 /* JXCategoryNumberView.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC6A824373AD3004C60C9 /* JXCategoryNumberView.m */; };
		C4ECC6E024373AD3004C60C9 /* JXCategoryNumberCell.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC6A924373AD3004C60C9 /* JXCategoryNumberCell.m */; };
		C4ECC6E124373AD3004C60C9 /* JXCategoryNumberCellModel.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC6AA24373AD3004C60C9 /* JXCategoryNumberCellModel.m */; };
		C4ECC6E224373AD3004C60C9 /* JXCategoryListContainerView.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC6AE24373AD3004C60C9 /* JXCategoryListContainerView.m */; };
		C4ECC6E324373AD3004C60C9 /* JXCategoryFactory.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC6B524373AD3004C60C9 /* JXCategoryFactory.m */; };
		C4ECC6E424373AD3004C60C9 /* JXCategoryCollectionView.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC6B724373AD3004C60C9 /* JXCategoryCollectionView.m */; };
		C4ECC6E524373AD3004C60C9 /* JXCategoryIndicatorParamsModel.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC6B824373AD3004C60C9 /* JXCategoryIndicatorParamsModel.m */; };
		C4ECC6E624373AD3004C60C9 /* JXCategoryViewAnimator.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC6B924373AD3004C60C9 /* JXCategoryViewAnimator.m */; };
		C4ECC6E724373AD3004C60C9 /* JXCategoryListCollectionContainerView.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC6BB24373AD3004C60C9 /* JXCategoryListCollectionContainerView.m */; };
		C4ECC6E824373AD3004C60C9 /* UIColor+JXAdd.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC6BC24373AD3004C60C9 /* UIColor+JXAdd.m */; };
		C4ECC6E924373AD3004C60C9 /* JXCategoryBaseView.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC6C224373AD3004C60C9 /* JXCategoryBaseView.m */; };
		C4ECC6EA24373AD3004C60C9 /* JXCategoryBaseCell.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC6C324373AD3004C60C9 /* JXCategoryBaseCell.m */; };
		C4ECC6EB24373AD3004C60C9 /* JXCategoryBaseCellModel.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC6C424373AD3004C60C9 /* JXCategoryBaseCellModel.m */; };
		C4ECC6EE24373AE2004C60C9 /* NullSafe.m in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC6ED24373AE2004C60C9 /* NullSafe.m */; };
		C4ECC6F224373AEE004C60C9 /* pinyin.c in Sources */ = {isa = PBXBuildFile; fileRef = C4ECC6F124373AEE004C60C9 /* pinyin.c */; };
		C4F8A4FF2437128E003FAB69 /* BSMeVC.m in Sources */ = {isa = PBXBuildFile; fileRef = C4F8A4FE2437128E003FAB69 /* BSMeVC.m */; };
		CE29773824A84983000861B2 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = CE29773A24A84983000861B2 /* InfoPlist.strings */; };
		CE29774F24AA39C8000861B2 /* Reachability.m in Sources */ = {isa = PBXBuildFile; fileRef = CE29774124AA39C8000861B2 /* Reachability.m */; };
		CE29775124AA39C8000861B2 /* StoreIAPObserver.m in Sources */ = {isa = PBXBuildFile; fileRef = CE29774424AA39C8000861B2 /* StoreIAPObserver.m */; };
		CE29775224AA39C8000861B2 /* ProgressViewUserInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = CE29774524AA39C8000861B2 /* ProgressViewUserInfo.m */; };
		CE29775324AA39C8000861B2 /* NSString+Base64.m in Sources */ = {isa = PBXBuildFile; fileRef = CE29774624AA39C8000861B2 /* NSString+Base64.m */; };
		CE29775424AA39C8000861B2 /* StoreIAPManager.m in Sources */ = {isa = PBXBuildFile; fileRef = CE29774824AA39C8000861B2 /* StoreIAPManager.m */; };
		************************ /* NSDictionary+Value.m in Sources */ = {isa = PBXBuildFile; fileRef = CE29774E24AA39C8000861B2 /* NSDictionary+Value.m */; };
		CE29775B24AA3C23000861B2 /* IntroduceBackground01.jpg in Resources */ = {isa = PBXBuildFile; fileRef = CE29775624AA3C23000861B2 /* IntroduceBackground01.jpg */; };
		CE29775C24AA3C23000861B2 /* AgreementBackground.jpg in Resources */ = {isa = PBXBuildFile; fileRef = CE29775724AA3C23000861B2 /* AgreementBackground.jpg */; };
		CE29775D24AA3C23000861B2 /* LaunchScreen.jpg in Resources */ = {isa = PBXBuildFile; fileRef = CE29775824AA3C23000861B2 /* LaunchScreen.jpg */; };
		CE29775E24AA3C23000861B2 /* IntroduceBackground02.jpg in Resources */ = {isa = PBXBuildFile; fileRef = CE29775924AA3C23000861B2 /* IntroduceBackground02.jpg */; };
		CE29775F24AA3C23000861B2 /* EnterViewBackground.jpg in Resources */ = {isa = PBXBuildFile; fileRef = CE29775A24AA3C23000861B2 /* EnterViewBackground.jpg */; };
		CE4962AA24A6065100E8491C /* SGQRCode.bundle in Resources */ = {isa = PBXBuildFile; fileRef = CE49629C24A6065100E8491C /* SGQRCode.bundle */; };
		CE4962AB24A6065100E8491C /* UIImage+SGImageSize.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49629E24A6065100E8491C /* UIImage+SGImageSize.m */; };
		CE4962AC24A6065100E8491C /* SGQRCodeHelperTool.m in Sources */ = {isa = PBXBuildFile; fileRef = CE4962A024A6065100E8491C /* SGQRCodeHelperTool.m */; };
		************************ /* SGQRCodeScanningView.m in Sources */ = {isa = PBXBuildFile; fileRef = CE4962A124A6065100E8491C /* SGQRCodeScanningView.m */; };
		************************ /* SGQRCodeScanManager.m in Sources */ = {isa = PBXBuildFile; fileRef = CE4962A224A6065100E8491C /* SGQRCodeScanManager.m */; };
		CE4962AF24A6065100E8491C /* SGQRCodeGenerateManager.m in Sources */ = {isa = PBXBuildFile; fileRef = CE4962A324A6065100E8491C /* SGQRCodeGenerateManager.m */; };
		CE4962B024A6065100E8491C /* SGQRCodeAlbumManager.m in Sources */ = {isa = PBXBuildFile; fileRef = CE4962A824A6065100E8491C /* SGQRCodeAlbumManager.m */; };
		CE4962B524A6085100E8491C /* BSTransferRes.m in Sources */ = {isa = PBXBuildFile; fileRef = CE4962B124A6085100E8491C /* BSTransferRes.m */; };
		CE4962B624A6085100E8491C /* BSResGroupModel.m in Sources */ = {isa = PBXBuildFile; fileRef = CE4962B224A6085100E8491C /* BSResGroupModel.m */; };
		************************ /* BSMusic.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49641424A60C1100E8491C /* BSMusic.m */; };
		CE49649F24A60C1100E8491C /* BSContact.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49641524A60C1100E8491C /* BSContact.m */; };
		CE4964A024A60C1100E8491C /* BSCalender.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49641624A60C1100E8491C /* BSCalender.m */; };
		CE4964A124A60C1100E8491C /* BSTransferResVC.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49641924A60C1100E8491C /* BSTransferResVC.m */; };
		************************ /* BSTransferPeersVC.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49642624A60C1100E8491C /* BSTransferPeersVC.m */; };
		CE4964A724A60C1100E8491C /* BSTransferPeersVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49642724A60C1100E8491C /* BSTransferPeersVC.xib */; };
		CE4964A824A60C1100E8491C /* BSHomeVC.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49642824A60C1100E8491C /* BSHomeVC.m */; };
		************************ /* BSTransferContactFileVC.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49642A24A60C1100E8491C /* BSTransferContactFileVC.m */; };
		CE4964AA24A60C1100E8491C /* BSConnTipsVC.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49642C24A60C1100E8491C /* BSConnTipsVC.m */; };
		************************ /* BSTransferMusicFileVC.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49642E24A60C1100E8491C /* BSTransferMusicFileVC.m */; };
		CE4964AC24A60C1100E8491C /* BSTransferMusicAlertVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49642F24A60C1100E8491C /* BSTransferMusicAlertVC.xib */; };
		CE4964AD24A60C1100E8491C /* BSConnTipsVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49643124A60C1100E8491C /* BSConnTipsVC.xib */; };
		CE4964AE24A60C1100E8491C /* BSTransferDeviceVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49643324A60C1100E8491C /* BSTransferDeviceVC.xib */; };
		CE4964AF24A60C1100E8491C /* BSTransferCalendarFileVC.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49643624A60C1100E8491C /* BSTransferCalendarFileVC.m */; };
		CE4964B024A60C1100E8491C /* BSTransferVC.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49643724A60C1100E8491C /* BSTransferVC.m */; };
		CE4964B124A60C1100E8491C /* BSTransferPhotoFileVC.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49643924A60C1100E8491C /* BSTransferPhotoFileVC.m */; };
		CE4964B224A60C1100E8491C /* BSTransferVideoFileVC.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49643B24A60C1100E8491C /* BSTransferVideoFileVC.m */; };
		CE4964B324A60C1100E8491C /* BSTransferDeviceVC.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49643D24A60C1100E8491C /* BSTransferDeviceVC.m */; };
		CE4964B424A60C1100E8491C /* BSTransferFileBaseVC.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49643F24A60C1100E8491C /* BSTransferFileBaseVC.m */; };
		CE4964B524A60C1100E8491C /* BSPhotoCCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49644424A60C1100E8491C /* BSPhotoCCell.xib */; };
		CE4964B624A60C1100E8491C /* BSPhotoCCell.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49644624A60C1100E8491C /* BSPhotoCCell.m */; };
		CE4964B724A60C1100E8491C /* BSVideoCCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49644724A60C1100E8491C /* BSVideoCCell.xib */; };
		************************ /* BSVideoCCell.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49644824A60C1100E8491C /* BSVideoCCell.m */; };
		CE4964B924A60C1100E8491C /* BSContactHeaderView.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49644A24A60C1100E8491C /* BSContactHeaderView.m */; };
		CE4964BA24A60C1100E8491C /* BSTransferEmptyView.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49644C24A60C1100E8491C /* BSTransferEmptyView.m */; };
		CE4964BB24A60C1100E8491C /* BSTransferEmptyView.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49644D24A60C1100E8491C /* BSTransferEmptyView.xib */; };
		CE4964BC24A60C1100E8491C /* BSTransferEmptyView.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49644E24A60C1100E8491C /* BSTransferEmptyView.xib */; };
		CE4964BD24A60C1100E8491C /* BSTransferPeerCell.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49645224A60C1100E8491C /* BSTransferPeerCell.m */; };
		CE4964BE24A60C1100E8491C /* BSCalendarCell.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49645324A60C1100E8491C /* BSCalendarCell.m */; };
		CE4964BF24A60C1100E8491C /* BSTransferPeerCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49645424A60C1100E8491C /* BSTransferPeerCell.xib */; };
		CE4964C024A60C1100E8491C /* BSResItemCell.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49645524A60C1100E8491C /* BSResItemCell.m */; };
		CE4964C124A60C1100E8491C /* BSResItemCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49645624A60C1100E8491C /* BSResItemCell.xib */; };
		CE4964C224A60C1100E8491C /* BSMusicCell.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49645724A60C1100E8491C /* BSMusicCell.m */; };
		CE4964C324A60C1100E8491C /* BSCalendarCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49645824A60C1100E8491C /* BSCalendarCell.xib */; };
		CE4964C424A60C1100E8491C /* BSMusicCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49645924A60C1100E8491C /* BSMusicCell.xib */; };
		CE4964C524A60C1100E8491C /* BSContactCell.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49645A24A60C1100E8491C /* BSContactCell.m */; };
		CE4964C624A60C1100E8491C /* BSContactCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49645B24A60C1100E8491C /* BSContactCell.xib */; };
		CE4964C724A60C1100E8491C /* BSAnimationView.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49646024A60C1100E8491C /* BSAnimationView.m */; };
		************************ /* BSAvatarView.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49646124A60C1100E8491C /* BSAvatarView.m */; };
		CE4964C924A60C1100E8491C /* BSAvatarView.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49646324A60C1100E8491C /* BSAvatarView.xib */; };
		CE4964CA24A60C1100E8491C /* BSContactHeaderView.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49646524A60C1100E8491C /* BSContactHeaderView.xib */; };
		CE4964D124A60C1100E8491C /* BSNetworkSpeedVC.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49647524A60C1100E8491C /* BSNetworkSpeedVC.m */; };
		CE4964D224A60C1100E8491C /* BSInstrumentDetailVC.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49647D24A60C1100E8491C /* BSInstrumentDetailVC.m */; };
		CE4964D324A60C1100E8491C /* BSNetworkSpeedRetVC.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49647F24A60C1100E8491C /* BSNetworkSpeedRetVC.m */; };
		CE4964D424A60C1100E8491C /* BSReceivQRCodeVC.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49648124A60C1100E8491C /* BSReceivQRCodeVC.m */; };
		CE4964D524A60C1100E8491C /* BSQRCodeScanningVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49648224A60C1100E8491C /* BSQRCodeScanningVC.xib */; };
		CE4964D624A60C1100E8491C /* BSReceivQRCodeVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49648424A60C1100E8491C /* BSReceivQRCodeVC.xib */; };
		CE4964D724A60C1100E8491C /* BSNetworkSpeedRetVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49648624A60C1100E8491C /* BSNetworkSpeedRetVC.xib */; };
		CE4964D824A60C1100E8491C /* BSNetworkSpeedVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49648824A60C1100E8491C /* BSNetworkSpeedVC.xib */; };
		CE4964D924A60C1100E8491C /* BSInstrumentVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49648A24A60C1100E8491C /* BSInstrumentVC.xib */; };
		CE4964DA24A60C1100E8491C /* BSDownloadQrcodeVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49648C24A60C1100E8491C /* BSDownloadQrcodeVC.xib */; };
		CE4964DB24A60C1100E8491C /* BSDownloadQrcodeVC.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49648F24A60C1100E8491C /* BSDownloadQrcodeVC.m */; };
		CE4964DC24A60C1100E8491C /* BSInstrumentVC.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49649124A60C1100E8491C /* BSInstrumentVC.m */; };
		CE4964DD24A60C1100E8491C /* BSQRCodeScanningVC.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49649224A60C1100E8491C /* BSQRCodeScanningVC.m */; };
		CE4964DE24A60C1100E8491C /* BSInsItemView.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49649724A60C1100E8491C /* BSInsItemView.m */; };
		************************ /* BSInstDetailCell.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49649824A60C1100E8491C /* BSInstDetailCell.m */; };
		CE4964E024A60C1100E8491C /* BSInsItemView.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49649924A60C1100E8491C /* BSInsItemView.xib */; };
		CE4964E124A60C1100E8491C /* BSClockDialView.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49649A24A60C1100E8491C /* BSClockDialView.m */; };
		CE4964E224A60C1100E8491C /* BSInstDetailCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE49649B24A60C1100E8491C /* BSInstDetailCell.xib */; };
		CE4964E324A60C5400E8491C /* BSTransferMusicAlertVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = CE4964E524A60C5400E8491C /* BSTransferMusicAlertVC.xib */; };
		CE4964FB24A60F1200E8491C /* MeasureNetTool.m in Sources */ = {isa = PBXBuildFile; fileRef = CE4964F924A60F1100E8491C /* MeasureNetTool.m */; };
		CE49650A24A60F5A00E8491C /* BSRetSendFileServer.m in Sources */ = {isa = PBXBuildFile; fileRef = CE4964FC24A60F5900E8491C /* BSRetSendFileServer.m */; };
		CE49650B24A60F5A00E8491C /* BSRetReciveFileServer.m in Sources */ = {isa = PBXBuildFile; fileRef = CE4964FF24A60F5900E8491C /* BSRetReciveFileServer.m */; };
		************************ /* BSAWSDynamoDBMgr.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49650124A60F5900E8491C /* BSAWSDynamoDBMgr.m */; };
		CE49650D24A60F5A00E8491C /* BSAWSS3Manager.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49650524A60F5900E8491C /* BSAWSS3Manager.m */; };
		CE49650E24A60F5A00E8491C /* BSSendFileManager.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49650624A60F5900E8491C /* BSSendFileManager.m */; };
		CE49650F24A60F5A00E8491C /* BSReciveFileManager.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49650824A60F5A00E8491C /* BSReciveFileManager.m */; };
		CE49651024A60F5A00E8491C /* BSUserDefaultManager.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49650924A60F5A00E8491C /* BSUserDefaultManager.m */; };
		CE49651324A60FA000E8491C /* NSData+UTF8.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49651224A60FA000E8491C /* NSData+UTF8.m */; };
		CE49651A24A612A000E8491C /* BSTransferMusicAlertVC.m in Sources */ = {isa = PBXBuildFile; fileRef = CE49651924A612A000E8491C /* BSTransferMusicAlertVC.m */; };
		D002C491249B589C00266381 /* BSWaitingTool.m in Sources */ = {isa = PBXBuildFile; fileRef = D002C490249B589C00266381 /* BSWaitingTool.m */; };
		D002C497249B61B300266381 /* BSAlertTool.m in Sources */ = {isa = PBXBuildFile; fileRef = D002C496249B61B300266381 /* BSAlertTool.m */; };
		D010EBD62491D26400C312E1 /* BSCloudStorageVC.m in Sources */ = {isa = PBXBuildFile; fileRef = D010EBCE2491D0EA00C312E1 /* BSCloudStorageVC.m */; };
		D0212D3024A05E2A000CE512 /* NSString+Predicate.m in Sources */ = {isa = PBXBuildFile; fileRef = D0212D2F24A05E2A000CE512 /* NSString+Predicate.m */; };
		D0212D3924A0A389000CE512 /* BSRegisterFinishVC.m in Sources */ = {isa = PBXBuildFile; fileRef = D0212D3724A0A389000CE512 /* BSRegisterFinishVC.m */; };
		D0212D3A24A0A389000CE512 /* BSRegisterFinishVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = D0212D3824A0A389000CE512 /* BSRegisterFinishVC.xib */; };
		D02208AD24A1F98E0019708C /* BSRegisterBaseVC.m in Sources */ = {isa = PBXBuildFile; fileRef = D02208AB24A1F98E0019708C /* BSRegisterBaseVC.m */; };
		D02208AE24A1F98E0019708C /* BSRegisterBaseVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = D02208AC24A1F98E0019708C /* BSRegisterBaseVC.xib */; };
		D02208BD24A21C470019708C /* BSGetBackPasswordVC.m in Sources */ = {isa = PBXBuildFile; fileRef = D02208BB24A21C470019708C /* BSGetBackPasswordVC.m */; };
		D02208BE24A21C470019708C /* BSGetBackPasswordVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = D02208BC24A21C470019708C /* BSGetBackPasswordVC.xib */; };
		D02208C324A21D710019708C /* BSVerifyPwdGuardVC.m in Sources */ = {isa = PBXBuildFile; fileRef = D02208C124A21D710019708C /* BSVerifyPwdGuardVC.m */; };
		D02208C424A21D710019708C /* BSVerifyPwdGuardVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = D02208C224A21D710019708C /* BSVerifyPwdGuardVC.xib */; };
		D02208C824A22E710019708C /* BSResetPasswordVC.m in Sources */ = {isa = PBXBuildFile; fileRef = D02208C624A22E710019708C /* BSResetPasswordVC.m */; };
		D02208C924A22E710019708C /* BSResetPasswordVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = D02208C724A22E710019708C /* BSResetPasswordVC.xib */; };
		D02208CD24A2408B0019708C /* BSResetPwdFinishVC.m in Sources */ = {isa = PBXBuildFile; fileRef = D02208CB24A2408B0019708C /* BSResetPwdFinishVC.m */; };
		D02208CE24A2408B0019708C /* BSResetPwdFinishVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = D02208CC24A2408B0019708C /* BSResetPwdFinishVC.xib */; };
		D02F84E1249533770040D1C3 /* BSEtDetailsView.m in Sources */ = {isa = PBXBuildFile; fileRef = D02F84E0249533770040D1C3 /* BSEtDetailsView.m */; };
		D02F84E724954D7E0040D1C3 /* BSEtWebUrlView.m in Sources */ = {isa = PBXBuildFile; fileRef = D02F84E624954D7E0040D1C3 /* BSEtWebUrlView.m */; };
		D02F84EA24955DBF0040D1C3 /* BSSubscribeRadio.m in Sources */ = {isa = PBXBuildFile; fileRef = D02F84E924955DBF0040D1C3 /* BSSubscribeRadio.m */; };
		D0417E0524A35B87009B5781 /* BSVIPRechargeVC.m in Sources */ = {isa = PBXBuildFile; fileRef = D0417E0324A35B87009B5781 /* BSVIPRechargeVC.m */; };
		D0417E0624A35B87009B5781 /* BSVIPRechargeVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = D0417E0424A35B87009B5781 /* BSVIPRechargeVC.xib */; };
		D0417E0B24A3646E009B5781 /* BSIconDetailsLabelView.m in Sources */ = {isa = PBXBuildFile; fileRef = D0417E0A24A3646E009B5781 /* BSIconDetailsLabelView.m */; };
		D0417E0E24A3695A009B5781 /* BSVipRadioView.m in Sources */ = {isa = PBXBuildFile; fileRef = D0417E0D24A3695A009B5781 /* BSVipRadioView.m */; };
		D04D3E3B24963D4E002C36BD /* BSEnterBaseController.m in Sources */ = {isa = PBXBuildFile; fileRef = D04D3E3924963D4E002C36BD /* BSEnterBaseController.m */; };
		D04D3E3C24963D4E002C36BD /* BSEnterBaseController.xib in Resources */ = {isa = PBXBuildFile; fileRef = D04D3E3A24963D4E002C36BD /* BSEnterBaseController.xib */; };
		D04D3E3F2496614E002C36BD /* UIView+BSAnimation.m in Sources */ = {isa = PBXBuildFile; fileRef = D04D3E3E2496614E002C36BD /* UIView+BSAnimation.m */; };
		D052DC482492139A00222ACB /* UIImage+Custom.m in Sources */ = {isa = PBXBuildFile; fileRef = D052DC472492139A00222ACB /* UIImage+Custom.m */; };
		D08BD25D24985ECF00590F7F /* BSCloudLoginVC.m in Sources */ = {isa = PBXBuildFile; fileRef = D08BD25B24985ECF00590F7F /* BSCloudLoginVC.m */; };
		D08BD25E24985ECF00590F7F /* BSCloudLoginVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = D08BD25C24985ECF00590F7F /* BSCloudLoginVC.xib */; };
		D08BD263249860F600590F7F /* BSCloudLoginHeadView.m in Sources */ = {isa = PBXBuildFile; fileRef = D08BD262249860F600590F7F /* BSCloudLoginHeadView.m */; };
		D08BD26724986A4400590F7F /* BSCloudEnterVC.m in Sources */ = {isa = PBXBuildFile; fileRef = D08BD26524986A4400590F7F /* BSCloudEnterVC.m */; };
		D08BD26824986A4400590F7F /* BSCloudEnterVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = D08BD26624986A4400590F7F /* BSCloudEnterVC.xib */; };
		D08BD26B2498992500590F7F /* BSLogoTitleView.m in Sources */ = {isa = PBXBuildFile; fileRef = D08BD26A2498992500590F7F /* BSLogoTitleView.m */; };
		D08BD2712498C7BD00590F7F /* BSLabelButtonView.m in Sources */ = {isa = PBXBuildFile; fileRef = D08BD2702498C7BD00590F7F /* BSLabelButtonView.m */; };
		D08BD2772498D55700590F7F /* BSCloudRegisterVC.m in Sources */ = {isa = PBXBuildFile; fileRef = D08BD2752498D55700590F7F /* BSCloudRegisterVC.m */; };
		D08BD2782498D55700590F7F /* BSCloudRegisterVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = D08BD2762498D55700590F7F /* BSCloudRegisterVC.xib */; };
		D099B0982491DCDB008CC521 /* BSTabBarItem.xib in Resources */ = {isa = PBXBuildFile; fileRef = D099B0912491DCDB008CC521 /* BSTabBarItem.xib */; };
		D099B0992491DCDB008CC521 /* BSTabBarItem.m in Sources */ = {isa = PBXBuildFile; fileRef = D099B0932491DCDB008CC521 /* BSTabBarItem.m */; };
		D099B09A2491DCDB008CC521 /* BSButton.m in Sources */ = {isa = PBXBuildFile; fileRef = D099B0942491DCDB008CC521 /* BSButton.m */; };
		D099B09B2491DCDB008CC521 /* BSView.m in Sources */ = {isa = PBXBuildFile; fileRef = D099B0952491DCDB008CC521 /* BSView.m */; };
		D099B09F2491DD4A008CC521 /* BSCloudStorageHeadView.m in Sources */ = {isa = PBXBuildFile; fileRef = D099B09E2491DD4A008CC521 /* BSCloudStorageHeadView.m */; };
		D099B0A22491DD82008CC521 /* BSCsNumLabel.m in Sources */ = {isa = PBXBuildFile; fileRef = D099B0A12491DD82008CC521 /* BSCsNumLabel.m */; };
		D099B0AB2491DF94008CC521 /* BSCsBodyCellView.m in Sources */ = {isa = PBXBuildFile; fileRef = D099B0AA2491DF94008CC521 /* BSCsBodyCellView.m */; };
		D0B2C42824935710000A83F8 /* BSLaunchScreenVC.m in Sources */ = {isa = PBXBuildFile; fileRef = D0B2C42624935710000A83F8 /* BSLaunchScreenVC.m */; };
		D0B2C42924935710000A83F8 /* BSLaunchScreenVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = D0B2C42724935710000A83F8 /* BSLaunchScreenVC.xib */; };
		D0B2C43524937112000A83F8 /* BSIntroduceView.m in Sources */ = {isa = PBXBuildFile; fileRef = D0B2C43424937112000A83F8 /* BSIntroduceView.m */; };
		D0B2C4382493893D000A83F8 /* BSWaitingAnimationView.m in Sources */ = {isa = PBXBuildFile; fileRef = D0B2C4372493893D000A83F8 /* BSWaitingAnimationView.m */; };
		D0B2C4402493A933000A83F8 /* BSAgreementView.m in Sources */ = {isa = PBXBuildFile; fileRef = D0B2C43F2493A933000A83F8 /* BSAgreementView.m */; };
		D0B2C4442493B22A000A83F8 /* BSEnterViewController01.m in Sources */ = {isa = PBXBuildFile; fileRef = D0B2C4422493B22A000A83F8 /* BSEnterViewController01.m */; };
		D0C4074F2499B0C700075843 /* UINavigationController+BSInitToTabBarChildController.m in Sources */ = {isa = PBXBuildFile; fileRef = D0C4074E2499B0C700075843 /* UINavigationController+BSInitToTabBarChildController.m */; };
		D0DEEEAC249C5064002E5B19 /* BSRegisterProgressView.m in Sources */ = {isa = PBXBuildFile; fileRef = D0DEEEAB249C5064002E5B19 /* BSRegisterProgressView.m */; };
		D0DEEEAF249CC8E8002E5B19 /* BSTextField.m in Sources */ = {isa = PBXBuildFile; fileRef = D0DEEEAE249CC8E8002E5B19 /* BSTextField.m */; };
		D0FAEE36249DE22A00DFA24D /* BSPwdGuardTextField.m in Sources */ = {isa = PBXBuildFile; fileRef = D0FAEE35249DE22A00DFA24D /* BSPwdGuardTextField.m */; };
		D0FAEE3B249E22C900DFA24D /* BSPasswordGuardVC.m in Sources */ = {isa = PBXBuildFile; fileRef = D0FAEE39249E22C900DFA24D /* BSPasswordGuardVC.m */; };
		D0FAEE3C249E22C900DFA24D /* BSPasswordGuardVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = D0FAEE3A249E22C900DFA24D /* BSPasswordGuardVC.xib */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0405864124AC12AD003B11AE /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		0C5832C4249F6FE40012BAB0 /* BSAccountModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSAccountModel.h; sourceTree = "<group>"; };
		0C5832C5249F6FE40012BAB0 /* BSAccountModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSAccountModel.m; sourceTree = "<group>"; };
		0DE47069D132F5E15EC57C98 /* Pods-PhoneCloneV2.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PhoneCloneV2.release.xcconfig"; path = "Target Support Files/Pods-PhoneCloneV2/Pods-PhoneCloneV2.release.xcconfig"; sourceTree = "<group>"; };
		11FDE9E1F75BEF04F832FB50 /* Pods-PhoneClone.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PhoneClone.release.xcconfig"; path = "Target Support Files/Pods-PhoneClone/Pods-PhoneClone.release.xcconfig"; sourceTree = "<group>"; };
		52E17375857294BA46281D28 /* Pods-PhoneClone.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PhoneClone.debug.xcconfig"; path = "Target Support Files/Pods-PhoneClone/Pods-PhoneClone.debug.xcconfig"; sourceTree = "<group>"; };
		6900953C8B1569EFE6C0752D /* Pods_PhoneCloneV2.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_PhoneCloneV2.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		A514D64F2B58C21300C207FC /* QRcodeView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QRcodeView.m; sourceTree = "<group>"; };
		A514D6502B58C21300C207FC /* QRcodeView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QRcodeView.h; sourceTree = "<group>"; };
		A514D6532B58C28700C207FC /* PopAnimationTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PopAnimationTool.m; sourceTree = "<group>"; };
		A514D6542B58C28700C207FC /* PopView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PopView.m; sourceTree = "<group>"; };
		A514D6552B58C28700C207FC /* PopAnimationTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PopAnimationTool.h; sourceTree = "<group>"; };
		A514D6562B58C28700C207FC /* PopView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PopView.h; sourceTree = "<group>"; };
		A52E870F2B576C8800708A0F /* startLinkViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = startLinkViewController.m; sourceTree = "<group>"; };
		A52E87102B576C8900708A0F /* startLinkViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = startLinkViewController.h; sourceTree = "<group>"; };
		A52E87142B576CBD00708A0F /* MySegmentedControl.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MySegmentedControl.m; sourceTree = "<group>"; };
		A52E87152B576CBD00708A0F /* MySegmentedControl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MySegmentedControl.h; sourceTree = "<group>"; };
		A52E87182B576FE700708A0F /* UILabel+createLabels.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UILabel+createLabels.h"; sourceTree = "<group>"; };
		A52E87192B576FE700708A0F /* UILabel+createLabels.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UILabel+createLabels.m"; sourceTree = "<group>"; };
		A52E871B2B57702200708A0F /* ZYELabel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZYELabel.m; sourceTree = "<group>"; };
		A52E871C2B57702200708A0F /* ZYELabel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZYELabel.h; sourceTree = "<group>"; };
		A52E871E2B57704300708A0F /* UIColor+Hex.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIColor+Hex.h"; sourceTree = "<group>"; };
		A52E871F2B57704300708A0F /* UIColor+Hex.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIColor+Hex.m"; sourceTree = "<group>"; };
		A52E87212B57711300708A0F /* UIButton+CenterImageAndTitle.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIButton+CenterImageAndTitle.m"; sourceTree = "<group>"; };
		A52E87222B57711300708A0F /* UIButton+Create.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIButton+Create.m"; sourceTree = "<group>"; };
		A52E87232B57711300708A0F /* UIButton+CenterImageAndTitle.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIButton+CenterImageAndTitle.h"; sourceTree = "<group>"; };
		A52E87242B57711300708A0F /* UIButton+Create.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIButton+Create.h"; sourceTree = "<group>"; };
		A52E87272B57716400708A0F /* ZYEButton.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZYEButton.h; sourceTree = "<group>"; };
		A52E87282B57716400708A0F /* ZYEButton.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZYEButton.m; sourceTree = "<group>"; };
		A5D274C02B54C5B800552859 /* BSConnectManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSConnectManager.h; sourceTree = "<group>"; };
		A5D274C12B54C5B800552859 /* BSConnectManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSConnectManager.m; sourceTree = "<group>"; };
		A5D274C32B54C5F400552859 /* tokenTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = tokenTool.h; sourceTree = "<group>"; };
		A5D274C42B54C5F400552859 /* tokenTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = tokenTool.m; sourceTree = "<group>"; };
		A5D274C62B55196D00552859 /* BSNewSendServer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSNewSendServer.h; sourceTree = "<group>"; };
		A5D274C72B55196D00552859 /* BSNewSendServer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSNewSendServer.m; sourceTree = "<group>"; };
		A5D5A39D2BAA7CBE00324468 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/BSTransferDeviceVC.strings"; sourceTree = "<group>"; };
		A5D5A39F2BAA7CD100324468 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/BSTransferDeviceVC.strings; sourceTree = "<group>"; };
		A5D7178D2B4F867800AE995C /* httpNework.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = httpNework.h; sourceTree = "<group>"; };
		A5D7178E2B4F867800AE995C /* httpNework.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = httpNework.m; sourceTree = "<group>"; };
		A5D717922B4F86B800AE995C /* MyHTTPConnection.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MyHTTPConnection.m; sourceTree = "<group>"; };
		A5D717932B4F86B800AE995C /* MyCustomDataResponse.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MyCustomDataResponse.h; sourceTree = "<group>"; };
		A5D717942B4F86B800AE995C /* MyHTTPResponse.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MyHTTPResponse.m; sourceTree = "<group>"; };
		A5D717952B4F86B800AE995C /* MyCustomDataResponse.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MyCustomDataResponse.m; sourceTree = "<group>"; };
		A5D717962B4F86B800AE995C /* MyHTTPConnection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MyHTTPConnection.h; sourceTree = "<group>"; };
		A5D717972B4F86B800AE995C /* MyHTTPResponse.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MyHTTPResponse.h; sourceTree = "<group>"; };
		A5D717992B4F86B800AE995C /* WebGelistManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = WebGelistManager.m; sourceTree = "<group>"; };
		A5D7179A2B4F86B800AE995C /* WebGelistManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = WebGelistManager.h; sourceTree = "<group>"; };
		A5D7179C2B4F86B800AE995C /* scannner.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = scannner.h; sourceTree = "<group>"; };
		A5D7179D2B4F86B800AE995C /* DHIPAdress.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DHIPAdress.h; sourceTree = "<group>"; };
		A5D7179E2B4F86B800AE995C /* NewScaner.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = NewScaner.m; sourceTree = "<group>"; };
		A5D7179F2B4F86B800AE995C /* scannner.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = scannner.m; sourceTree = "<group>"; };
		A5D717A02B4F86B800AE995C /* DHIPAdress.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DHIPAdress.m; sourceTree = "<group>"; };
		A5D717A12B4F86B800AE995C /* NewScaner.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = NewScaner.h; sourceTree = "<group>"; };
		A5D717A92B4F871A00AE995C /* PCObjcect.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PCObjcect.h; sourceTree = "<group>"; };
		A5D717AA2B4F871A00AE995C /* PCObjcect.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PCObjcect.m; sourceTree = "<group>"; };
		A5D717CF2B4F8A0F00AE995C /* FileManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FileManager.m; sourceTree = "<group>"; };
		A5D717D02B4F8A1000AE995C /* FileManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FileManager.h; sourceTree = "<group>"; };
		A5D717D42B4F8B5A00AE995C /* File.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = File.h; sourceTree = "<group>"; };
		A5D717D52B4F8B5A00AE995C /* File.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = File.m; sourceTree = "<group>"; };
		A5D717D72B4F8BCB00AE995C /* CalendarListEventInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CalendarListEventInfo.h; sourceTree = "<group>"; };
		A5D717D82B4F8BCB00AE995C /* CalendarListEventInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CalendarListEventInfo.m; sourceTree = "<group>"; };
		A5DCC9DD2B5F9D66004151F8 /* UIImageView+Create.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImageView+Create.h"; sourceTree = "<group>"; };
		A5DCC9DF2B5F9D66004151F8 /* MyAdManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MyAdManager.h; sourceTree = "<group>"; };
		A5DCC9E02B5F9D66004151F8 /* UpdateManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpdateManager.h; sourceTree = "<group>"; };
		A5DCC9E22B5F9D66004151F8 /* UIView+Extension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+Extension.h"; sourceTree = "<group>"; };
		A5DCC9E32B5F9D66004151F8 /* ZYEUpdateView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZYEUpdateView.h; sourceTree = "<group>"; };
		A5DCC9E62B5F9D66004151F8 /* UIImageView+Create.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+Create.m"; sourceTree = "<group>"; };
		A5DCC9EA2B5F9D66004151F8 /* MyAdManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MyAdManager.m; sourceTree = "<group>"; };
		A5DCC9EB2B5F9D66004151F8 /* UpdateManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpdateManager.m; sourceTree = "<group>"; };
		A5DCC9ED2B5F9D66004151F8 /* UIView+Extension.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+Extension.m"; sourceTree = "<group>"; };
		A5DCC9F02B5F9D66004151F8 /* ZYEUpdateView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZYEUpdateView.m; sourceTree = "<group>"; };
		A5DCC9F12B5F9D66004151F8 /* CTDConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CTDConfig.h; sourceTree = "<group>"; };
		A5E272C22B834B77008BF227 /* BSLinkView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSLinkView.h; sourceTree = "<group>"; };
		A5E272C32B834B77008BF227 /* BSLinkView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSLinkView.m; sourceTree = "<group>"; };
		A5E272C52B834E2D008BF227 /* BSLinkButton.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSLinkButton.h; sourceTree = "<group>"; };
		A5E272C62B834E2D008BF227 /* BSLinkButton.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSLinkButton.m; sourceTree = "<group>"; };
		A5E272C82B835328008BF227 /* UIView+cornerRadius.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+cornerRadius.m"; sourceTree = "<group>"; };
		A5E272C92B835328008BF227 /* UIView+cornerRadius.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+cornerRadius.h"; sourceTree = "<group>"; };
		C40BBF14243B2DAA00061B3D /* EventKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = EventKit.framework; path = System/Library/Frameworks/EventKit.framework; sourceTree = SDKROOT; };
		C40BBF16243B2DD700061B3D /* EventKitUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = EventKitUI.framework; path = System/Library/Frameworks/EventKitUI.framework; sourceTree = SDKROOT; };
		C41640392438A9CA0079BCB8 /* BSDeviceInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSDeviceInfo.h; sourceTree = "<group>"; };
		C416403A2438A9CA0079BCB8 /* BSDeviceInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSDeviceInfo.m; sourceTree = "<group>"; };
		C416403C2438ACE40079BCB8 /* UIDevice+Screen.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIDevice+Screen.h"; sourceTree = "<group>"; };
		C416403D2438ACE40079BCB8 /* UIDevice+Screen.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIDevice+Screen.m"; sourceTree = "<group>"; };
		C459F5FB243A27B000169E98 /* BSTransferFileProgressVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSTransferFileProgressVC.h; sourceTree = "<group>"; };
		C459F5FC243A27B000169E98 /* BSTransferFileProgressVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSTransferFileProgressVC.m; sourceTree = "<group>"; };
		C459F5FD243A27B000169E98 /* BSTransferFileProgressVC.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BSTransferFileProgressVC.xib; sourceTree = "<group>"; };
		C462B2F62434EEC0007EDF29 /* PhoneCloneV2.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = PhoneCloneV2.app; sourceTree = BUILT_PRODUCTS_DIR; };
		C462B2F92434EEC0007EDF29 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		C462B2FA2434EEC0007EDF29 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		C462B3022434EEC1007EDF29 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		C462B3052434EEC1007EDF29 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		C462B3072434EEC1007EDF29 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		C462B3082434EEC1007EDF29 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		C467ECB224388F7200FBC375 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		C467ECB424388F8200FBC375 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Localizable.strings"; sourceTree = "<group>"; };
		C480B2332434FB1D00637D3A /* PhoneCloneV2.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PhoneCloneV2.pch; sourceTree = "<group>"; };
		C480B2342434FB9F00637D3A /* WeakObjMacro.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WeakObjMacro.h; sourceTree = "<group>"; };
		C480B2352434FBB700637D3A /* UtilityMacro.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UtilityMacro.h; sourceTree = "<group>"; };
		C480B2362434FBCB00637D3A /* Macro.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Macro.h; sourceTree = "<group>"; };
		C480B23B2434FC2900637D3A /* BSBaseViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSBaseViewController.h; sourceTree = "<group>"; };
		C480B23C2434FC2900637D3A /* BSBaseViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSBaseViewController.m; sourceTree = "<group>"; };
		C480B23E2434FCE000637D3A /* BSNavigationController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSNavigationController.h; sourceTree = "<group>"; };
		C480B23F2434FCE000637D3A /* BSNavigationController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSNavigationController.m; sourceTree = "<group>"; };
		C498244F2438D172003C94B0 /* NSTimer+timerBlock.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSTimer+timerBlock.m"; sourceTree = "<group>"; };
		C49824502438D172003C94B0 /* ZZCircleProgress.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZZCircleProgress.h; sourceTree = "<group>"; };
		C49824512438D172003C94B0 /* NSTimer+timerBlock.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSTimer+timerBlock.h"; sourceTree = "<group>"; };
		C49824522438D172003C94B0 /* ZZCircleProgress.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZZCircleProgress.m; sourceTree = "<group>"; };
		C4A374E42439DA3A00FA82D9 /* BSPeerModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSPeerModel.h; sourceTree = "<group>"; };
		C4A374E52439DA3A00FA82D9 /* BSPeerModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSPeerModel.m; sourceTree = "<group>"; };
		C4CCF3F024388CB600A289A8 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/BSMeVC.strings"; sourceTree = "<group>"; };
		C4CCF3F124388CC400A289A8 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		C4D8E6B12438888900199E15 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/BSMeVC.xib; sourceTree = "<group>"; };
		C4D8E6B4243888B900199E15 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/BSMeVC.strings; sourceTree = "<group>"; };
		C4E8AB402436F136004B4331 /* BSTabBarController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSTabBarController.h; sourceTree = "<group>"; };
		C4E8AB412436F136004B4331 /* BSTabBarController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSTabBarController.m; sourceTree = "<group>"; };
		C4ECC66B24373AD2004C60C9 /* JXCategoryTitleVerticalZoomCellModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryTitleVerticalZoomCellModel.h; sourceTree = "<group>"; };
		C4ECC66C24373AD2004C60C9 /* JXCategoryTitleVerticalZoomCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryTitleVerticalZoomCell.h; sourceTree = "<group>"; };
		C4ECC66D24373AD2004C60C9 /* JXCategoryTitleVerticalZoomView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryTitleVerticalZoomView.h; sourceTree = "<group>"; };
		C4ECC66E24373AD2004C60C9 /* JXCategoryTitleVerticalZoomCellModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryTitleVerticalZoomCellModel.m; sourceTree = "<group>"; };
		C4ECC66F24373AD2004C60C9 /* JXCategoryTitleVerticalZoomCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryTitleVerticalZoomCell.m; sourceTree = "<group>"; };
		C4ECC67024373AD2004C60C9 /* JXCategoryTitleVerticalZoomView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryTitleVerticalZoomView.m; sourceTree = "<group>"; };
		C4ECC67224373AD2004C60C9 /* JXCategoryIndicatorCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryIndicatorCell.h; sourceTree = "<group>"; };
		C4ECC67324373AD2004C60C9 /* JXCategoryIndicatorCellModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryIndicatorCellModel.h; sourceTree = "<group>"; };
		C4ECC67424373AD2004C60C9 /* JXCategoryIndicatorView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryIndicatorView.h; sourceTree = "<group>"; };
		C4ECC67624373AD3004C60C9 /* JXCategoryIndicatorDotLineView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryIndicatorDotLineView.h; sourceTree = "<group>"; };
		C4ECC67724373AD3004C60C9 /* JXCategoryIndicatorLineView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryIndicatorLineView.m; sourceTree = "<group>"; };
		C4ECC67824373AD3004C60C9 /* JXCategoryIndicatorTriangleView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryIndicatorTriangleView.m; sourceTree = "<group>"; };
		C4ECC67924373AD3004C60C9 /* JXCategoryIndicatorRainbowLineView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryIndicatorRainbowLineView.m; sourceTree = "<group>"; };
		C4ECC67A24373AD3004C60C9 /* JXCategoryIndicatorBackgroundView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryIndicatorBackgroundView.h; sourceTree = "<group>"; };
		C4ECC67B24373AD3004C60C9 /* JXCategoryIndicatorImageView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryIndicatorImageView.m; sourceTree = "<group>"; };
		C4ECC67C24373AD3004C60C9 /* JXCategoryIndicatorBallView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryIndicatorBallView.m; sourceTree = "<group>"; };
		C4ECC67D24373AD3004C60C9 /* JXCategoryIndicatorComponentView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryIndicatorComponentView.m; sourceTree = "<group>"; };
		C4ECC67E24373AD3004C60C9 /* JXCategoryIndicatorLineView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryIndicatorLineView.h; sourceTree = "<group>"; };
		C4ECC67F24373AD3004C60C9 /* JXCategoryIndicatorDotLineView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryIndicatorDotLineView.m; sourceTree = "<group>"; };
		C4ECC68024373AD3004C60C9 /* JXCategoryIndicatorRainbowLineView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryIndicatorRainbowLineView.h; sourceTree = "<group>"; };
		C4ECC68124373AD3004C60C9 /* JXCategoryIndicatorTriangleView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryIndicatorTriangleView.h; sourceTree = "<group>"; };
		C4ECC68224373AD3004C60C9 /* JXCategoryIndicatorBallView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryIndicatorBallView.h; sourceTree = "<group>"; };
		C4ECC68324373AD3004C60C9 /* JXCategoryIndicatorImageView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryIndicatorImageView.h; sourceTree = "<group>"; };
		C4ECC68424373AD3004C60C9 /* JXCategoryIndicatorBackgroundView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryIndicatorBackgroundView.m; sourceTree = "<group>"; };
		C4ECC68524373AD3004C60C9 /* JXCategoryIndicatorComponentView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryIndicatorComponentView.h; sourceTree = "<group>"; };
		C4ECC68624373AD3004C60C9 /* JXCategoryIndicatorCellModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryIndicatorCellModel.m; sourceTree = "<group>"; };
		C4ECC68724373AD3004C60C9 /* JXCategoryIndicatorCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryIndicatorCell.m; sourceTree = "<group>"; };
		C4ECC68824373AD3004C60C9 /* JXCategoryIndicatorView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryIndicatorView.m; sourceTree = "<group>"; };
		C4ECC68A24373AD3004C60C9 /* JXCategoryDotCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryDotCell.m; sourceTree = "<group>"; };
		C4ECC68B24373AD3004C60C9 /* JXCategoryDotCellModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryDotCellModel.h; sourceTree = "<group>"; };
		C4ECC68C24373AD3004C60C9 /* JXCategoryDotView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryDotView.m; sourceTree = "<group>"; };
		C4ECC68D24373AD3004C60C9 /* JXCategoryDotCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryDotCell.h; sourceTree = "<group>"; };
		C4ECC68E24373AD3004C60C9 /* JXCategoryDotView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryDotView.h; sourceTree = "<group>"; };
		C4ECC68F24373AD3004C60C9 /* JXCategoryDotCellModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryDotCellModel.m; sourceTree = "<group>"; };
		C4ECC69124373AD3004C60C9 /* JXCategoryTitleImageView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryTitleImageView.m; sourceTree = "<group>"; };
		C4ECC69224373AD3004C60C9 /* JXCategoryTitleImageCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryTitleImageCell.m; sourceTree = "<group>"; };
		C4ECC69324373AD3004C60C9 /* JXCategoryTitleImageCellModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryTitleImageCellModel.h; sourceTree = "<group>"; };
		C4ECC69424373AD3004C60C9 /* JXCategoryTitleImageView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryTitleImageView.h; sourceTree = "<group>"; };
		C4ECC69524373AD3004C60C9 /* JXCategoryTitleImageCellModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryTitleImageCellModel.m; sourceTree = "<group>"; };
		C4ECC69624373AD3004C60C9 /* JXCategoryTitleImageCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryTitleImageCell.h; sourceTree = "<group>"; };
		C4ECC69824373AD3004C60C9 /* JXCategoryTitleCellModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryTitleCellModel.h; sourceTree = "<group>"; };
		C4ECC69924373AD3004C60C9 /* JXCategoryTitleCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryTitleCell.h; sourceTree = "<group>"; };
		C4ECC69A24373AD3004C60C9 /* JXCategoryTitleView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryTitleView.h; sourceTree = "<group>"; };
		C4ECC69B24373AD3004C60C9 /* JXCategoryTitleCellModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryTitleCellModel.m; sourceTree = "<group>"; };
		C4ECC69C24373AD3004C60C9 /* JXCategoryTitleCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryTitleCell.m; sourceTree = "<group>"; };
		C4ECC69D24373AD3004C60C9 /* JXCategoryTitleView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryTitleView.m; sourceTree = "<group>"; };
		C4ECC69E24373AD3004C60C9 /* JXCategoryView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryView.h; sourceTree = "<group>"; };
		C4ECC6A024373AD3004C60C9 /* JXCategoryImageView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryImageView.m; sourceTree = "<group>"; };
		C4ECC6A124373AD3004C60C9 /* JXCategoryImageCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryImageCell.m; sourceTree = "<group>"; };
		C4ECC6A224373AD3004C60C9 /* JXCategoryImageCellModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryImageCellModel.h; sourceTree = "<group>"; };
		C4ECC6A324373AD3004C60C9 /* JXCategoryImageView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryImageView.h; sourceTree = "<group>"; };
		C4ECC6A424373AD3004C60C9 /* JXCategoryImageCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryImageCell.h; sourceTree = "<group>"; };
		C4ECC6A524373AD3004C60C9 /* JXCategoryImageCellModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryImageCellModel.m; sourceTree = "<group>"; };
		C4ECC6A724373AD3004C60C9 /* JXCategoryNumberCellModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryNumberCellModel.h; sourceTree = "<group>"; };
		C4ECC6A824373AD3004C60C9 /* JXCategoryNumberView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryNumberView.m; sourceTree = "<group>"; };
		C4ECC6A924373AD3004C60C9 /* JXCategoryNumberCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryNumberCell.m; sourceTree = "<group>"; };
		C4ECC6AA24373AD3004C60C9 /* JXCategoryNumberCellModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryNumberCellModel.m; sourceTree = "<group>"; };
		C4ECC6AB24373AD3004C60C9 /* JXCategoryNumberView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryNumberView.h; sourceTree = "<group>"; };
		C4ECC6AC24373AD3004C60C9 /* JXCategoryNumberCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryNumberCell.h; sourceTree = "<group>"; };
		C4ECC6AE24373AD3004C60C9 /* JXCategoryListContainerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryListContainerView.m; sourceTree = "<group>"; };
		C4ECC6AF24373AD3004C60C9 /* JXCategoryViewAnimator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryViewAnimator.h; sourceTree = "<group>"; };
		C4ECC6B024373AD3004C60C9 /* JXCategoryIndicatorParamsModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryIndicatorParamsModel.h; sourceTree = "<group>"; };
		C4ECC6B124373AD3004C60C9 /* JXCategoryCollectionView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryCollectionView.h; sourceTree = "<group>"; };
		C4ECC6B224373AD3004C60C9 /* JXCategoryListCollectionContainerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryListCollectionContainerView.h; sourceTree = "<group>"; };
		C4ECC6B324373AD3004C60C9 /* JXCategoryIndicatorProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryIndicatorProtocol.h; sourceTree = "<group>"; };
		C4ECC6B424373AD3004C60C9 /* JXCategoryViewDefines.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryViewDefines.h; sourceTree = "<group>"; };
		C4ECC6B524373AD3004C60C9 /* JXCategoryFactory.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryFactory.m; sourceTree = "<group>"; };
		C4ECC6B624373AD3004C60C9 /* UIColor+JXAdd.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIColor+JXAdd.h"; sourceTree = "<group>"; };
		C4ECC6B724373AD3004C60C9 /* JXCategoryCollectionView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryCollectionView.m; sourceTree = "<group>"; };
		C4ECC6B824373AD3004C60C9 /* JXCategoryIndicatorParamsModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryIndicatorParamsModel.m; sourceTree = "<group>"; };
		C4ECC6B924373AD3004C60C9 /* JXCategoryViewAnimator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryViewAnimator.m; sourceTree = "<group>"; };
		C4ECC6BA24373AD3004C60C9 /* JXCategoryListContainerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryListContainerView.h; sourceTree = "<group>"; };
		C4ECC6BB24373AD3004C60C9 /* JXCategoryListCollectionContainerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryListCollectionContainerView.m; sourceTree = "<group>"; };
		C4ECC6BC24373AD3004C60C9 /* UIColor+JXAdd.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIColor+JXAdd.m"; sourceTree = "<group>"; };
		C4ECC6BD24373AD3004C60C9 /* JXCategoryFactory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryFactory.h; sourceTree = "<group>"; };
		C4ECC6BF24373AD3004C60C9 /* JXCategoryBaseView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryBaseView.h; sourceTree = "<group>"; };
		C4ECC6C024373AD3004C60C9 /* JXCategoryBaseCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryBaseCell.h; sourceTree = "<group>"; };
		C4ECC6C124373AD3004C60C9 /* JXCategoryBaseCellModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = JXCategoryBaseCellModel.h; sourceTree = "<group>"; };
		C4ECC6C224373AD3004C60C9 /* JXCategoryBaseView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryBaseView.m; sourceTree = "<group>"; };
		C4ECC6C324373AD3004C60C9 /* JXCategoryBaseCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryBaseCell.m; sourceTree = "<group>"; };
		C4ECC6C424373AD3004C60C9 /* JXCategoryBaseCellModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = JXCategoryBaseCellModel.m; sourceTree = "<group>"; };
		C4ECC6ED24373AE2004C60C9 /* NullSafe.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = NullSafe.m; sourceTree = "<group>"; };
		C4ECC6F024373AEE004C60C9 /* pinyin.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pinyin.h; sourceTree = "<group>"; };
		C4ECC6F124373AEE004C60C9 /* pinyin.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pinyin.c; sourceTree = "<group>"; };
		C4F8A4FD2437128E003FAB69 /* BSMeVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSMeVC.h; sourceTree = "<group>"; };
		C4F8A4FE2437128E003FAB69 /* BSMeVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSMeVC.m; sourceTree = "<group>"; };
		CE29773924A84983000861B2 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		CE29773B24A84984000861B2 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		CE29774124AA39C8000861B2 /* Reachability.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Reachability.m; sourceTree = "<group>"; };
		CE29774224AA39C8000861B2 /* StoreIAPManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = StoreIAPManager.h; sourceTree = "<group>"; };
		CE29774424AA39C8000861B2 /* StoreIAPObserver.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = StoreIAPObserver.m; sourceTree = "<group>"; };
		CE29774524AA39C8000861B2 /* ProgressViewUserInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ProgressViewUserInfo.m; sourceTree = "<group>"; };
		CE29774624AA39C8000861B2 /* NSString+Base64.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSString+Base64.m"; sourceTree = "<group>"; };
		CE29774724AA39C8000861B2 /* NSDictionary+Value.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSDictionary+Value.h"; sourceTree = "<group>"; };
		CE29774824AA39C8000861B2 /* StoreIAPManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = StoreIAPManager.m; sourceTree = "<group>"; };
		CE29774A24AA39C8000861B2 /* StoreIAPObserver.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = StoreIAPObserver.h; sourceTree = "<group>"; };
		CE29774B24AA39C8000861B2 /* ProgressViewUserInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ProgressViewUserInfo.h; sourceTree = "<group>"; };
		CE29774C24AA39C8000861B2 /* Reachability.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Reachability.h; sourceTree = "<group>"; };
		CE29774D24AA39C8000861B2 /* NSString+Base64.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+Base64.h"; sourceTree = "<group>"; };
		CE29774E24AA39C8000861B2 /* NSDictionary+Value.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSDictionary+Value.m"; sourceTree = "<group>"; };
		CE29775624AA3C23000861B2 /* IntroduceBackground01.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = IntroduceBackground01.jpg; sourceTree = "<group>"; };
		CE29775724AA3C23000861B2 /* AgreementBackground.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = AgreementBackground.jpg; sourceTree = "<group>"; };
		CE29775824AA3C23000861B2 /* LaunchScreen.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = LaunchScreen.jpg; sourceTree = "<group>"; };
		CE29775924AA3C23000861B2 /* IntroduceBackground02.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = IntroduceBackground02.jpg; sourceTree = "<group>"; };
		CE29775A24AA3C23000861B2 /* EnterViewBackground.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = EnterViewBackground.jpg; sourceTree = "<group>"; };
		CE49629B24A6065100E8491C /* SGQRCode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SGQRCode.h; sourceTree = "<group>"; };
		CE49629C24A6065100E8491C /* SGQRCode.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = SGQRCode.bundle; sourceTree = "<group>"; };
		CE49629E24A6065100E8491C /* UIImage+SGImageSize.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+SGImageSize.m"; sourceTree = "<group>"; };
		CE49629F24A6065100E8491C /* UIImage+SGImageSize.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+SGImageSize.h"; sourceTree = "<group>"; };
		CE4962A024A6065100E8491C /* SGQRCodeHelperTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SGQRCodeHelperTool.m; sourceTree = "<group>"; };
		CE4962A124A6065100E8491C /* SGQRCodeScanningView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SGQRCodeScanningView.m; sourceTree = "<group>"; };
		CE4962A224A6065100E8491C /* SGQRCodeScanManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SGQRCodeScanManager.m; sourceTree = "<group>"; };
		CE4962A324A6065100E8491C /* SGQRCodeGenerateManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SGQRCodeGenerateManager.m; sourceTree = "<group>"; };
		CE4962A424A6065100E8491C /* SGQRCodeAlbumManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SGQRCodeAlbumManager.h; sourceTree = "<group>"; };
		CE4962A524A6065100E8491C /* SGQRCodeHelperTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SGQRCodeHelperTool.h; sourceTree = "<group>"; };
		CE4962A624A6065100E8491C /* SGQRCodeScanningView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SGQRCodeScanningView.h; sourceTree = "<group>"; };
		CE4962A724A6065100E8491C /* SGQRCodeScanManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SGQRCodeScanManager.h; sourceTree = "<group>"; };
		CE4962A824A6065100E8491C /* SGQRCodeAlbumManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SGQRCodeAlbumManager.m; sourceTree = "<group>"; };
		CE4962A924A6065100E8491C /* SGQRCodeGenerateManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SGQRCodeGenerateManager.h; sourceTree = "<group>"; };
		CE4962B124A6085100E8491C /* BSTransferRes.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSTransferRes.m; sourceTree = "<group>"; };
		CE4962B224A6085100E8491C /* BSResGroupModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSResGroupModel.m; sourceTree = "<group>"; };
		CE4962B324A6085100E8491C /* BSResGroupModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSResGroupModel.h; sourceTree = "<group>"; };
		CE4962B424A6085100E8491C /* BSTransferRes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSTransferRes.h; sourceTree = "<group>"; };
		CE49641124A60C1100E8491C /* BSMusic.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSMusic.h; sourceTree = "<group>"; };
		CE49641224A60C1100E8491C /* BSCalender.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSCalender.h; sourceTree = "<group>"; };
		CE49641324A60C1100E8491C /* BSContact.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSContact.h; sourceTree = "<group>"; };
		CE49641424A60C1100E8491C /* BSMusic.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSMusic.m; sourceTree = "<group>"; };
		CE49641524A60C1100E8491C /* BSContact.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSContact.m; sourceTree = "<group>"; };
		CE49641624A60C1100E8491C /* BSCalender.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSCalender.m; sourceTree = "<group>"; };
		CE49641824A60C1100E8491C /* BSTransferPhotoFileVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSTransferPhotoFileVC.h; sourceTree = "<group>"; };
		CE49641924A60C1100E8491C /* BSTransferResVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSTransferResVC.m; sourceTree = "<group>"; };
		CE49642024A60C1100E8491C /* BSTransferVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSTransferVC.h; sourceTree = "<group>"; };
		CE49642124A60C1100E8491C /* BSTransferCalendarFileVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSTransferCalendarFileVC.h; sourceTree = "<group>"; };
		CE49642624A60C1100E8491C /* BSTransferPeersVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSTransferPeersVC.m; sourceTree = "<group>"; };
		CE49642724A60C1100E8491C /* BSTransferPeersVC.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BSTransferPeersVC.xib; sourceTree = "<group>"; };
		CE49642824A60C1100E8491C /* BSHomeVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSHomeVC.m; sourceTree = "<group>"; };
		CE49642924A60C1100E8491C /* BSTransferVideoFileVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSTransferVideoFileVC.h; sourceTree = "<group>"; };
		CE49642A24A60C1100E8491C /* BSTransferContactFileVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSTransferContactFileVC.m; sourceTree = "<group>"; };
		CE49642B24A60C1100E8491C /* BSTransferFileBaseVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSTransferFileBaseVC.h; sourceTree = "<group>"; };
		CE49642C24A60C1100E8491C /* BSConnTipsVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSConnTipsVC.m; sourceTree = "<group>"; };
		CE49642D24A60C1100E8491C /* BSTransferDeviceVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSTransferDeviceVC.h; sourceTree = "<group>"; };
		CE49642E24A60C1100E8491C /* BSTransferMusicFileVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSTransferMusicFileVC.m; sourceTree = "<group>"; };
		CE49643024A60C1100E8491C /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/BSTransferMusicAlertVC.xib; sourceTree = "<group>"; };
		CE49643224A60C1100E8491C /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/BSConnTipsVC.xib; sourceTree = "<group>"; };
		CE49643424A60C1100E8491C /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/BSTransferDeviceVC.xib; sourceTree = "<group>"; };
		CE49643524A60C1100E8491C /* BSTransferPeersVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSTransferPeersVC.h; sourceTree = "<group>"; };
		CE49643624A60C1100E8491C /* BSTransferCalendarFileVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSTransferCalendarFileVC.m; sourceTree = "<group>"; };
		CE49643724A60C1100E8491C /* BSTransferVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSTransferVC.m; sourceTree = "<group>"; };
		CE49643824A60C1100E8491C /* BSTransferResVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSTransferResVC.h; sourceTree = "<group>"; };
		CE49643924A60C1100E8491C /* BSTransferPhotoFileVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSTransferPhotoFileVC.m; sourceTree = "<group>"; };
		CE49643A24A60C1100E8491C /* BSTransferContactFileVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSTransferContactFileVC.h; sourceTree = "<group>"; };
		CE49643B24A60C1100E8491C /* BSTransferVideoFileVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSTransferVideoFileVC.m; sourceTree = "<group>"; };
		CE49643C24A60C1100E8491C /* BSHomeVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSHomeVC.h; sourceTree = "<group>"; };
		CE49643D24A60C1100E8491C /* BSTransferDeviceVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSTransferDeviceVC.m; sourceTree = "<group>"; };
		CE49643E24A60C1100E8491C /* BSConnTipsVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSConnTipsVC.h; sourceTree = "<group>"; };
		CE49643F24A60C1100E8491C /* BSTransferFileBaseVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSTransferFileBaseVC.m; sourceTree = "<group>"; };
		CE49644024A60C1100E8491C /* BSTransferMusicFileVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSTransferMusicFileVC.h; sourceTree = "<group>"; };
		CE49644324A60C1100E8491C /* BSPhotoCCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSPhotoCCell.h; sourceTree = "<group>"; };
		CE49644424A60C1100E8491C /* BSPhotoCCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BSPhotoCCell.xib; sourceTree = "<group>"; };
		CE49644524A60C1100E8491C /* BSVideoCCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSVideoCCell.h; sourceTree = "<group>"; };
		CE49644624A60C1100E8491C /* BSPhotoCCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSPhotoCCell.m; sourceTree = "<group>"; };
		CE49644724A60C1100E8491C /* BSVideoCCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BSVideoCCell.xib; sourceTree = "<group>"; };
		CE49644824A60C1100E8491C /* BSVideoCCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSVideoCCell.m; sourceTree = "<group>"; };
		CE49644924A60C1100E8491C /* BSAnimationView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSAnimationView.h; sourceTree = "<group>"; };
		CE49644A24A60C1100E8491C /* BSContactHeaderView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSContactHeaderView.m; sourceTree = "<group>"; };
		CE49644B24A60C1100E8491C /* BSAvatarView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSAvatarView.h; sourceTree = "<group>"; };
		CE49644C24A60C1100E8491C /* BSTransferEmptyView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSTransferEmptyView.m; sourceTree = "<group>"; };
		CE49644D24A60C1100E8491C /* BSTransferEmptyView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BSTransferEmptyView.xib; sourceTree = "<group>"; };
		CE49644F24A60C1100E8491C /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/BSTransferEmptyView.xib; sourceTree = "<group>"; };
		CE49645124A60C1100E8491C /* BSContactCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSContactCell.h; sourceTree = "<group>"; };
		CE49645224A60C1100E8491C /* BSTransferPeerCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSTransferPeerCell.m; sourceTree = "<group>"; };
		CE49645324A60C1100E8491C /* BSCalendarCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSCalendarCell.m; sourceTree = "<group>"; };
		CE49645424A60C1100E8491C /* BSTransferPeerCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BSTransferPeerCell.xib; sourceTree = "<group>"; };
		CE49645524A60C1100E8491C /* BSResItemCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSResItemCell.m; sourceTree = "<group>"; };
		CE49645624A60C1100E8491C /* BSResItemCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BSResItemCell.xib; sourceTree = "<group>"; };
		CE49645724A60C1100E8491C /* BSMusicCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSMusicCell.m; sourceTree = "<group>"; };
		CE49645824A60C1100E8491C /* BSCalendarCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BSCalendarCell.xib; sourceTree = "<group>"; };
		CE49645924A60C1100E8491C /* BSMusicCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BSMusicCell.xib; sourceTree = "<group>"; };
		CE49645A24A60C1100E8491C /* BSContactCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSContactCell.m; sourceTree = "<group>"; };
		CE49645B24A60C1100E8491C /* BSContactCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BSContactCell.xib; sourceTree = "<group>"; };
		CE49645C24A60C1100E8491C /* BSResItemCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSResItemCell.h; sourceTree = "<group>"; };
		CE49645D24A60C1100E8491C /* BSCalendarCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSCalendarCell.h; sourceTree = "<group>"; };
		CE49645E24A60C1100E8491C /* BSTransferPeerCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSTransferPeerCell.h; sourceTree = "<group>"; };
		CE49645F24A60C1100E8491C /* BSMusicCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSMusicCell.h; sourceTree = "<group>"; };
		CE49646024A60C1100E8491C /* BSAnimationView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSAnimationView.m; sourceTree = "<group>"; };
		CE49646124A60C1100E8491C /* BSAvatarView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSAvatarView.m; sourceTree = "<group>"; };
		CE49646224A60C1100E8491C /* BSContactHeaderView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSContactHeaderView.h; sourceTree = "<group>"; };
		CE49646324A60C1100E8491C /* BSAvatarView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BSAvatarView.xib; sourceTree = "<group>"; };
		CE49646424A60C1100E8491C /* BSTransferEmptyView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSTransferEmptyView.h; sourceTree = "<group>"; };
		CE49646524A60C1100E8491C /* BSContactHeaderView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BSContactHeaderView.xib; sourceTree = "<group>"; };
		CE49647524A60C1100E8491C /* BSNetworkSpeedVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSNetworkSpeedVC.m; sourceTree = "<group>"; };
		CE49647624A60C1100E8491C /* BSDownloadQrcodeVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSDownloadQrcodeVC.h; sourceTree = "<group>"; };
		CE49647D24A60C1100E8491C /* BSInstrumentDetailVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSInstrumentDetailVC.m; sourceTree = "<group>"; };
		CE49647E24A60C1100E8491C /* BSInstrumentVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSInstrumentVC.h; sourceTree = "<group>"; };
		CE49647F24A60C1100E8491C /* BSNetworkSpeedRetVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSNetworkSpeedRetVC.m; sourceTree = "<group>"; };
		CE49648024A60C1100E8491C /* BSQRCodeScanningVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSQRCodeScanningVC.h; sourceTree = "<group>"; };
		CE49648124A60C1100E8491C /* BSReceivQRCodeVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSReceivQRCodeVC.m; sourceTree = "<group>"; };
		CE49648324A60C1100E8491C /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/BSQRCodeScanningVC.xib; sourceTree = "<group>"; };
		CE49648524A60C1100E8491C /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/BSReceivQRCodeVC.xib; sourceTree = "<group>"; };
		CE49648724A60C1100E8491C /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/BSNetworkSpeedRetVC.xib; sourceTree = "<group>"; };
		CE49648924A60C1100E8491C /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/BSNetworkSpeedVC.xib; sourceTree = "<group>"; };
		CE49648B24A60C1100E8491C /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/BSInstrumentVC.xib; sourceTree = "<group>"; };
		CE49648D24A60C1100E8491C /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/BSDownloadQrcodeVC.xib; sourceTree = "<group>"; };
		CE49648E24A60C1100E8491C /* BSInstrumentDetailVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSInstrumentDetailVC.h; sourceTree = "<group>"; };
		CE49648F24A60C1100E8491C /* BSDownloadQrcodeVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSDownloadQrcodeVC.m; sourceTree = "<group>"; };
		CE49649024A60C1100E8491C /* BSNetworkSpeedVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSNetworkSpeedVC.h; sourceTree = "<group>"; };
		CE49649124A60C1100E8491C /* BSInstrumentVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSInstrumentVC.m; sourceTree = "<group>"; };
		CE49649224A60C1100E8491C /* BSQRCodeScanningVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSQRCodeScanningVC.m; sourceTree = "<group>"; };
		CE49649324A60C1100E8491C /* BSNetworkSpeedRetVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSNetworkSpeedRetVC.h; sourceTree = "<group>"; };
		CE49649424A60C1100E8491C /* BSReceivQRCodeVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSReceivQRCodeVC.h; sourceTree = "<group>"; };
		CE49649624A60C1100E8491C /* BSClockDialView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSClockDialView.h; sourceTree = "<group>"; };
		CE49649724A60C1100E8491C /* BSInsItemView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSInsItemView.m; sourceTree = "<group>"; };
		CE49649824A60C1100E8491C /* BSInstDetailCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSInstDetailCell.m; sourceTree = "<group>"; };
		CE49649924A60C1100E8491C /* BSInsItemView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BSInsItemView.xib; sourceTree = "<group>"; };
		CE49649A24A60C1100E8491C /* BSClockDialView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSClockDialView.m; sourceTree = "<group>"; };
		CE49649B24A60C1100E8491C /* BSInstDetailCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BSInstDetailCell.xib; sourceTree = "<group>"; };
		CE49649C24A60C1100E8491C /* BSInstDetailCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSInstDetailCell.h; sourceTree = "<group>"; };
		CE49649D24A60C1100E8491C /* BSInsItemView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSInsItemView.h; sourceTree = "<group>"; };
		CE4964E624A60C5E00E8491C /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/BSTransferMusicAlertVC.strings"; sourceTree = "<group>"; };
		CE4964E724A60C6100E8491C /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/BSTransferMusicAlertVC.xib; sourceTree = "<group>"; };
		CE4964E824A60CB400E8491C /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/BSTransferMusicAlertVC.strings; sourceTree = "<group>"; };
		CE4964EB24A60D7500E8491C /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/BSConnTipsVC.strings; sourceTree = "<group>"; };
		CE4964EC24A60D7700E8491C /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/BSConnTipsVC.strings"; sourceTree = "<group>"; };
		CE4964ED24A60DE200E8491C /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/BSQRCodeScanningVC.strings; sourceTree = "<group>"; };
		CE4964EE24A60DE400E8491C /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/BSQRCodeScanningVC.strings"; sourceTree = "<group>"; };
		CE4964EF24A60DE800E8491C /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/BSReceivQRCodeVC.strings; sourceTree = "<group>"; };
		CE4964F024A60DEA00E8491C /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/BSReceivQRCodeVC.strings"; sourceTree = "<group>"; };
		CE4964F124A60DF400E8491C /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/BSNetworkSpeedRetVC.strings; sourceTree = "<group>"; };
		CE4964F224A60DF800E8491C /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/BSNetworkSpeedRetVC.strings"; sourceTree = "<group>"; };
		CE4964F324A60DFD00E8491C /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/BSNetworkSpeedVC.strings; sourceTree = "<group>"; };
		CE4964F424A60DFF00E8491C /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/BSNetworkSpeedVC.strings"; sourceTree = "<group>"; };
		CE4964F524A60E0400E8491C /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/BSInstrumentVC.strings; sourceTree = "<group>"; };
		CE4964F624A60E0600E8491C /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/BSInstrumentVC.strings"; sourceTree = "<group>"; };
		CE4964F724A60E0A00E8491C /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/BSDownloadQrcodeVC.strings; sourceTree = "<group>"; };
		CE4964F824A60E0C00E8491C /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/BSDownloadQrcodeVC.strings"; sourceTree = "<group>"; };
		CE4964F924A60F1100E8491C /* MeasureNetTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MeasureNetTool.m; sourceTree = "<group>"; };
		CE4964FA24A60F1100E8491C /* MeasureNetTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MeasureNetTool.h; sourceTree = "<group>"; };
		CE4964FC24A60F5900E8491C /* BSRetSendFileServer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSRetSendFileServer.m; sourceTree = "<group>"; };
		CE4964FD24A60F5900E8491C /* BSSendFileManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSSendFileManager.h; sourceTree = "<group>"; };
		CE4964FE24A60F5900E8491C /* BSAWSDynamoDBMgr.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSAWSDynamoDBMgr.h; sourceTree = "<group>"; };
		CE4964FF24A60F5900E8491C /* BSRetReciveFileServer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSRetReciveFileServer.m; sourceTree = "<group>"; };
		CE49650024A60F5900E8491C /* BSRetSendFileServer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSRetSendFileServer.h; sourceTree = "<group>"; };
		CE49650124A60F5900E8491C /* BSAWSDynamoDBMgr.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSAWSDynamoDBMgr.m; sourceTree = "<group>"; };
		CE49650224A60F5900E8491C /* BSUserDefaultManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSUserDefaultManager.h; sourceTree = "<group>"; };
		CE49650324A60F5900E8491C /* BSReciveFileManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSReciveFileManager.h; sourceTree = "<group>"; };
		CE49650424A60F5900E8491C /* BSAWSS3Manager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSAWSS3Manager.h; sourceTree = "<group>"; };
		CE49650524A60F5900E8491C /* BSAWSS3Manager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSAWSS3Manager.m; sourceTree = "<group>"; };
		CE49650624A60F5900E8491C /* BSSendFileManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSSendFileManager.m; sourceTree = "<group>"; };
		CE49650724A60F5A00E8491C /* BSRetReciveFileServer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSRetReciveFileServer.h; sourceTree = "<group>"; };
		CE49650824A60F5A00E8491C /* BSReciveFileManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSReciveFileManager.m; sourceTree = "<group>"; };
		CE49650924A60F5A00E8491C /* BSUserDefaultManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSUserDefaultManager.m; sourceTree = "<group>"; };
		CE49651124A60FA000E8491C /* NSData+UTF8.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSData+UTF8.h"; sourceTree = "<group>"; };
		CE49651224A60FA000E8491C /* NSData+UTF8.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSData+UTF8.m"; sourceTree = "<group>"; };
		CE49651424A60FE600E8491C /* config.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = config.h; sourceTree = "<group>"; };
		CE49651824A612A000E8491C /* BSTransferMusicAlertVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSTransferMusicAlertVC.h; sourceTree = "<group>"; };
		CE49651924A612A000E8491C /* BSTransferMusicAlertVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSTransferMusicAlertVC.m; sourceTree = "<group>"; };
		D002C48F249B589C00266381 /* BSWaitingTool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSWaitingTool.h; sourceTree = "<group>"; };
		D002C490249B589C00266381 /* BSWaitingTool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSWaitingTool.m; sourceTree = "<group>"; };
		D002C495249B61B300266381 /* BSAlertTool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSAlertTool.h; sourceTree = "<group>"; };
		D002C496249B61B300266381 /* BSAlertTool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSAlertTool.m; sourceTree = "<group>"; };
		D010EBCE2491D0EA00C312E1 /* BSCloudStorageVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSCloudStorageVC.m; sourceTree = "<group>"; };
		D010EBCF2491D0EA00C312E1 /* BSCloudStorageVC.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BSCloudStorageVC.xib; sourceTree = "<group>"; };
		D010EBD02491D0EA00C312E1 /* BSCloudStorageVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSCloudStorageVC.h; sourceTree = "<group>"; };
		D0212D2E24A05E2A000CE512 /* NSString+Predicate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSString+Predicate.h"; sourceTree = "<group>"; };
		D0212D2F24A05E2A000CE512 /* NSString+Predicate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSString+Predicate.m"; sourceTree = "<group>"; };
		D0212D3624A0A389000CE512 /* BSRegisterFinishVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSRegisterFinishVC.h; sourceTree = "<group>"; };
		D0212D3724A0A389000CE512 /* BSRegisterFinishVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSRegisterFinishVC.m; sourceTree = "<group>"; };
		D0212D3824A0A389000CE512 /* BSRegisterFinishVC.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BSRegisterFinishVC.xib; sourceTree = "<group>"; };
		D02208AA24A1F98E0019708C /* BSRegisterBaseVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSRegisterBaseVC.h; sourceTree = "<group>"; };
		D02208AB24A1F98E0019708C /* BSRegisterBaseVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSRegisterBaseVC.m; sourceTree = "<group>"; };
		D02208AC24A1F98E0019708C /* BSRegisterBaseVC.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BSRegisterBaseVC.xib; sourceTree = "<group>"; };
		D02208BA24A21C470019708C /* BSGetBackPasswordVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSGetBackPasswordVC.h; sourceTree = "<group>"; };
		D02208BB24A21C470019708C /* BSGetBackPasswordVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSGetBackPasswordVC.m; sourceTree = "<group>"; };
		D02208BC24A21C470019708C /* BSGetBackPasswordVC.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BSGetBackPasswordVC.xib; sourceTree = "<group>"; };
		D02208C024A21D710019708C /* BSVerifyPwdGuardVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSVerifyPwdGuardVC.h; sourceTree = "<group>"; };
		D02208C124A21D710019708C /* BSVerifyPwdGuardVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSVerifyPwdGuardVC.m; sourceTree = "<group>"; };
		D02208C224A21D710019708C /* BSVerifyPwdGuardVC.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BSVerifyPwdGuardVC.xib; sourceTree = "<group>"; };
		D02208C524A22E710019708C /* BSResetPasswordVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSResetPasswordVC.h; sourceTree = "<group>"; };
		D02208C624A22E710019708C /* BSResetPasswordVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSResetPasswordVC.m; sourceTree = "<group>"; };
		D02208C724A22E710019708C /* BSResetPasswordVC.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BSResetPasswordVC.xib; sourceTree = "<group>"; };
		D02208CA24A2408B0019708C /* BSResetPwdFinishVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSResetPwdFinishVC.h; sourceTree = "<group>"; };
		D02208CB24A2408B0019708C /* BSResetPwdFinishVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSResetPwdFinishVC.m; sourceTree = "<group>"; };
		D02208CC24A2408B0019708C /* BSResetPwdFinishVC.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BSResetPwdFinishVC.xib; sourceTree = "<group>"; };
		D02F84DF249533770040D1C3 /* BSEtDetailsView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSEtDetailsView.h; sourceTree = "<group>"; };
		D02F84E0249533770040D1C3 /* BSEtDetailsView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSEtDetailsView.m; sourceTree = "<group>"; };
		D02F84E524954D7E0040D1C3 /* BSEtWebUrlView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSEtWebUrlView.h; sourceTree = "<group>"; };
		D02F84E624954D7E0040D1C3 /* BSEtWebUrlView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSEtWebUrlView.m; sourceTree = "<group>"; };
		D02F84E824955DBF0040D1C3 /* BSSubscribeRadio.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSSubscribeRadio.h; sourceTree = "<group>"; };
		D02F84E924955DBF0040D1C3 /* BSSubscribeRadio.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSSubscribeRadio.m; sourceTree = "<group>"; };
		D0417E0224A35B87009B5781 /* BSVIPRechargeVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSVIPRechargeVC.h; sourceTree = "<group>"; };
		D0417E0324A35B87009B5781 /* BSVIPRechargeVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSVIPRechargeVC.m; sourceTree = "<group>"; };
		D0417E0424A35B87009B5781 /* BSVIPRechargeVC.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BSVIPRechargeVC.xib; sourceTree = "<group>"; };
		D0417E0924A3646E009B5781 /* BSIconDetailsLabelView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSIconDetailsLabelView.h; sourceTree = "<group>"; };
		D0417E0A24A3646E009B5781 /* BSIconDetailsLabelView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSIconDetailsLabelView.m; sourceTree = "<group>"; };
		D0417E0C24A3695A009B5781 /* BSVipRadioView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSVipRadioView.h; sourceTree = "<group>"; };
		D0417E0D24A3695A009B5781 /* BSVipRadioView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSVipRadioView.m; sourceTree = "<group>"; };
		D04D3E3824963D4E002C36BD /* BSEnterBaseController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSEnterBaseController.h; sourceTree = "<group>"; };
		D04D3E3924963D4E002C36BD /* BSEnterBaseController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSEnterBaseController.m; sourceTree = "<group>"; };
		D04D3E3A24963D4E002C36BD /* BSEnterBaseController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BSEnterBaseController.xib; sourceTree = "<group>"; };
		D04D3E3D2496614E002C36BD /* UIView+BSAnimation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+BSAnimation.h"; sourceTree = "<group>"; };
		D04D3E3E2496614E002C36BD /* UIView+BSAnimation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+BSAnimation.m"; sourceTree = "<group>"; };
		D052DC462492139A00222ACB /* UIImage+Custom.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+Custom.h"; sourceTree = "<group>"; };
		D052DC472492139A00222ACB /* UIImage+Custom.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+Custom.m"; sourceTree = "<group>"; };
		D08BD25A24985ECF00590F7F /* BSCloudLoginVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSCloudLoginVC.h; sourceTree = "<group>"; };
		D08BD25B24985ECF00590F7F /* BSCloudLoginVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSCloudLoginVC.m; sourceTree = "<group>"; };
		D08BD25C24985ECF00590F7F /* BSCloudLoginVC.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BSCloudLoginVC.xib; sourceTree = "<group>"; };
		D08BD261249860F600590F7F /* BSCloudLoginHeadView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSCloudLoginHeadView.h; sourceTree = "<group>"; };
		D08BD262249860F600590F7F /* BSCloudLoginHeadView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSCloudLoginHeadView.m; sourceTree = "<group>"; };
		D08BD26424986A4400590F7F /* BSCloudEnterVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSCloudEnterVC.h; sourceTree = "<group>"; };
		D08BD26524986A4400590F7F /* BSCloudEnterVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSCloudEnterVC.m; sourceTree = "<group>"; };
		D08BD26624986A4400590F7F /* BSCloudEnterVC.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BSCloudEnterVC.xib; sourceTree = "<group>"; };
		D08BD2692498992500590F7F /* BSLogoTitleView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSLogoTitleView.h; sourceTree = "<group>"; };
		D08BD26A2498992500590F7F /* BSLogoTitleView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSLogoTitleView.m; sourceTree = "<group>"; };
		D08BD26F2498C7BD00590F7F /* BSLabelButtonView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSLabelButtonView.h; sourceTree = "<group>"; };
		D08BD2702498C7BD00590F7F /* BSLabelButtonView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSLabelButtonView.m; sourceTree = "<group>"; };
		D08BD2742498D55700590F7F /* BSCloudRegisterVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSCloudRegisterVC.h; sourceTree = "<group>"; };
		D08BD2752498D55700590F7F /* BSCloudRegisterVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSCloudRegisterVC.m; sourceTree = "<group>"; };
		D08BD2762498D55700590F7F /* BSCloudRegisterVC.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BSCloudRegisterVC.xib; sourceTree = "<group>"; };
		D099B0912491DCDB008CC521 /* BSTabBarItem.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BSTabBarItem.xib; sourceTree = "<group>"; };
		D099B0922491DCDB008CC521 /* BSView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSView.h; sourceTree = "<group>"; };
		D099B0932491DCDB008CC521 /* BSTabBarItem.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSTabBarItem.m; sourceTree = "<group>"; };
		D099B0942491DCDB008CC521 /* BSButton.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSButton.m; sourceTree = "<group>"; };
		D099B0952491DCDB008CC521 /* BSView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSView.m; sourceTree = "<group>"; };
		D099B0962491DCDB008CC521 /* BSTabBarItem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSTabBarItem.h; sourceTree = "<group>"; };
		D099B0972491DCDB008CC521 /* BSButton.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSButton.h; sourceTree = "<group>"; };
		D099B09D2491DD4A008CC521 /* BSCloudStorageHeadView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSCloudStorageHeadView.h; sourceTree = "<group>"; };
		D099B09E2491DD4A008CC521 /* BSCloudStorageHeadView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSCloudStorageHeadView.m; sourceTree = "<group>"; };
		D099B0A02491DD82008CC521 /* BSCsNumLabel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSCsNumLabel.h; sourceTree = "<group>"; };
		D099B0A12491DD82008CC521 /* BSCsNumLabel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSCsNumLabel.m; sourceTree = "<group>"; };
		D099B0A92491DF94008CC521 /* BSCsBodyCellView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSCsBodyCellView.h; sourceTree = "<group>"; };
		D099B0AA2491DF94008CC521 /* BSCsBodyCellView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSCsBodyCellView.m; sourceTree = "<group>"; };
		D0B2C42524935710000A83F8 /* BSLaunchScreenVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSLaunchScreenVC.h; sourceTree = "<group>"; };
		D0B2C42624935710000A83F8 /* BSLaunchScreenVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSLaunchScreenVC.m; sourceTree = "<group>"; };
		D0B2C42724935710000A83F8 /* BSLaunchScreenVC.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BSLaunchScreenVC.xib; sourceTree = "<group>"; };
		D0B2C43324937112000A83F8 /* BSIntroduceView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSIntroduceView.h; sourceTree = "<group>"; };
		D0B2C43424937112000A83F8 /* BSIntroduceView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSIntroduceView.m; sourceTree = "<group>"; };
		D0B2C4362493893D000A83F8 /* BSWaitingAnimationView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSWaitingAnimationView.h; sourceTree = "<group>"; };
		D0B2C4372493893D000A83F8 /* BSWaitingAnimationView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSWaitingAnimationView.m; sourceTree = "<group>"; };
		D0B2C43E2493A933000A83F8 /* BSAgreementView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSAgreementView.h; sourceTree = "<group>"; };
		D0B2C43F2493A933000A83F8 /* BSAgreementView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSAgreementView.m; sourceTree = "<group>"; };
		D0B2C4412493B22A000A83F8 /* BSEnterViewController01.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSEnterViewController01.h; sourceTree = "<group>"; };
		D0B2C4422493B22A000A83F8 /* BSEnterViewController01.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSEnterViewController01.m; sourceTree = "<group>"; };
		D0C4074D2499B0C700075843 /* UINavigationController+BSInitToTabBarChildController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UINavigationController+BSInitToTabBarChildController.h"; sourceTree = "<group>"; };
		D0C4074E2499B0C700075843 /* UINavigationController+BSInitToTabBarChildController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UINavigationController+BSInitToTabBarChildController.m"; sourceTree = "<group>"; };
		D0DEEEAA249C5064002E5B19 /* BSRegisterProgressView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSRegisterProgressView.h; sourceTree = "<group>"; };
		D0DEEEAB249C5064002E5B19 /* BSRegisterProgressView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSRegisterProgressView.m; sourceTree = "<group>"; };
		D0DEEEAD249CC8E8002E5B19 /* BSTextField.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSTextField.h; sourceTree = "<group>"; };
		D0DEEEAE249CC8E8002E5B19 /* BSTextField.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSTextField.m; sourceTree = "<group>"; };
		D0FAEE34249DE22A00DFA24D /* BSPwdGuardTextField.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSPwdGuardTextField.h; sourceTree = "<group>"; };
		D0FAEE35249DE22A00DFA24D /* BSPwdGuardTextField.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSPwdGuardTextField.m; sourceTree = "<group>"; };
		D0FAEE38249E22C900DFA24D /* BSPasswordGuardVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BSPasswordGuardVC.h; sourceTree = "<group>"; };
		D0FAEE39249E22C900DFA24D /* BSPasswordGuardVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BSPasswordGuardVC.m; sourceTree = "<group>"; };
		D0FAEE3A249E22C900DFA24D /* BSPasswordGuardVC.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BSPasswordGuardVC.xib; sourceTree = "<group>"; };
		F8FCB8A6B42BC1C6204AE900 /* Pods-PhoneCloneV2.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-PhoneCloneV2.debug.xcconfig"; path = "Target Support Files/Pods-PhoneCloneV2/Pods-PhoneCloneV2.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		C462B2F32434EEC0007EDF29 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C40BBF17243B2DD700061B3D /* EventKitUI.framework in Frameworks */,
				C40BBF15243B2DAA00061B3D /* EventKit.framework in Frameworks */,
				0405864224AC12AD003B11AE /* StoreKit.framework in Frameworks */,
				13F6B3D98508CF9E050A77F1 /* Pods_PhoneCloneV2.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		14B045B92C51EC055C88B97E /* Pods */ = {
			isa = PBXGroup;
			children = (
				52E17375857294BA46281D28 /* Pods-PhoneClone.debug.xcconfig */,
				11FDE9E1F75BEF04F832FB50 /* Pods-PhoneClone.release.xcconfig */,
				F8FCB8A6B42BC1C6204AE900 /* Pods-PhoneCloneV2.debug.xcconfig */,
				0DE47069D132F5E15EC57C98 /* Pods-PhoneCloneV2.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		58B105A07046A19FDFBD116D /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				0405864124AC12AD003B11AE /* StoreKit.framework */,
				C40BBF16243B2DD700061B3D /* EventKitUI.framework */,
				C40BBF14243B2DAA00061B3D /* EventKit.framework */,
				6900953C8B1569EFE6C0752D /* Pods_PhoneCloneV2.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		A514D6522B58C28700C207FC /* PopView */ = {
			isa = PBXGroup;
			children = (
				A514D6532B58C28700C207FC /* PopAnimationTool.m */,
				A514D6542B58C28700C207FC /* PopView.m */,
				A514D6552B58C28700C207FC /* PopAnimationTool.h */,
				A514D6562B58C28700C207FC /* PopView.h */,
			);
			path = PopView;
			sourceTree = "<group>";
		};
		A52E87122B576C8B00708A0F /* link */ = {
			isa = PBXGroup;
			children = (
				A5E272C92B835328008BF227 /* UIView+cornerRadius.h */,
				A5E272C82B835328008BF227 /* UIView+cornerRadius.m */,
				A5E272C52B834E2D008BF227 /* BSLinkButton.h */,
				A5E272C62B834E2D008BF227 /* BSLinkButton.m */,
				A52E87102B576C8900708A0F /* startLinkViewController.h */,
				A52E870F2B576C8800708A0F /* startLinkViewController.m */,
				A5E272C22B834B77008BF227 /* BSLinkView.h */,
				A5E272C32B834B77008BF227 /* BSLinkView.m */,
			);
			path = link;
			sourceTree = "<group>";
		};
		A52E87132B576CB200708A0F /* otherViews */ = {
			isa = PBXGroup;
			children = (
				A514D6522B58C28700C207FC /* PopView */,
				A514D6502B58C21300C207FC /* QRcodeView.h */,
				A514D64F2B58C21300C207FC /* QRcodeView.m */,
				A52E87272B57716400708A0F /* ZYEButton.h */,
				A52E87282B57716400708A0F /* ZYEButton.m */,
				A52E871C2B57702200708A0F /* ZYELabel.h */,
				A52E871B2B57702200708A0F /* ZYELabel.m */,
				A52E87152B576CBD00708A0F /* MySegmentedControl.h */,
				A52E87142B576CBD00708A0F /* MySegmentedControl.m */,
			);
			path = otherViews;
			sourceTree = "<group>";
		};
		A52E87172B576FD600708A0F /* categroy */ = {
			isa = PBXGroup;
			children = (
				A52E87232B57711300708A0F /* UIButton+CenterImageAndTitle.h */,
				A52E87212B57711300708A0F /* UIButton+CenterImageAndTitle.m */,
				A52E87242B57711300708A0F /* UIButton+Create.h */,
				A52E87222B57711300708A0F /* UIButton+Create.m */,
				A52E871E2B57704300708A0F /* UIColor+Hex.h */,
				A52E871F2B57704300708A0F /* UIColor+Hex.m */,
				A52E87182B576FE700708A0F /* UILabel+createLabels.h */,
				A52E87192B576FE700708A0F /* UILabel+createLabels.m */,
			);
			path = categroy;
			sourceTree = "<group>";
		};
		A5D7178C2B4F867800AE995C /* network */ = {
			isa = PBXGroup;
			children = (
				A5D7178D2B4F867800AE995C /* httpNework.h */,
				A5D7178E2B4F867800AE995C /* httpNework.m */,
				A5D274C02B54C5B800552859 /* BSConnectManager.h */,
				A5D274C12B54C5B800552859 /* BSConnectManager.m */,
			);
			path = network;
			sourceTree = "<group>";
		};
		A5D717902B4F86B800AE995C /* wifi */ = {
			isa = PBXGroup;
			children = (
				A5D717912B4F86B800AE995C /* connect */,
				A5D7179B2B4F86B800AE995C /* tool */,
			);
			path = wifi;
			sourceTree = "<group>";
		};
		A5D717912B4F86B800AE995C /* connect */ = {
			isa = PBXGroup;
			children = (
				A5D717932B4F86B800AE995C /* MyCustomDataResponse.h */,
				A5D717952B4F86B800AE995C /* MyCustomDataResponse.m */,
				A5D717962B4F86B800AE995C /* MyHTTPConnection.h */,
				A5D717922B4F86B800AE995C /* MyHTTPConnection.m */,
				A5D717972B4F86B800AE995C /* MyHTTPResponse.h */,
				A5D717942B4F86B800AE995C /* MyHTTPResponse.m */,
				A5D717982B4F86B800AE995C /* webNeed */,
			);
			path = connect;
			sourceTree = "<group>";
		};
		A5D717982B4F86B800AE995C /* webNeed */ = {
			isa = PBXGroup;
			children = (
				A5D717992B4F86B800AE995C /* WebGelistManager.m */,
				A5D7179A2B4F86B800AE995C /* WebGelistManager.h */,
			);
			path = webNeed;
			sourceTree = "<group>";
		};
		A5D7179B2B4F86B800AE995C /* tool */ = {
			isa = PBXGroup;
			children = (
				A5D7179D2B4F86B800AE995C /* DHIPAdress.h */,
				A5D717A02B4F86B800AE995C /* DHIPAdress.m */,
				A5D717A12B4F86B800AE995C /* NewScaner.h */,
				A5D7179E2B4F86B800AE995C /* NewScaner.m */,
				A5D7179C2B4F86B800AE995C /* scannner.h */,
				A5D7179F2B4F86B800AE995C /* scannner.m */,
			);
			path = tool;
			sourceTree = "<group>";
		};
		A5D717D22B4F8A2500AE995C /* core */ = {
			isa = PBXGroup;
			children = (
				A52E87172B576FD600708A0F /* categroy */,
				A5D274C32B54C5F400552859 /* tokenTool.h */,
				A5D274C42B54C5F400552859 /* tokenTool.m */,
				A5D717D32B4F8A3500AE995C /* manager */,
			);
			path = core;
			sourceTree = "<group>";
		};
		A5D717D32B4F8A3500AE995C /* manager */ = {
			isa = PBXGroup;
			children = (
				A5D717D72B4F8BCB00AE995C /* CalendarListEventInfo.h */,
				A5D717D82B4F8BCB00AE995C /* CalendarListEventInfo.m */,
				A5D717D02B4F8A1000AE995C /* FileManager.h */,
				A5D717CF2B4F8A0F00AE995C /* FileManager.m */,
			);
			path = manager;
			sourceTree = "<group>";
		};
		A5DCC9D92B5F9D66004151F8 /* update */ = {
			isa = PBXGroup;
			children = (
				A5DCC9F12B5F9D66004151F8 /* CTDConfig.h */,
				A5DCC9DF2B5F9D66004151F8 /* MyAdManager.h */,
				A5DCC9EA2B5F9D66004151F8 /* MyAdManager.m */,
				A5DCC9DD2B5F9D66004151F8 /* UIImageView+Create.h */,
				A5DCC9E62B5F9D66004151F8 /* UIImageView+Create.m */,
				A5DCC9E22B5F9D66004151F8 /* UIView+Extension.h */,
				A5DCC9ED2B5F9D66004151F8 /* UIView+Extension.m */,
				A5DCC9E02B5F9D66004151F8 /* UpdateManager.h */,
				A5DCC9EB2B5F9D66004151F8 /* UpdateManager.m */,
				A5DCC9E32B5F9D66004151F8 /* ZYEUpdateView.h */,
				A5DCC9F02B5F9D66004151F8 /* ZYEUpdateView.m */,
			);
			path = update;
			sourceTree = "<group>";
		};
		C462B2ED2434EEC0007EDF29 = {
			isa = PBXGroup;
			children = (
				C462B2F82434EEC0007EDF29 /* PhoneCloneV2 */,
				C462B2F72434EEC0007EDF29 /* Products */,
				14B045B92C51EC055C88B97E /* Pods */,
				58B105A07046A19FDFBD116D /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		C462B2F72434EEC0007EDF29 /* Products */ = {
			isa = PBXGroup;
			children = (
				C462B2F62434EEC0007EDF29 /* PhoneCloneV2.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		C462B2F82434EEC0007EDF29 /* PhoneCloneV2 */ = {
			isa = PBXGroup;
			children = (
				A5DCC9D92B5F9D66004151F8 /* update */,
				A52E87132B576CB200708A0F /* otherViews */,
				A5D717D22B4F8A2500AE995C /* core */,
				A5D717902B4F86B800AE995C /* wifi */,
				A5D7178C2B4F867800AE995C /* network */,
				C480B2322434FB0400637D3A /* Library */,
				C480B2242434FB0400637D3A /* Modules */,
				C480B2312434FB0400637D3A /* Resources */,
				C480B21E2434FB0400637D3A /* Util */,
				C480B21D2434EFAA00637D3A /* Supporting Files */,
				C462B2F92434EEC0007EDF29 /* AppDelegate.h */,
				C462B2FA2434EEC0007EDF29 /* AppDelegate.m */,
			);
			path = PhoneCloneV2;
			sourceTree = "<group>";
		};
		C480B21D2434EFAA00637D3A /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				C462B3022434EEC1007EDF29 /* Assets.xcassets */,
				C462B3042434EEC1007EDF29 /* LaunchScreen.storyboard */,
				C462B3072434EEC1007EDF29 /* Info.plist */,
				C462B3082434EEC1007EDF29 /* main.m */,
				C480B2332434FB1D00637D3A /* PhoneCloneV2.pch */,
				CE29773A24A84983000861B2 /* InfoPlist.strings */,
				C467ECB324388F7200FBC375 /* Localizable.strings */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		C480B21E2434FB0400637D3A /* Util */ = {
			isa = PBXGroup;
			children = (
				A5D717D42B4F8B5A00AE995C /* File.h */,
				A5D717D52B4F8B5A00AE995C /* File.m */,
				CE29774024AA39C8000861B2 /* StoreKitAPI */,
				D002C48E249B57F600266381 /* Tools */,
				C480B21F2434FB0400637D3A /* Network */,
				C480B2202434FB0400637D3A /* Manager */,
				C480B2212434FB0400637D3A /* Common */,
				C480B2222434FB0400637D3A /* Macro */,
				C480B2232434FB0400637D3A /* Catgory */,
				A5D717A92B4F871A00AE995C /* PCObjcect.h */,
				A5D717AA2B4F871A00AE995C /* PCObjcect.m */,
			);
			path = Util;
			sourceTree = "<group>";
		};
		C480B21F2434FB0400637D3A /* Network */ = {
			isa = PBXGroup;
			children = (
				CE4964FA24A60F1100E8491C /* MeasureNetTool.h */,
				CE4964F924A60F1100E8491C /* MeasureNetTool.m */,
			);
			path = Network;
			sourceTree = "<group>";
		};
		C480B2202434FB0400637D3A /* Manager */ = {
			isa = PBXGroup;
			children = (
				CE4964FE24A60F5900E8491C /* BSAWSDynamoDBMgr.h */,
				CE49650124A60F5900E8491C /* BSAWSDynamoDBMgr.m */,
				CE49650424A60F5900E8491C /* BSAWSS3Manager.h */,
				CE49650524A60F5900E8491C /* BSAWSS3Manager.m */,
				CE49650324A60F5900E8491C /* BSReciveFileManager.h */,
				CE49650824A60F5A00E8491C /* BSReciveFileManager.m */,
				CE49650724A60F5A00E8491C /* BSRetReciveFileServer.h */,
				CE4964FF24A60F5900E8491C /* BSRetReciveFileServer.m */,
				CE49650024A60F5900E8491C /* BSRetSendFileServer.h */,
				CE4964FC24A60F5900E8491C /* BSRetSendFileServer.m */,
				A5D274C62B55196D00552859 /* BSNewSendServer.h */,
				A5D274C72B55196D00552859 /* BSNewSendServer.m */,
				CE4964FD24A60F5900E8491C /* BSSendFileManager.h */,
				CE49650624A60F5900E8491C /* BSSendFileManager.m */,
				CE49650224A60F5900E8491C /* BSUserDefaultManager.h */,
				CE49650924A60F5A00E8491C /* BSUserDefaultManager.m */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		C480B2212434FB0400637D3A /* Common */ = {
			isa = PBXGroup;
			children = (
				C41640392438A9CA0079BCB8 /* BSDeviceInfo.h */,
				C416403A2438A9CA0079BCB8 /* BSDeviceInfo.m */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		C480B2222434FB0400637D3A /* Macro */ = {
			isa = PBXGroup;
			children = (
				CE49651424A60FE600E8491C /* config.h */,
				C480B2342434FB9F00637D3A /* WeakObjMacro.h */,
				C480B2352434FBB700637D3A /* UtilityMacro.h */,
				C480B2362434FBCB00637D3A /* Macro.h */,
			);
			path = Macro;
			sourceTree = "<group>";
		};
		C480B2232434FB0400637D3A /* Catgory */ = {
			isa = PBXGroup;
			children = (
				CE49651124A60FA000E8491C /* NSData+UTF8.h */,
				CE49651224A60FA000E8491C /* NSData+UTF8.m */,
				C416403C2438ACE40079BCB8 /* UIDevice+Screen.h */,
				C416403D2438ACE40079BCB8 /* UIDevice+Screen.m */,
				D052DC462492139A00222ACB /* UIImage+Custom.h */,
				D052DC472492139A00222ACB /* UIImage+Custom.m */,
				D04D3E3D2496614E002C36BD /* UIView+BSAnimation.h */,
				D04D3E3E2496614E002C36BD /* UIView+BSAnimation.m */,
				D0C4074D2499B0C700075843 /* UINavigationController+BSInitToTabBarChildController.h */,
				D0C4074E2499B0C700075843 /* UINavigationController+BSInitToTabBarChildController.m */,
				D0212D2E24A05E2A000CE512 /* NSString+Predicate.h */,
				D0212D2F24A05E2A000CE512 /* NSString+Predicate.m */,
			);
			path = Catgory;
			sourceTree = "<group>";
		};
		C480B2242434FB0400637D3A /* Modules */ = {
			isa = PBXGroup;
			children = (
				A52E87122B576C8B00708A0F /* link */,
				CE49640F24A60C1100E8491C /* Home */,
				CE49646624A60C1100E8491C /* Instrument */,
				D0B2C41F24935605000A83F8 /* LaunchScreen */,
				C480B2372434FC1D00637D3A /* Main */,
				D010EBCB2491D0EA00C312E1 /* CloudStorage */,
				C480B22D2434FB0400637D3A /* Me */,
			);
			path = Modules;
			sourceTree = "<group>";
		};
		C480B22D2434FB0400637D3A /* Me */ = {
			isa = PBXGroup;
			children = (
				C480B22E2434FB0400637D3A /* Models */,
				C480B22F2434FB0400637D3A /* Viewcontrollers */,
				C480B2302434FB0400637D3A /* Views */,
			);
			path = Me;
			sourceTree = "<group>";
		};
		C480B22E2434FB0400637D3A /* Models */ = {
			isa = PBXGroup;
			children = (
			);
			path = Models;
			sourceTree = "<group>";
		};
		C480B22F2434FB0400637D3A /* Viewcontrollers */ = {
			isa = PBXGroup;
			children = (
				C4F8A4FD2437128E003FAB69 /* BSMeVC.h */,
				C4F8A4FE2437128E003FAB69 /* BSMeVC.m */,
				C4D8E6B22438888900199E15 /* BSMeVC.xib */,
			);
			path = Viewcontrollers;
			sourceTree = "<group>";
		};
		C480B2302434FB0400637D3A /* Views */ = {
			isa = PBXGroup;
			children = (
			);
			path = Views;
			sourceTree = "<group>";
		};
		C480B2312434FB0400637D3A /* Resources */ = {
			isa = PBXGroup;
			children = (
				D0FAEE25249DD1BC00DFA24D /* Images */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		C480B2322434FB0400637D3A /* Library */ = {
			isa = PBXGroup;
			children = (
				CE49629A24A6065100E8491C /* SGQRCode */,
				C498244E2438D172003C94B0 /* ZZCircleProgress */,
				C4ECC6EF24373AEE004C60C9 /* Pinyin */,
				C4ECC6EC24373AE2004C60C9 /* NullSafe */,
				C4ECC66924373AD2004C60C9 /* JXCategoryView */,
			);
			path = Library;
			sourceTree = "<group>";
		};
		C480B2372434FC1D00637D3A /* Main */ = {
			isa = PBXGroup;
			children = (
				C480B2382434FC1D00637D3A /* Models */,
				C480B2392434FC1D00637D3A /* Viewcontrollers */,
				D099B0902491DCDB008CC521 /* Views */,
			);
			path = Main;
			sourceTree = "<group>";
		};
		C480B2382434FC1D00637D3A /* Models */ = {
			isa = PBXGroup;
			children = (
				CE4962B324A6085100E8491C /* BSResGroupModel.h */,
				CE4962B224A6085100E8491C /* BSResGroupModel.m */,
				CE4962B424A6085100E8491C /* BSTransferRes.h */,
				CE4962B124A6085100E8491C /* BSTransferRes.m */,
				C4A374E42439DA3A00FA82D9 /* BSPeerModel.h */,
				C4A374E52439DA3A00FA82D9 /* BSPeerModel.m */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		C480B2392434FC1D00637D3A /* Viewcontrollers */ = {
			isa = PBXGroup;
			children = (
				C480B23B2434FC2900637D3A /* BSBaseViewController.h */,
				C480B23C2434FC2900637D3A /* BSBaseViewController.m */,
				C480B23E2434FCE000637D3A /* BSNavigationController.h */,
				C480B23F2434FCE000637D3A /* BSNavigationController.m */,
				C4E8AB402436F136004B4331 /* BSTabBarController.h */,
				C4E8AB412436F136004B4331 /* BSTabBarController.m */,
				C459F5FB243A27B000169E98 /* BSTransferFileProgressVC.h */,
				C459F5FC243A27B000169E98 /* BSTransferFileProgressVC.m */,
				C459F5FD243A27B000169E98 /* BSTransferFileProgressVC.xib */,
			);
			path = Viewcontrollers;
			sourceTree = "<group>";
		};
		C498244E2438D172003C94B0 /* ZZCircleProgress */ = {
			isa = PBXGroup;
			children = (
				C498244F2438D172003C94B0 /* NSTimer+timerBlock.m */,
				C49824502438D172003C94B0 /* ZZCircleProgress.h */,
				C49824512438D172003C94B0 /* NSTimer+timerBlock.h */,
				C49824522438D172003C94B0 /* ZZCircleProgress.m */,
			);
			path = ZZCircleProgress;
			sourceTree = "<group>";
		};
		C4ECC66924373AD2004C60C9 /* JXCategoryView */ = {
			isa = PBXGroup;
			children = (
				C4ECC66A24373AD2004C60C9 /* VerticalZoomTitle */,
				C4ECC67124373AD2004C60C9 /* Indicator */,
				C4ECC68924373AD3004C60C9 /* Dot */,
				C4ECC69024373AD3004C60C9 /* TitleImage */,
				C4ECC69724373AD3004C60C9 /* Title */,
				C4ECC69E24373AD3004C60C9 /* JXCategoryView.h */,
				C4ECC69F24373AD3004C60C9 /* Image */,
				C4ECC6A624373AD3004C60C9 /* Number */,
				C4ECC6AD24373AD3004C60C9 /* Common */,
				C4ECC6BE24373AD3004C60C9 /* Base */,
			);
			path = JXCategoryView;
			sourceTree = "<group>";
		};
		C4ECC66A24373AD2004C60C9 /* VerticalZoomTitle */ = {
			isa = PBXGroup;
			children = (
				C4ECC66B24373AD2004C60C9 /* JXCategoryTitleVerticalZoomCellModel.h */,
				C4ECC66C24373AD2004C60C9 /* JXCategoryTitleVerticalZoomCell.h */,
				C4ECC66D24373AD2004C60C9 /* JXCategoryTitleVerticalZoomView.h */,
				C4ECC66E24373AD2004C60C9 /* JXCategoryTitleVerticalZoomCellModel.m */,
				C4ECC66F24373AD2004C60C9 /* JXCategoryTitleVerticalZoomCell.m */,
				C4ECC67024373AD2004C60C9 /* JXCategoryTitleVerticalZoomView.m */,
			);
			path = VerticalZoomTitle;
			sourceTree = "<group>";
		};
		C4ECC67124373AD2004C60C9 /* Indicator */ = {
			isa = PBXGroup;
			children = (
				C4ECC67224373AD2004C60C9 /* JXCategoryIndicatorCell.h */,
				C4ECC67324373AD2004C60C9 /* JXCategoryIndicatorCellModel.h */,
				C4ECC67424373AD2004C60C9 /* JXCategoryIndicatorView.h */,
				C4ECC67524373AD2004C60C9 /* IndicatorViews */,
				C4ECC68624373AD3004C60C9 /* JXCategoryIndicatorCellModel.m */,
				C4ECC68724373AD3004C60C9 /* JXCategoryIndicatorCell.m */,
				C4ECC68824373AD3004C60C9 /* JXCategoryIndicatorView.m */,
			);
			path = Indicator;
			sourceTree = "<group>";
		};
		C4ECC67524373AD2004C60C9 /* IndicatorViews */ = {
			isa = PBXGroup;
			children = (
				C4ECC67624373AD3004C60C9 /* JXCategoryIndicatorDotLineView.h */,
				C4ECC67724373AD3004C60C9 /* JXCategoryIndicatorLineView.m */,
				C4ECC67824373AD3004C60C9 /* JXCategoryIndicatorTriangleView.m */,
				C4ECC67924373AD3004C60C9 /* JXCategoryIndicatorRainbowLineView.m */,
				C4ECC67A24373AD3004C60C9 /* JXCategoryIndicatorBackgroundView.h */,
				C4ECC67B24373AD3004C60C9 /* JXCategoryIndicatorImageView.m */,
				C4ECC67C24373AD3004C60C9 /* JXCategoryIndicatorBallView.m */,
				C4ECC67D24373AD3004C60C9 /* JXCategoryIndicatorComponentView.m */,
				C4ECC67E24373AD3004C60C9 /* JXCategoryIndicatorLineView.h */,
				C4ECC67F24373AD3004C60C9 /* JXCategoryIndicatorDotLineView.m */,
				C4ECC68024373AD3004C60C9 /* JXCategoryIndicatorRainbowLineView.h */,
				C4ECC68124373AD3004C60C9 /* JXCategoryIndicatorTriangleView.h */,
				C4ECC68224373AD3004C60C9 /* JXCategoryIndicatorBallView.h */,
				C4ECC68324373AD3004C60C9 /* JXCategoryIndicatorImageView.h */,
				C4ECC68424373AD3004C60C9 /* JXCategoryIndicatorBackgroundView.m */,
				C4ECC68524373AD3004C60C9 /* JXCategoryIndicatorComponentView.h */,
			);
			path = IndicatorViews;
			sourceTree = "<group>";
		};
		C4ECC68924373AD3004C60C9 /* Dot */ = {
			isa = PBXGroup;
			children = (
				C4ECC68A24373AD3004C60C9 /* JXCategoryDotCell.m */,
				C4ECC68B24373AD3004C60C9 /* JXCategoryDotCellModel.h */,
				C4ECC68C24373AD3004C60C9 /* JXCategoryDotView.m */,
				C4ECC68D24373AD3004C60C9 /* JXCategoryDotCell.h */,
				C4ECC68E24373AD3004C60C9 /* JXCategoryDotView.h */,
				C4ECC68F24373AD3004C60C9 /* JXCategoryDotCellModel.m */,
			);
			path = Dot;
			sourceTree = "<group>";
		};
		C4ECC69024373AD3004C60C9 /* TitleImage */ = {
			isa = PBXGroup;
			children = (
				C4ECC69124373AD3004C60C9 /* JXCategoryTitleImageView.m */,
				C4ECC69224373AD3004C60C9 /* JXCategoryTitleImageCell.m */,
				C4ECC69324373AD3004C60C9 /* JXCategoryTitleImageCellModel.h */,
				C4ECC69424373AD3004C60C9 /* JXCategoryTitleImageView.h */,
				C4ECC69524373AD3004C60C9 /* JXCategoryTitleImageCellModel.m */,
				C4ECC69624373AD3004C60C9 /* JXCategoryTitleImageCell.h */,
			);
			path = TitleImage;
			sourceTree = "<group>";
		};
		C4ECC69724373AD3004C60C9 /* Title */ = {
			isa = PBXGroup;
			children = (
				C4ECC69824373AD3004C60C9 /* JXCategoryTitleCellModel.h */,
				C4ECC69924373AD3004C60C9 /* JXCategoryTitleCell.h */,
				C4ECC69A24373AD3004C60C9 /* JXCategoryTitleView.h */,
				C4ECC69B24373AD3004C60C9 /* JXCategoryTitleCellModel.m */,
				C4ECC69C24373AD3004C60C9 /* JXCategoryTitleCell.m */,
				C4ECC69D24373AD3004C60C9 /* JXCategoryTitleView.m */,
			);
			path = Title;
			sourceTree = "<group>";
		};
		C4ECC69F24373AD3004C60C9 /* Image */ = {
			isa = PBXGroup;
			children = (
				C4ECC6A024373AD3004C60C9 /* JXCategoryImageView.m */,
				C4ECC6A124373AD3004C60C9 /* JXCategoryImageCell.m */,
				C4ECC6A224373AD3004C60C9 /* JXCategoryImageCellModel.h */,
				C4ECC6A324373AD3004C60C9 /* JXCategoryImageView.h */,
				C4ECC6A424373AD3004C60C9 /* JXCategoryImageCell.h */,
				C4ECC6A524373AD3004C60C9 /* JXCategoryImageCellModel.m */,
			);
			path = Image;
			sourceTree = "<group>";
		};
		C4ECC6A624373AD3004C60C9 /* Number */ = {
			isa = PBXGroup;
			children = (
				C4ECC6A724373AD3004C60C9 /* JXCategoryNumberCellModel.h */,
				C4ECC6A824373AD3004C60C9 /* JXCategoryNumberView.m */,
				C4ECC6A924373AD3004C60C9 /* JXCategoryNumberCell.m */,
				C4ECC6AA24373AD3004C60C9 /* JXCategoryNumberCellModel.m */,
				C4ECC6AB24373AD3004C60C9 /* JXCategoryNumberView.h */,
				C4ECC6AC24373AD3004C60C9 /* JXCategoryNumberCell.h */,
			);
			path = Number;
			sourceTree = "<group>";
		};
		C4ECC6AD24373AD3004C60C9 /* Common */ = {
			isa = PBXGroup;
			children = (
				C4ECC6AE24373AD3004C60C9 /* JXCategoryListContainerView.m */,
				C4ECC6AF24373AD3004C60C9 /* JXCategoryViewAnimator.h */,
				C4ECC6B024373AD3004C60C9 /* JXCategoryIndicatorParamsModel.h */,
				C4ECC6B124373AD3004C60C9 /* JXCategoryCollectionView.h */,
				C4ECC6B224373AD3004C60C9 /* JXCategoryListCollectionContainerView.h */,
				C4ECC6B324373AD3004C60C9 /* JXCategoryIndicatorProtocol.h */,
				C4ECC6B424373AD3004C60C9 /* JXCategoryViewDefines.h */,
				C4ECC6B524373AD3004C60C9 /* JXCategoryFactory.m */,
				C4ECC6B624373AD3004C60C9 /* UIColor+JXAdd.h */,
				C4ECC6B724373AD3004C60C9 /* JXCategoryCollectionView.m */,
				C4ECC6B824373AD3004C60C9 /* JXCategoryIndicatorParamsModel.m */,
				C4ECC6B924373AD3004C60C9 /* JXCategoryViewAnimator.m */,
				C4ECC6BA24373AD3004C60C9 /* JXCategoryListContainerView.h */,
				C4ECC6BB24373AD3004C60C9 /* JXCategoryListCollectionContainerView.m */,
				C4ECC6BC24373AD3004C60C9 /* UIColor+JXAdd.m */,
				C4ECC6BD24373AD3004C60C9 /* JXCategoryFactory.h */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		C4ECC6BE24373AD3004C60C9 /* Base */ = {
			isa = PBXGroup;
			children = (
				C4ECC6BF24373AD3004C60C9 /* JXCategoryBaseView.h */,
				C4ECC6C024373AD3004C60C9 /* JXCategoryBaseCell.h */,
				C4ECC6C124373AD3004C60C9 /* JXCategoryBaseCellModel.h */,
				C4ECC6C224373AD3004C60C9 /* JXCategoryBaseView.m */,
				C4ECC6C324373AD3004C60C9 /* JXCategoryBaseCell.m */,
				C4ECC6C424373AD3004C60C9 /* JXCategoryBaseCellModel.m */,
			);
			path = Base;
			sourceTree = "<group>";
		};
		C4ECC6EC24373AE2004C60C9 /* NullSafe */ = {
			isa = PBXGroup;
			children = (
				C4ECC6ED24373AE2004C60C9 /* NullSafe.m */,
			);
			path = NullSafe;
			sourceTree = "<group>";
		};
		C4ECC6EF24373AEE004C60C9 /* Pinyin */ = {
			isa = PBXGroup;
			children = (
				C4ECC6F024373AEE004C60C9 /* pinyin.h */,
				C4ECC6F124373AEE004C60C9 /* pinyin.c */,
			);
			path = Pinyin;
			sourceTree = "<group>";
		};
		CE29774024AA39C8000861B2 /* StoreKitAPI */ = {
			isa = PBXGroup;
			children = (
				CE29774724AA39C8000861B2 /* NSDictionary+Value.h */,
				CE29774E24AA39C8000861B2 /* NSDictionary+Value.m */,
				CE29774D24AA39C8000861B2 /* NSString+Base64.h */,
				CE29774624AA39C8000861B2 /* NSString+Base64.m */,
				CE29774B24AA39C8000861B2 /* ProgressViewUserInfo.h */,
				CE29774524AA39C8000861B2 /* ProgressViewUserInfo.m */,
				CE29774C24AA39C8000861B2 /* Reachability.h */,
				CE29774124AA39C8000861B2 /* Reachability.m */,
				CE29774224AA39C8000861B2 /* StoreIAPManager.h */,
				CE29774824AA39C8000861B2 /* StoreIAPManager.m */,
				CE29774A24AA39C8000861B2 /* StoreIAPObserver.h */,
				CE29774424AA39C8000861B2 /* StoreIAPObserver.m */,
			);
			path = StoreKitAPI;
			sourceTree = "<group>";
		};
		CE49629A24A6065100E8491C /* SGQRCode */ = {
			isa = PBXGroup;
			children = (
				CE49629B24A6065100E8491C /* SGQRCode.h */,
				CE49629C24A6065100E8491C /* SGQRCode.bundle */,
				CE49629D24A6065100E8491C /* Category */,
				CE4962A024A6065100E8491C /* SGQRCodeHelperTool.m */,
				CE4962A124A6065100E8491C /* SGQRCodeScanningView.m */,
				CE4962A224A6065100E8491C /* SGQRCodeScanManager.m */,
				CE4962A324A6065100E8491C /* SGQRCodeGenerateManager.m */,
				CE4962A424A6065100E8491C /* SGQRCodeAlbumManager.h */,
				CE4962A524A6065100E8491C /* SGQRCodeHelperTool.h */,
				CE4962A624A6065100E8491C /* SGQRCodeScanningView.h */,
				CE4962A724A6065100E8491C /* SGQRCodeScanManager.h */,
				CE4962A824A6065100E8491C /* SGQRCodeAlbumManager.m */,
				CE4962A924A6065100E8491C /* SGQRCodeGenerateManager.h */,
			);
			path = SGQRCode;
			sourceTree = "<group>";
		};
		CE49629D24A6065100E8491C /* Category */ = {
			isa = PBXGroup;
			children = (
				CE49629E24A6065100E8491C /* UIImage+SGImageSize.m */,
				CE49629F24A6065100E8491C /* UIImage+SGImageSize.h */,
			);
			path = Category;
			sourceTree = "<group>";
		};
		CE49640F24A60C1100E8491C /* Home */ = {
			isa = PBXGroup;
			children = (
				CE49641024A60C1100E8491C /* Models */,
				CE49641724A60C1100E8491C /* Viewcontrollers */,
				CE49644124A60C1100E8491C /* Views */,
			);
			path = Home;
			sourceTree = "<group>";
		};
		CE49641024A60C1100E8491C /* Models */ = {
			isa = PBXGroup;
			children = (
				CE49641124A60C1100E8491C /* BSMusic.h */,
				CE49641224A60C1100E8491C /* BSCalender.h */,
				CE49641324A60C1100E8491C /* BSContact.h */,
				CE49641424A60C1100E8491C /* BSMusic.m */,
				CE49641524A60C1100E8491C /* BSContact.m */,
				CE49641624A60C1100E8491C /* BSCalender.m */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		CE49641724A60C1100E8491C /* Viewcontrollers */ = {
			isa = PBXGroup;
			children = (
				CE49643E24A60C1100E8491C /* BSConnTipsVC.h */,
				CE49642C24A60C1100E8491C /* BSConnTipsVC.m */,
				CE49643124A60C1100E8491C /* BSConnTipsVC.xib */,
				CE49643C24A60C1100E8491C /* BSHomeVC.h */,
				CE49642824A60C1100E8491C /* BSHomeVC.m */,
				CE49642124A60C1100E8491C /* BSTransferCalendarFileVC.h */,
				CE49643624A60C1100E8491C /* BSTransferCalendarFileVC.m */,
				CE49643A24A60C1100E8491C /* BSTransferContactFileVC.h */,
				CE49642A24A60C1100E8491C /* BSTransferContactFileVC.m */,
				CE49642D24A60C1100E8491C /* BSTransferDeviceVC.h */,
				CE49643D24A60C1100E8491C /* BSTransferDeviceVC.m */,
				CE49643324A60C1100E8491C /* BSTransferDeviceVC.xib */,
				CE49642B24A60C1100E8491C /* BSTransferFileBaseVC.h */,
				CE49643F24A60C1100E8491C /* BSTransferFileBaseVC.m */,
				CE49651824A612A000E8491C /* BSTransferMusicAlertVC.h */,
				CE49651924A612A000E8491C /* BSTransferMusicAlertVC.m */,
				CE4964E524A60C5400E8491C /* BSTransferMusicAlertVC.xib */,
				CE49642F24A60C1100E8491C /* BSTransferMusicAlertVC.xib */,
				CE49644024A60C1100E8491C /* BSTransferMusicFileVC.h */,
				CE49642E24A60C1100E8491C /* BSTransferMusicFileVC.m */,
				CE49643524A60C1100E8491C /* BSTransferPeersVC.h */,
				CE49642624A60C1100E8491C /* BSTransferPeersVC.m */,
				CE49642724A60C1100E8491C /* BSTransferPeersVC.xib */,
				CE49641824A60C1100E8491C /* BSTransferPhotoFileVC.h */,
				CE49643924A60C1100E8491C /* BSTransferPhotoFileVC.m */,
				CE49643824A60C1100E8491C /* BSTransferResVC.h */,
				CE49641924A60C1100E8491C /* BSTransferResVC.m */,
				CE49642024A60C1100E8491C /* BSTransferVC.h */,
				CE49643724A60C1100E8491C /* BSTransferVC.m */,
				CE49642924A60C1100E8491C /* BSTransferVideoFileVC.h */,
				CE49643B24A60C1100E8491C /* BSTransferVideoFileVC.m */,
			);
			path = Viewcontrollers;
			sourceTree = "<group>";
		};
		CE49644124A60C1100E8491C /* Views */ = {
			isa = PBXGroup;
			children = (
				CE49644224A60C1100E8491C /* CCell */,
				CE49644924A60C1100E8491C /* BSAnimationView.h */,
				CE49644A24A60C1100E8491C /* BSContactHeaderView.m */,
				CE49644B24A60C1100E8491C /* BSAvatarView.h */,
				CE49644C24A60C1100E8491C /* BSTransferEmptyView.m */,
				CE49644D24A60C1100E8491C /* BSTransferEmptyView.xib */,
				CE49644E24A60C1100E8491C /* BSTransferEmptyView.xib */,
				CE49645024A60C1100E8491C /* Cell */,
				CE49646024A60C1100E8491C /* BSAnimationView.m */,
				CE49646124A60C1100E8491C /* BSAvatarView.m */,
				CE49646224A60C1100E8491C /* BSContactHeaderView.h */,
				CE49646324A60C1100E8491C /* BSAvatarView.xib */,
				CE49646424A60C1100E8491C /* BSTransferEmptyView.h */,
				CE49646524A60C1100E8491C /* BSContactHeaderView.xib */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		CE49644224A60C1100E8491C /* CCell */ = {
			isa = PBXGroup;
			children = (
				CE49644324A60C1100E8491C /* BSPhotoCCell.h */,
				CE49644424A60C1100E8491C /* BSPhotoCCell.xib */,
				CE49644524A60C1100E8491C /* BSVideoCCell.h */,
				CE49644624A60C1100E8491C /* BSPhotoCCell.m */,
				CE49644724A60C1100E8491C /* BSVideoCCell.xib */,
				CE49644824A60C1100E8491C /* BSVideoCCell.m */,
			);
			path = CCell;
			sourceTree = "<group>";
		};
		CE49645024A60C1100E8491C /* Cell */ = {
			isa = PBXGroup;
			children = (
				CE49645124A60C1100E8491C /* BSContactCell.h */,
				CE49645224A60C1100E8491C /* BSTransferPeerCell.m */,
				CE49645324A60C1100E8491C /* BSCalendarCell.m */,
				CE49645424A60C1100E8491C /* BSTransferPeerCell.xib */,
				CE49645524A60C1100E8491C /* BSResItemCell.m */,
				CE49645624A60C1100E8491C /* BSResItemCell.xib */,
				CE49645724A60C1100E8491C /* BSMusicCell.m */,
				CE49645824A60C1100E8491C /* BSCalendarCell.xib */,
				CE49645924A60C1100E8491C /* BSMusicCell.xib */,
				CE49645A24A60C1100E8491C /* BSContactCell.m */,
				CE49645B24A60C1100E8491C /* BSContactCell.xib */,
				CE49645C24A60C1100E8491C /* BSResItemCell.h */,
				CE49645D24A60C1100E8491C /* BSCalendarCell.h */,
				CE49645E24A60C1100E8491C /* BSTransferPeerCell.h */,
				CE49645F24A60C1100E8491C /* BSMusicCell.h */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		CE49646624A60C1100E8491C /* Instrument */ = {
			isa = PBXGroup;
			children = (
				CE49646724A60C1100E8491C /* Models */,
				CE49646824A60C1100E8491C /* Viewcontrollers */,
				CE49649524A60C1100E8491C /* Views */,
			);
			path = Instrument;
			sourceTree = "<group>";
		};
		CE49646724A60C1100E8491C /* Models */ = {
			isa = PBXGroup;
			children = (
			);
			path = Models;
			sourceTree = "<group>";
		};
		CE49646824A60C1100E8491C /* Viewcontrollers */ = {
			isa = PBXGroup;
			children = (
				CE49647624A60C1100E8491C /* BSDownloadQrcodeVC.h */,
				CE49648F24A60C1100E8491C /* BSDownloadQrcodeVC.m */,
				CE49648C24A60C1100E8491C /* BSDownloadQrcodeVC.xib */,
				CE49648E24A60C1100E8491C /* BSInstrumentDetailVC.h */,
				CE49647D24A60C1100E8491C /* BSInstrumentDetailVC.m */,
				CE49647E24A60C1100E8491C /* BSInstrumentVC.h */,
				CE49649124A60C1100E8491C /* BSInstrumentVC.m */,
				CE49648A24A60C1100E8491C /* BSInstrumentVC.xib */,
				CE49649324A60C1100E8491C /* BSNetworkSpeedRetVC.h */,
				CE49647F24A60C1100E8491C /* BSNetworkSpeedRetVC.m */,
				CE49648624A60C1100E8491C /* BSNetworkSpeedRetVC.xib */,
				CE49649024A60C1100E8491C /* BSNetworkSpeedVC.h */,
				CE49647524A60C1100E8491C /* BSNetworkSpeedVC.m */,
				CE49648824A60C1100E8491C /* BSNetworkSpeedVC.xib */,
				CE49648024A60C1100E8491C /* BSQRCodeScanningVC.h */,
				CE49649224A60C1100E8491C /* BSQRCodeScanningVC.m */,
				CE49648224A60C1100E8491C /* BSQRCodeScanningVC.xib */,
				CE49649424A60C1100E8491C /* BSReceivQRCodeVC.h */,
				CE49648124A60C1100E8491C /* BSReceivQRCodeVC.m */,
				CE49648424A60C1100E8491C /* BSReceivQRCodeVC.xib */,
			);
			path = Viewcontrollers;
			sourceTree = "<group>";
		};
		CE49649524A60C1100E8491C /* Views */ = {
			isa = PBXGroup;
			children = (
				CE49649624A60C1100E8491C /* BSClockDialView.h */,
				CE49649724A60C1100E8491C /* BSInsItemView.m */,
				CE49649824A60C1100E8491C /* BSInstDetailCell.m */,
				CE49649924A60C1100E8491C /* BSInsItemView.xib */,
				CE49649A24A60C1100E8491C /* BSClockDialView.m */,
				CE49649B24A60C1100E8491C /* BSInstDetailCell.xib */,
				CE49649C24A60C1100E8491C /* BSInstDetailCell.h */,
				CE49649D24A60C1100E8491C /* BSInsItemView.h */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		D002C48E249B57F600266381 /* Tools */ = {
			isa = PBXGroup;
			children = (
				D002C48F249B589C00266381 /* BSWaitingTool.h */,
				D002C490249B589C00266381 /* BSWaitingTool.m */,
				D002C495249B61B300266381 /* BSAlertTool.h */,
				D002C496249B61B300266381 /* BSAlertTool.m */,
			);
			path = Tools;
			sourceTree = "<group>";
		};
		D010EBCB2491D0EA00C312E1 /* CloudStorage */ = {
			isa = PBXGroup;
			children = (
				D010EBCC2491D0EA00C312E1 /* Models */,
				D010EBCD2491D0EA00C312E1 /* Viewcontrollers */,
				D099B09C2491DD0E008CC521 /* Views */,
			);
			path = CloudStorage;
			sourceTree = "<group>";
		};
		D010EBCC2491D0EA00C312E1 /* Models */ = {
			isa = PBXGroup;
			children = (
				0C5832C4249F6FE40012BAB0 /* BSAccountModel.h */,
				0C5832C5249F6FE40012BAB0 /* BSAccountModel.m */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		D010EBCD2491D0EA00C312E1 /* Viewcontrollers */ = {
			isa = PBXGroup;
			children = (
				D0417E0724A35B8F009B5781 /* BSVIPRechargeVC */,
				D02208BF24A21D040019708C /* BSGetBackPasswordVC */,
				D08BD2732498D47C00590F7F /* BSCloudStorageVC */,
				D08BD2722498D46F00590F7F /* BSCloudLoginVC */,
				D0FAEE37249E21D900DFA24D /* BSCloudRegisterVC */,
				D08BD26424986A4400590F7F /* BSCloudEnterVC.h */,
				D08BD26524986A4400590F7F /* BSCloudEnterVC.m */,
				D08BD26624986A4400590F7F /* BSCloudEnterVC.xib */,
				D02208AA24A1F98E0019708C /* BSRegisterBaseVC.h */,
				D02208AB24A1F98E0019708C /* BSRegisterBaseVC.m */,
				D02208AC24A1F98E0019708C /* BSRegisterBaseVC.xib */,
			);
			path = Viewcontrollers;
			sourceTree = "<group>";
		};
		D02208BF24A21D040019708C /* BSGetBackPasswordVC */ = {
			isa = PBXGroup;
			children = (
				D02208BA24A21C470019708C /* BSGetBackPasswordVC.h */,
				D02208BB24A21C470019708C /* BSGetBackPasswordVC.m */,
				D02208BC24A21C470019708C /* BSGetBackPasswordVC.xib */,
				D02208C024A21D710019708C /* BSVerifyPwdGuardVC.h */,
				D02208C124A21D710019708C /* BSVerifyPwdGuardVC.m */,
				D02208C224A21D710019708C /* BSVerifyPwdGuardVC.xib */,
				D02208C524A22E710019708C /* BSResetPasswordVC.h */,
				D02208C624A22E710019708C /* BSResetPasswordVC.m */,
				D02208C724A22E710019708C /* BSResetPasswordVC.xib */,
				D02208CA24A2408B0019708C /* BSResetPwdFinishVC.h */,
				D02208CB24A2408B0019708C /* BSResetPwdFinishVC.m */,
				D02208CC24A2408B0019708C /* BSResetPwdFinishVC.xib */,
			);
			path = BSGetBackPasswordVC;
			sourceTree = "<group>";
		};
		D0417E0724A35B8F009B5781 /* BSVIPRechargeVC */ = {
			isa = PBXGroup;
			children = (
				D0417E0224A35B87009B5781 /* BSVIPRechargeVC.h */,
				D0417E0324A35B87009B5781 /* BSVIPRechargeVC.m */,
				D0417E0424A35B87009B5781 /* BSVIPRechargeVC.xib */,
			);
			path = BSVIPRechargeVC;
			sourceTree = "<group>";
		};
		D0417E0824A3641E009B5781 /* BSVIPRechargeVC */ = {
			isa = PBXGroup;
			children = (
				D0417E0924A3646E009B5781 /* BSIconDetailsLabelView.h */,
				D0417E0A24A3646E009B5781 /* BSIconDetailsLabelView.m */,
				D0417E0C24A3695A009B5781 /* BSVipRadioView.h */,
				D0417E0D24A3695A009B5781 /* BSVipRadioView.m */,
			);
			path = BSVIPRechargeVC;
			sourceTree = "<group>";
		};
		D08BD25F24985F2200590F7F /* BSCloudStorageVC */ = {
			isa = PBXGroup;
			children = (
				D099B09D2491DD4A008CC521 /* BSCloudStorageHeadView.h */,
				D099B09E2491DD4A008CC521 /* BSCloudStorageHeadView.m */,
				D099B0A02491DD82008CC521 /* BSCsNumLabel.h */,
				D099B0A12491DD82008CC521 /* BSCsNumLabel.m */,
				D099B0A92491DF94008CC521 /* BSCsBodyCellView.h */,
				D099B0AA2491DF94008CC521 /* BSCsBodyCellView.m */,
			);
			path = BSCloudStorageVC;
			sourceTree = "<group>";
		};
		D08BD26024985F7300590F7F /* BSCloudLoginVC */ = {
			isa = PBXGroup;
			children = (
				D08BD261249860F600590F7F /* BSCloudLoginHeadView.h */,
				D08BD262249860F600590F7F /* BSCloudLoginHeadView.m */,
				D08BD2692498992500590F7F /* BSLogoTitleView.h */,
				D08BD26A2498992500590F7F /* BSLogoTitleView.m */,
				D08BD26F2498C7BD00590F7F /* BSLabelButtonView.h */,
				D08BD2702498C7BD00590F7F /* BSLabelButtonView.m */,
			);
			path = BSCloudLoginVC;
			sourceTree = "<group>";
		};
		D08BD2722498D46F00590F7F /* BSCloudLoginVC */ = {
			isa = PBXGroup;
			children = (
				D08BD25A24985ECF00590F7F /* BSCloudLoginVC.h */,
				D08BD25B24985ECF00590F7F /* BSCloudLoginVC.m */,
				D08BD25C24985ECF00590F7F /* BSCloudLoginVC.xib */,
			);
			path = BSCloudLoginVC;
			sourceTree = "<group>";
		};
		D08BD2732498D47C00590F7F /* BSCloudStorageVC */ = {
			isa = PBXGroup;
			children = (
				D010EBD02491D0EA00C312E1 /* BSCloudStorageVC.h */,
				D010EBCE2491D0EA00C312E1 /* BSCloudStorageVC.m */,
				D010EBCF2491D0EA00C312E1 /* BSCloudStorageVC.xib */,
			);
			path = BSCloudStorageVC;
			sourceTree = "<group>";
		};
		D099B0902491DCDB008CC521 /* Views */ = {
			isa = PBXGroup;
			children = (
				D099B0912491DCDB008CC521 /* BSTabBarItem.xib */,
				D099B0922491DCDB008CC521 /* BSView.h */,
				D099B0932491DCDB008CC521 /* BSTabBarItem.m */,
				D099B0942491DCDB008CC521 /* BSButton.m */,
				D099B0952491DCDB008CC521 /* BSView.m */,
				D099B0962491DCDB008CC521 /* BSTabBarItem.h */,
				D099B0972491DCDB008CC521 /* BSButton.h */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		D099B09C2491DD0E008CC521 /* Views */ = {
			isa = PBXGroup;
			children = (
				D0417E0824A3641E009B5781 /* BSVIPRechargeVC */,
				D08BD25F24985F2200590F7F /* BSCloudStorageVC */,
				D08BD26024985F7300590F7F /* BSCloudLoginVC */,
				D0DEEEA3249C4E19002E5B19 /* BSCloudRegisterVC */,
				D0DEEEAD249CC8E8002E5B19 /* BSTextField.h */,
				D0DEEEAE249CC8E8002E5B19 /* BSTextField.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		D0B2C41F24935605000A83F8 /* LaunchScreen */ = {
			isa = PBXGroup;
			children = (
				D0B2C42C249358D7000A83F8 /* Views */,
				D0B2C42B249358C7000A83F8 /* Viewcontrollers */,
				D0B2C42A249358BF000A83F8 /* Models */,
			);
			path = LaunchScreen;
			sourceTree = "<group>";
		};
		D0B2C42A249358BF000A83F8 /* Models */ = {
			isa = PBXGroup;
			children = (
			);
			path = Models;
			sourceTree = "<group>";
		};
		D0B2C42B249358C7000A83F8 /* Viewcontrollers */ = {
			isa = PBXGroup;
			children = (
				D0B2C42524935710000A83F8 /* BSLaunchScreenVC.h */,
				D0B2C42624935710000A83F8 /* BSLaunchScreenVC.m */,
				D0B2C42724935710000A83F8 /* BSLaunchScreenVC.xib */,
				D04D3E3824963D4E002C36BD /* BSEnterBaseController.h */,
				D04D3E3924963D4E002C36BD /* BSEnterBaseController.m */,
				D04D3E3A24963D4E002C36BD /* BSEnterBaseController.xib */,
				D0B2C4412493B22A000A83F8 /* BSEnterViewController01.h */,
				D0B2C4422493B22A000A83F8 /* BSEnterViewController01.m */,
			);
			path = Viewcontrollers;
			sourceTree = "<group>";
		};
		D0B2C42C249358D7000A83F8 /* Views */ = {
			isa = PBXGroup;
			children = (
				D0B2C4362493893D000A83F8 /* BSWaitingAnimationView.h */,
				D0B2C4372493893D000A83F8 /* BSWaitingAnimationView.m */,
				D0B2C43E2493A933000A83F8 /* BSAgreementView.h */,
				D0B2C43F2493A933000A83F8 /* BSAgreementView.m */,
				D0B2C43324937112000A83F8 /* BSIntroduceView.h */,
				D0B2C43424937112000A83F8 /* BSIntroduceView.m */,
				D02F84E824955DBF0040D1C3 /* BSSubscribeRadio.h */,
				D02F84E924955DBF0040D1C3 /* BSSubscribeRadio.m */,
				D02F84DF249533770040D1C3 /* BSEtDetailsView.h */,
				D02F84E0249533770040D1C3 /* BSEtDetailsView.m */,
				D02F84E524954D7E0040D1C3 /* BSEtWebUrlView.h */,
				D02F84E624954D7E0040D1C3 /* BSEtWebUrlView.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		D0DEEEA3249C4E19002E5B19 /* BSCloudRegisterVC */ = {
			isa = PBXGroup;
			children = (
				D0DEEEAA249C5064002E5B19 /* BSRegisterProgressView.h */,
				D0DEEEAB249C5064002E5B19 /* BSRegisterProgressView.m */,
				D0FAEE34249DE22A00DFA24D /* BSPwdGuardTextField.h */,
				D0FAEE35249DE22A00DFA24D /* BSPwdGuardTextField.m */,
			);
			path = BSCloudRegisterVC;
			sourceTree = "<group>";
		};
		D0FAEE25249DD1BC00DFA24D /* Images */ = {
			isa = PBXGroup;
			children = (
				CE29775724AA3C23000861B2 /* AgreementBackground.jpg */,
				CE29775A24AA3C23000861B2 /* EnterViewBackground.jpg */,
				CE29775624AA3C23000861B2 /* IntroduceBackground01.jpg */,
				CE29775924AA3C23000861B2 /* IntroduceBackground02.jpg */,
				CE29775824AA3C23000861B2 /* LaunchScreen.jpg */,
			);
			path = Images;
			sourceTree = "<group>";
		};
		D0FAEE37249E21D900DFA24D /* BSCloudRegisterVC */ = {
			isa = PBXGroup;
			children = (
				D08BD2742498D55700590F7F /* BSCloudRegisterVC.h */,
				D08BD2752498D55700590F7F /* BSCloudRegisterVC.m */,
				D08BD2762498D55700590F7F /* BSCloudRegisterVC.xib */,
				D0FAEE38249E22C900DFA24D /* BSPasswordGuardVC.h */,
				D0FAEE39249E22C900DFA24D /* BSPasswordGuardVC.m */,
				D0FAEE3A249E22C900DFA24D /* BSPasswordGuardVC.xib */,
				D0212D3624A0A389000CE512 /* BSRegisterFinishVC.h */,
				D0212D3724A0A389000CE512 /* BSRegisterFinishVC.m */,
				D0212D3824A0A389000CE512 /* BSRegisterFinishVC.xib */,
			);
			path = BSCloudRegisterVC;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		C462B2F52434EEC0007EDF29 /* PhoneCloneV2 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C462B30C2434EEC1007EDF29 /* Build configuration list for PBXNativeTarget "PhoneCloneV2" */;
			buildPhases = (
				35C04B0FE08F49BD3FEAA2DE /* [CP] Check Pods Manifest.lock */,
				C462B2F22434EEC0007EDF29 /* Sources */,
				C462B2F32434EEC0007EDF29 /* Frameworks */,
				C462B2F42434EEC0007EDF29 /* Resources */,
				98C803EE6496B55C43ED6A07 /* [CP] Copy Pods Resources */,
				98C9330B44152CE67F701FB0 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = PhoneCloneV2;
			productName = PhoneClone;
			productReference = C462B2F62434EEC0007EDF29 /* PhoneCloneV2.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		C462B2EE2434EEC0007EDF29 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				CLASSPREFIX = BS;
				LastUpgradeCheck = 1020;
				ORGANIZATIONNAME = PhoneClone;
				TargetAttributes = {
					C462B2F52434EEC0007EDF29 = {
						CreatedOnToolsVersion = 10.2.1;
					};
				};
			};
			buildConfigurationList = C462B2F12434EEC0007EDF29 /* Build configuration list for PBXProject "PhoneCloneV2" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = C462B2ED2434EEC0007EDF29;
			productRefGroup = C462B2F72434EEC0007EDF29 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				C462B2F52434EEC0007EDF29 /* PhoneCloneV2 */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		C462B2F42434EEC0007EDF29 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CE29775D24AA3C23000861B2 /* LaunchScreen.jpg in Resources */,
				D02208C924A22E710019708C /* BSResetPasswordVC.xib in Resources */,
				C4D8E6B02438888900199E15 /* BSMeVC.xib in Resources */,
				D02208BE24A21C470019708C /* BSGetBackPasswordVC.xib in Resources */,
				CE4964DA24A60C1100E8491C /* BSDownloadQrcodeVC.xib in Resources */,
				CE4964AE24A60C1100E8491C /* BSTransferDeviceVC.xib in Resources */,
				CE4964D524A60C1100E8491C /* BSQRCodeScanningVC.xib in Resources */,
				CE4964D624A60C1100E8491C /* BSReceivQRCodeVC.xib in Resources */,
				CE4964D924A60C1100E8491C /* BSInstrumentVC.xib in Resources */,
				CE4964C424A60C1100E8491C /* BSMusicCell.xib in Resources */,
				D08BD25E24985ECF00590F7F /* BSCloudLoginVC.xib in Resources */,
				D0417E0624A35B87009B5781 /* BSVIPRechargeVC.xib in Resources */,
				C462B3062434EEC1007EDF29 /* LaunchScreen.storyboard in Resources */,
				C459F5FF243A27B000169E98 /* BSTransferFileProgressVC.xib in Resources */,
				CE29775E24AA3C23000861B2 /* IntroduceBackground02.jpg in Resources */,
				D099B0982491DCDB008CC521 /* BSTabBarItem.xib in Resources */,
				CE4964BB24A60C1100E8491C /* BSTransferEmptyView.xib in Resources */,
				CE4964B724A60C1100E8491C /* BSVideoCCell.xib in Resources */,
				CE4964C124A60C1100E8491C /* BSResItemCell.xib in Resources */,
				D0FAEE3C249E22C900DFA24D /* BSPasswordGuardVC.xib in Resources */,
				C467ECB124388F7200FBC375 /* Localizable.strings in Resources */,
				D04D3E3C24963D4E002C36BD /* BSEnterBaseController.xib in Resources */,
				CE29775B24AA3C23000861B2 /* IntroduceBackground01.jpg in Resources */,
				CE4964E324A60C5400E8491C /* BSTransferMusicAlertVC.xib in Resources */,
				CE4964AD24A60C1100E8491C /* BSConnTipsVC.xib in Resources */,
				CE29775F24AA3C23000861B2 /* EnterViewBackground.jpg in Resources */,
				CE4964AC24A60C1100E8491C /* BSTransferMusicAlertVC.xib in Resources */,
				D08BD2782498D55700590F7F /* BSCloudRegisterVC.xib in Resources */,
				D0212D3A24A0A389000CE512 /* BSRegisterFinishVC.xib in Resources */,
				CE29775C24AA3C23000861B2 /* AgreementBackground.jpg in Resources */,
				CE4964CA24A60C1100E8491C /* BSContactHeaderView.xib in Resources */,
				CE4964C324A60C1100E8491C /* BSCalendarCell.xib in Resources */,
				CE29773824A84983000861B2 /* InfoPlist.strings in Resources */,
				D0B2C42924935710000A83F8 /* BSLaunchScreenVC.xib in Resources */,
				D02208C424A21D710019708C /* BSVerifyPwdGuardVC.xib in Resources */,
				CE4964C924A60C1100E8491C /* BSAvatarView.xib in Resources */,
				CE4964D724A60C1100E8491C /* BSNetworkSpeedRetVC.xib in Resources */,
				CE4964B524A60C1100E8491C /* BSPhotoCCell.xib in Resources */,
				CE4964E024A60C1100E8491C /* BSInsItemView.xib in Resources */,
				CE4964E224A60C1100E8491C /* BSInstDetailCell.xib in Resources */,
				D02208CE24A2408B0019708C /* BSResetPwdFinishVC.xib in Resources */,
				C462B3032434EEC1007EDF29 /* Assets.xcassets in Resources */,
				CE4964BF24A60C1100E8491C /* BSTransferPeerCell.xib in Resources */,
				CE4964A724A60C1100E8491C /* BSTransferPeersVC.xib in Resources */,
				CE4964D824A60C1100E8491C /* BSNetworkSpeedVC.xib in Resources */,
				CE4962AA24A6065100E8491C /* SGQRCode.bundle in Resources */,
				D08BD26824986A4400590F7F /* BSCloudEnterVC.xib in Resources */,
				D02208AE24A1F98E0019708C /* BSRegisterBaseVC.xib in Resources */,
				CE4964BC24A60C1100E8491C /* BSTransferEmptyView.xib in Resources */,
				CE4964C624A60C1100E8491C /* BSContactCell.xib in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		35C04B0FE08F49BD3FEAA2DE /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-PhoneCloneV2-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		98C803EE6496B55C43ED6A07 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-PhoneCloneV2/Pods-PhoneCloneV2-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-PhoneCloneV2/Pods-PhoneCloneV2-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-PhoneCloneV2/Pods-PhoneCloneV2-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		98C9330B44152CE67F701FB0 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-PhoneCloneV2/Pods-PhoneCloneV2-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-PhoneCloneV2/Pods-PhoneCloneV2-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-PhoneCloneV2/Pods-PhoneCloneV2-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		C462B2F22434EEC0007EDF29 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D010EBD62491D26400C312E1 /* BSCloudStorageVC.m in Sources */,
				C480B2402434FCE000637D3A /* BSNavigationController.m in Sources */,
				CE4964FB24A60F1200E8491C /* MeasureNetTool.m in Sources */,
				C4ECC6E624373AD3004C60C9 /* JXCategoryViewAnimator.m in Sources */,
				CE4964C524A60C1100E8491C /* BSContactCell.m in Sources */,
				D02208C324A21D710019708C /* BSVerifyPwdGuardVC.m in Sources */,
				C4ECC6D124373AD3004C60C9 /* JXCategoryIndicatorCell.m in Sources */,
				C4ECC6D224373AD3004C60C9 /* JXCategoryIndicatorView.m in Sources */,
				A52E87262B57711300708A0F /* UIButton+Create.m in Sources */,
				CE4964DB24A60C1100E8491C /* BSDownloadQrcodeVC.m in Sources */,
				D08BD2712498C7BD00590F7F /* BSLabelButtonView.m in Sources */,
				D08BD26B2498992500590F7F /* BSLogoTitleView.m in Sources */,
				A52E871D2B57702200708A0F /* ZYELabel.m in Sources */,
				D0FAEE3B249E22C900DFA24D /* BSPasswordGuardVC.m in Sources */,
				D08BD25D24985ECF00590F7F /* BSCloudLoginVC.m in Sources */,
				CE49650D24A60F5A00E8491C /* BSAWSS3Manager.m in Sources */,
				D02F84E724954D7E0040D1C3 /* BSEtWebUrlView.m in Sources */,
				D08BD2772498D55700590F7F /* BSCloudRegisterVC.m in Sources */,
				C4ECC6C824373AD3004C60C9 /* JXCategoryIndicatorLineView.m in Sources */,
				A514D6512B58C21300C207FC /* QRcodeView.m in Sources */,
				CE4964D124A60C1100E8491C /* BSNetworkSpeedVC.m in Sources */,
				D0FAEE36249DE22A00DFA24D /* BSPwdGuardTextField.m in Sources */,
				C4ECC6CA24373AD3004C60C9 /* JXCategoryIndicatorRainbowLineView.m in Sources */,
				C4E8AB422436F136004B4331 /* BSTabBarController.m in Sources */,
				CE4964D324A60C1100E8491C /* BSNetworkSpeedRetVC.m in Sources */,
				A5E272CA2B835328008BF227 /* UIView+cornerRadius.m in Sources */,
				D02208AD24A1F98E0019708C /* BSRegisterBaseVC.m in Sources */,
				C4ECC6C724373AD3004C60C9 /* JXCategoryTitleVerticalZoomView.m in Sources */,
				D0B2C4402493A933000A83F8 /* BSAgreementView.m in Sources */,
				C4ECC6EB24373AD3004C60C9 /* JXCategoryBaseCellModel.m in Sources */,
				D0417E0524A35B87009B5781 /* BSVIPRechargeVC.m in Sources */,
				D02208CD24A2408B0019708C /* BSResetPwdFinishVC.m in Sources */,
				C4ECC6E324373AD3004C60C9 /* JXCategoryFactory.m in Sources */,
				C4ECC6D924373AD3004C60C9 /* JXCategoryTitleCellModel.m in Sources */,
				CE4964DE24A60C1100E8491C /* BSInsItemView.m in Sources */,
				A5D274C22B54C5B900552859 /* BSConnectManager.m in Sources */,
				C4ECC6EA24373AD3004C60C9 /* JXCategoryBaseCell.m in Sources */,
				C4ECC6E224373AD3004C60C9 /* JXCategoryListContainerView.m in Sources */,
				CE4962B024A6065100E8491C /* SGQRCodeAlbumManager.m in Sources */,
				C4ECC6CB24373AD3004C60C9 /* JXCategoryIndicatorImageView.m in Sources */,
				A5D717A22B4F86B800AE995C /* MyHTTPConnection.m in Sources */,
				CE4964A124A60C1100E8491C /* BSTransferResVC.m in Sources */,
				D099B09A2491DCDB008CC521 /* BSButton.m in Sources */,
				CE4962B524A6085100E8491C /* BSTransferRes.m in Sources */,
				C4ECC6CC24373AD3004C60C9 /* JXCategoryIndicatorBallView.m in Sources */,
				C4ECC6CF24373AD3004C60C9 /* JXCategoryIndicatorBackgroundView.m in Sources */,
				D0B2C4442493B22A000A83F8 /* BSEnterViewController01.m in Sources */,
				C4ECC6EE24373AE2004C60C9 /* NullSafe.m in Sources */,
				C4ECC6E824373AD3004C60C9 /* UIColor+JXAdd.m in Sources */,
				A52E87202B57704300708A0F /* UIColor+Hex.m in Sources */,
				CE29775324AA39C8000861B2 /* NSString+Base64.m in Sources */,
				A514D6572B58C28800C207FC /* PopAnimationTool.m in Sources */,
				D02F84EA24955DBF0040D1C3 /* BSSubscribeRadio.m in Sources */,
				CE4964B624A60C1100E8491C /* BSPhotoCCell.m in Sources */,
				D08BD263249860F600590F7F /* BSCloudLoginHeadView.m in Sources */,
				CE4964AA24A60C1100E8491C /* BSConnTipsVC.m in Sources */,
				CE4964BD24A60C1100E8491C /* BSTransferPeerCell.m in Sources */,
				CE4964DC24A60C1100E8491C /* BSInstrumentVC.m in Sources */,
				C4ECC6D824373AD3004C60C9 /* JXCategoryTitleImageCellModel.m in Sources */,
				C4ECC6C624373AD3004C60C9 /* JXCategoryTitleVerticalZoomCell.m in Sources */,
				A5DCC9F92B5F9D66004151F8 /* UpdateManager.m in Sources */,
				A5E272C72B834E2D008BF227 /* BSLinkButton.m in Sources */,
				CE4964D424A60C1100E8491C /* BSReceivQRCodeVC.m in Sources */,
				CE29774F24AA39C8000861B2 /* Reachability.m in Sources */,
				CE49650B24A60F5A00E8491C /* BSRetReciveFileServer.m in Sources */,
				C4ECC6DB24373AD3004C60C9 /* JXCategoryTitleView.m in Sources */,
				A52E87162B576CBD00708A0F /* MySegmentedControl.m in Sources */,
				A5D274C52B54C5F400552859 /* tokenTool.m in Sources */,
				A5D717A72B4F86B800AE995C /* scannner.m in Sources */,
				CE4964A024A60C1100E8491C /* BSCalender.m in Sources */,
				CE4964E124A60C1100E8491C /* BSClockDialView.m in Sources */,
				CE49650E24A60F5A00E8491C /* BSSendFileManager.m in Sources */,
				CE4964C724A60C1100E8491C /* BSAnimationView.m in Sources */,
				CE29775424AA39C8000861B2 /* StoreIAPManager.m in Sources */,
				C4ECC6F224373AEE004C60C9 /* pinyin.c in Sources */,
				D099B09B2491DCDB008CC521 /* BSView.m in Sources */,
				CE4964AF24A60C1100E8491C /* BSTransferCalendarFileVC.m in Sources */,
				C4ECC6E924373AD3004C60C9 /* JXCategoryBaseView.m in Sources */,
				CE4964C024A60C1100E8491C /* BSResItemCell.m in Sources */,
				C4ECC6DF24373AD3004C60C9 /* JXCategoryNumberView.m in Sources */,
				A5D717D62B4F8B5A00AE995C /* File.m in Sources */,
				A52E87292B57716400708A0F /* ZYEButton.m in Sources */,
				A5DCC9F82B5F9D66004151F8 /* MyAdManager.m in Sources */,
				CE4964BA24A60C1100E8491C /* BSTransferEmptyView.m in Sources */,
				CE49651024A60F5A00E8491C /* BSUserDefaultManager.m in Sources */,
				C4ECC6D724373AD3004C60C9 /* JXCategoryTitleImageCell.m in Sources */,
				C462B3092434EEC1007EDF29 /* main.m in Sources */,
				D099B0992491DCDB008CC521 /* BSTabBarItem.m in Sources */,
				CE4962B624A6085100E8491C /* BSResGroupModel.m in Sources */,
				D052DC482492139A00222ACB /* UIImage+Custom.m in Sources */,
				A52E871A2B576FE700708A0F /* UILabel+createLabels.m in Sources */,
				D04D3E3B24963D4E002C36BD /* BSEnterBaseController.m in Sources */,
				D0DEEEAC249C5064002E5B19 /* BSRegisterProgressView.m in Sources */,
				C4ECC6DE24373AD3004C60C9 /* JXCategoryImageCellModel.m in Sources */,
				A5DCC9FC2B5F9D66004151F8 /* ZYEUpdateView.m in Sources */,
				A5D717A52B4F86B800AE995C /* WebGelistManager.m in Sources */,
				A52E87252B57711300708A0F /* UIButton+CenterImageAndTitle.m in Sources */,
				A5D7178F2B4F867800AE995C /* httpNework.m in Sources */,
				D0DEEEAF249CC8E8002E5B19 /* BSTextField.m in Sources */,
				CE4964B324A60C1100E8491C /* BSTransferDeviceVC.m in Sources */,
				A5D717D12B4F8A1000AE995C /* FileManager.m in Sources */,
				D002C491249B589C00266381 /* BSWaitingTool.m in Sources */,
				CE4962AB24A6065100E8491C /* UIImage+SGImageSize.m in Sources */,
				C49824542438D172003C94B0 /* ZZCircleProgress.m in Sources */,
				D02208BD24A21C470019708C /* BSGetBackPasswordVC.m in Sources */,
				CE4964C224A60C1100E8491C /* BSMusicCell.m in Sources */,
				C4ECC6CE24373AD3004C60C9 /* JXCategoryIndicatorDotLineView.m in Sources */,
				D0C4074F2499B0C700075843 /* UINavigationController+BSInitToTabBarChildController.m in Sources */,
				C4ECC6D624373AD3004C60C9 /* JXCategoryTitleImageView.m in Sources */,
				C4A374E62439DA3A00FA82D9 /* BSPeerModel.m in Sources */,
				CE4964D224A60C1100E8491C /* BSInstrumentDetailVC.m in Sources */,
				C4ECC6DC24373AD3004C60C9 /* JXCategoryImageView.m in Sources */,
				A5D274C82B55196D00552859 /* BSNewSendServer.m in Sources */,
				CE4964A824A60C1100E8491C /* BSHomeVC.m in Sources */,
				C4ECC6D424373AD3004C60C9 /* JXCategoryDotView.m in Sources */,
				C4ECC6DD24373AD3004C60C9 /* JXCategoryImageCell.m in Sources */,
				CE4964B224A60C1100E8491C /* BSTransferVideoFileVC.m in Sources */,
				D0417E0E24A3695A009B5781 /* BSVipRadioView.m in Sources */,
				A5D717AB2B4F871A00AE995C /* PCObjcect.m in Sources */,
				A5DCC9FA2B5F9D66004151F8 /* UIView+Extension.m in Sources */,
				CE4964B124A60C1100E8491C /* BSTransferPhotoFileVC.m in Sources */,
				A514D6582B58C28800C207FC /* PopView.m in Sources */,
				C4F8A4FF2437128E003FAB69 /* BSMeVC.m in Sources */,
				CE49650F24A60F5A00E8491C /* BSReciveFileManager.m in Sources */,
				CE4964B424A60C1100E8491C /* BSTransferFileBaseVC.m in Sources */,
				C4ECC6D324373AD3004C60C9 /* JXCategoryDotCell.m in Sources */,
				CE4964BE24A60C1100E8491C /* BSCalendarCell.m in Sources */,
				C4ECC6C524373AD3004C60C9 /* JXCategoryTitleVerticalZoomCellModel.m in Sources */,
				C416403B2438A9CA0079BCB8 /* BSDeviceInfo.m in Sources */,
				A5D717A62B4F86B800AE995C /* NewScaner.m in Sources */,
				CE4964B924A60C1100E8491C /* BSContactHeaderView.m in Sources */,
				A5D717A32B4F86B800AE995C /* MyHTTPResponse.m in Sources */,
				CE4964DD24A60C1100E8491C /* BSQRCodeScanningVC.m in Sources */,
				CE49651A24A612A000E8491C /* BSTransferMusicAlertVC.m in Sources */,
				C459F5FE243A27B000169E98 /* BSTransferFileProgressVC.m in Sources */,
				D0B2C43524937112000A83F8 /* BSIntroduceView.m in Sources */,
				C462B2FB2434EEC0007EDF29 /* AppDelegate.m in Sources */,
				D0417E0B24A3646E009B5781 /* BSIconDetailsLabelView.m in Sources */,
				C4ECC6DA24373AD3004C60C9 /* JXCategoryTitleCell.m in Sources */,
				CE4964B024A60C1100E8491C /* BSTransferVC.m in Sources */,
				C416403E2438ACE40079BCB8 /* UIDevice+Screen.m in Sources */,
				D08BD26724986A4400590F7F /* BSCloudEnterVC.m in Sources */,
				C4ECC6E124373AD3004C60C9 /* JXCategoryNumberCellModel.m in Sources */,
				CE49650A24A60F5A00E8491C /* BSRetSendFileServer.m in Sources */,
				A5D717A82B4F86B800AE995C /* DHIPAdress.m in Sources */,
				D02208C824A22E710019708C /* BSResetPasswordVC.m in Sources */,
				CE4962AF24A6065100E8491C /* SGQRCodeGenerateManager.m in Sources */,
				CE4962AC24A6065100E8491C /* SGQRCodeHelperTool.m in Sources */,
				A5D717D92B4F8BCB00AE995C /* CalendarListEventInfo.m in Sources */,
				D0B2C4382493893D000A83F8 /* BSWaitingAnimationView.m in Sources */,
				C4ECC6E724373AD3004C60C9 /* JXCategoryListCollectionContainerView.m in Sources */,
				D099B09F2491DD4A008CC521 /* BSCloudStorageHeadView.m in Sources */,
				A5E272C42B834B78008BF227 /* BSLinkView.m in Sources */,
				************************ /* BSTransferContactFileVC.m in Sources */,
				C4ECC6CD24373AD3004C60C9 /* JXCategoryIndicatorComponentView.m in Sources */,
				************************ /* BSAvatarView.m in Sources */,
				D0B2C42824935710000A83F8 /* BSLaunchScreenVC.m in Sources */,
				C4ECC6E024373AD3004C60C9 /* JXCategoryNumberCell.m in Sources */,
				0C5832C6249F6FE40012BAB0 /* BSAccountModel.m in Sources */,
				C4ECC6D524373AD3004C60C9 /* JXCategoryDotCellModel.m in Sources */,
				************************ /* SGQRCodeScanManager.m in Sources */,
				C4ECC6E524373AD3004C60C9 /* JXCategoryIndicatorParamsModel.m in Sources */,
				************************ /* BSTransferMusicFileVC.m in Sources */,
				************************ /* SGQRCodeScanningView.m in Sources */,
				************************ /* BSTransferPeersVC.m in Sources */,
				C4ECC6E424373AD3004C60C9 /* JXCategoryCollectionView.m in Sources */,
				C4ECC6D024373AD3004C60C9 /* JXCategoryIndicatorCellModel.m in Sources */,
				************************ /* BSAWSDynamoDBMgr.m in Sources */,
				D002C497249B61B300266381 /* BSAlertTool.m in Sources */,
				************************ /* NSDictionary+Value.m in Sources */,
				************************ /* BSInstDetailCell.m in Sources */,
				************************ /* BSVideoCCell.m in Sources */,
				************************ /* BSMusic.m in Sources */,
				D099B0AB2491DF94008CC521 /* BSCsBodyCellView.m in Sources */,
				A5D717A42B4F86B800AE995C /* MyCustomDataResponse.m in Sources */,
				CE29775224AA39C8000861B2 /* ProgressViewUserInfo.m in Sources */,
				CE29775124AA39C8000861B2 /* StoreIAPObserver.m in Sources */,
				D04D3E3F2496614E002C36BD /* UIView+BSAnimation.m in Sources */,
				C4ECC6C924373AD3004C60C9 /* JXCategoryIndicatorTriangleView.m in Sources */,
				D0212D3924A0A389000CE512 /* BSRegisterFinishVC.m in Sources */,
				C480B23D2434FC2900637D3A /* BSBaseViewController.m in Sources */,
				CE49651324A60FA000E8491C /* NSData+UTF8.m in Sources */,
				D02F84E1249533770040D1C3 /* BSEtDetailsView.m in Sources */,
				D099B0A22491DD82008CC521 /* BSCsNumLabel.m in Sources */,
				C49824532438D172003C94B0 /* NSTimer+timerBlock.m in Sources */,
				D0212D3024A05E2A000CE512 /* NSString+Predicate.m in Sources */,
				CE49649F24A60C1100E8491C /* BSContact.m in Sources */,
				A5DCC9F72B5F9D66004151F8 /* UIImageView+Create.m in Sources */,
				A52E87112B576C8900708A0F /* startLinkViewController.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		C462B3042434EEC1007EDF29 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				C462B3052434EEC1007EDF29 /* Base */,
				C4CCF3F124388CC400A289A8 /* zh-Hans */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
		C467ECB324388F7200FBC375 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				C467ECB224388F7200FBC375 /* en */,
				C467ECB424388F8200FBC375 /* zh-Hans */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
		C4D8E6B22438888900199E15 /* BSMeVC.xib */ = {
			isa = PBXVariantGroup;
			children = (
				C4D8E6B12438888900199E15 /* Base */,
				C4D8E6B4243888B900199E15 /* en */,
				C4CCF3F024388CB600A289A8 /* zh-Hans */,
			);
			name = BSMeVC.xib;
			sourceTree = "<group>";
		};
		CE29773A24A84983000861B2 /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				CE29773924A84983000861B2 /* en */,
				CE29773B24A84984000861B2 /* zh-Hans */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
		CE49642F24A60C1100E8491C /* BSTransferMusicAlertVC.xib */ = {
			isa = PBXVariantGroup;
			children = (
				CE49643024A60C1100E8491C /* Base */,
			);
			name = BSTransferMusicAlertVC.xib;
			sourceTree = "<group>";
		};
		CE49643124A60C1100E8491C /* BSConnTipsVC.xib */ = {
			isa = PBXVariantGroup;
			children = (
				CE49643224A60C1100E8491C /* Base */,
				CE4964EB24A60D7500E8491C /* en */,
				CE4964EC24A60D7700E8491C /* zh-Hans */,
			);
			name = BSConnTipsVC.xib;
			sourceTree = "<group>";
		};
		CE49643324A60C1100E8491C /* BSTransferDeviceVC.xib */ = {
			isa = PBXVariantGroup;
			children = (
				CE49643424A60C1100E8491C /* Base */,
				A5D5A39D2BAA7CBE00324468 /* zh-Hans */,
				A5D5A39F2BAA7CD100324468 /* en */,
			);
			name = BSTransferDeviceVC.xib;
			sourceTree = "<group>";
		};
		CE49644E24A60C1100E8491C /* BSTransferEmptyView.xib */ = {
			isa = PBXVariantGroup;
			children = (
				CE49644F24A60C1100E8491C /* Base */,
			);
			name = BSTransferEmptyView.xib;
			sourceTree = "<group>";
		};
		CE49648224A60C1100E8491C /* BSQRCodeScanningVC.xib */ = {
			isa = PBXVariantGroup;
			children = (
				CE49648324A60C1100E8491C /* Base */,
				CE4964ED24A60DE200E8491C /* en */,
				CE4964EE24A60DE400E8491C /* zh-Hans */,
			);
			name = BSQRCodeScanningVC.xib;
			sourceTree = "<group>";
		};
		CE49648424A60C1100E8491C /* BSReceivQRCodeVC.xib */ = {
			isa = PBXVariantGroup;
			children = (
				CE49648524A60C1100E8491C /* Base */,
				CE4964EF24A60DE800E8491C /* en */,
				CE4964F024A60DEA00E8491C /* zh-Hans */,
			);
			name = BSReceivQRCodeVC.xib;
			sourceTree = "<group>";
		};
		CE49648624A60C1100E8491C /* BSNetworkSpeedRetVC.xib */ = {
			isa = PBXVariantGroup;
			children = (
				CE49648724A60C1100E8491C /* Base */,
				CE4964F124A60DF400E8491C /* en */,
				CE4964F224A60DF800E8491C /* zh-Hans */,
			);
			name = BSNetworkSpeedRetVC.xib;
			sourceTree = "<group>";
		};
		CE49648824A60C1100E8491C /* BSNetworkSpeedVC.xib */ = {
			isa = PBXVariantGroup;
			children = (
				CE49648924A60C1100E8491C /* Base */,
				CE4964F324A60DFD00E8491C /* en */,
				CE4964F424A60DFF00E8491C /* zh-Hans */,
			);
			name = BSNetworkSpeedVC.xib;
			sourceTree = "<group>";
		};
		CE49648A24A60C1100E8491C /* BSInstrumentVC.xib */ = {
			isa = PBXVariantGroup;
			children = (
				CE49648B24A60C1100E8491C /* Base */,
				CE4964F524A60E0400E8491C /* en */,
				CE4964F624A60E0600E8491C /* zh-Hans */,
			);
			name = BSInstrumentVC.xib;
			sourceTree = "<group>";
		};
		CE49648C24A60C1100E8491C /* BSDownloadQrcodeVC.xib */ = {
			isa = PBXVariantGroup;
			children = (
				CE49648D24A60C1100E8491C /* Base */,
				CE4964F724A60E0A00E8491C /* en */,
				CE4964F824A60E0C00E8491C /* zh-Hans */,
			);
			name = BSDownloadQrcodeVC.xib;
			sourceTree = "<group>";
		};
		CE4964E524A60C5400E8491C /* BSTransferMusicAlertVC.xib */ = {
			isa = PBXVariantGroup;
			children = (
				CE4964E624A60C5E00E8491C /* zh-Hans */,
				CE4964E724A60C6100E8491C /* Base */,
				CE4964E824A60CB400E8491C /* en */,
			);
			name = BSTransferMusicAlertVC.xib;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		C462B30A2434EEC1007EDF29 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		C462B30B2434EEC1007EDF29 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		C462B30D2434EEC1007EDF29 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F8FCB8A6B42BC1C6204AE900 /* Pods-PhoneCloneV2.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1.37;
				DEVELOPMENT_TEAM = W83W7DVU38;
				GCC_PREFIX_HEADER = "$(SRCROOT)/$(PROJECT_NAEM)/PhoneCloneV2/PhoneCloneV2.pch";
				INFOPLIST_FILE = PhoneCloneV2/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.4;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ld64",
					"-ObjC",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.huang.phonetransfer.test;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		C462B30E2434EEC1007EDF29 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0DE47069D132F5E15EC57C98 /* Pods-PhoneCloneV2.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1.37;
				DEVELOPMENT_TEAM = W83W7DVU38;
				GCC_PREFIX_HEADER = "$(SRCROOT)/$(PROJECT_NAEM)/PhoneCloneV2/PhoneCloneV2.pch";
				INFOPLIST_FILE = PhoneCloneV2/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.4;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ld64",
					"-ObjC",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.huang.phonetransfer.test;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		C462B2F12434EEC0007EDF29 /* Build configuration list for PBXProject "PhoneCloneV2" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C462B30A2434EEC1007EDF29 /* Debug */,
				C462B30B2434EEC1007EDF29 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C462B30C2434EEC1007EDF29 /* Build configuration list for PBXNativeTarget "PhoneCloneV2" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C462B30D2434EEC1007EDF29 /* Debug */,
				C462B30E2434EEC1007EDF29 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = C462B2EE2434EEC0007EDF29 /* Project object */;
}
