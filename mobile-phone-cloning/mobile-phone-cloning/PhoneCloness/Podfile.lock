PODS:
  - Ads-CN (5.6.0.7):
    - Ads-CN/BUAdSDK (= 5.6.0.7)
  - Ads-CN/BUAdSDK (5.6.0.7):
    - BURelyFoundation/CSJ (= 0.3.0.4)
  - AFNetworking (4.0.1):
    - AFNetworking/NSURLSession (= 4.0.1)
    - AFNetworking/Reachability (= 4.0.1)
    - AFNetworking/Security (= 4.0.1)
    - AFNetworking/Serialization (= 4.0.1)
    - AFNetworking/UIKit (= 4.0.1)
  - AFNetworking/NSURLSession (4.0.1):
    - AFNetworking/Reachability
    - AFNetworking/Security
    - AFNetworking/Serialization
  - AFNetworking/Reachability (4.0.1)
  - AFNetworking/Security (4.0.1)
  - AFNetworking/Serialization (4.0.1)
  - AFNetworking/UIKit (4.0.1):
    - AFNetworking/NSURLSession
  - AppAuth (1.6.2):
    - AppAuth/Core (= 1.6.2)
    - AppAuth/ExternalUserAgent (= 1.6.2)
  - AppAuth/Core (1.6.2)
  - AppAuth/ExternalUserAgent (1.6.2):
    - AppAuth/Core
  - AWSCore (2.33.8)
  - AWSDynamoDB (2.33.8):
    - AWSCore (= 2.33.8)
  - AWSS3 (2.33.8):
    - AWSCore (= 2.33.8)
  - BlocksKit (2.2.6):
    - BlocksKit/All (= 2.2.6)
  - BlocksKit/All (2.2.6):
    - BlocksKit/Core
    - BlocksKit/DynamicDelegate
    - BlocksKit/MessageUI
    - BlocksKit/QuickLook
    - BlocksKit/UIKit
  - BlocksKit/Core (2.2.6)
  - BlocksKit/DynamicDelegate (2.2.6)
  - BlocksKit/MessageUI (2.2.6):
    - BlocksKit/Core
    - BlocksKit/DynamicDelegate
  - BlocksKit/QuickLook (2.2.6):
    - BlocksKit/Core
    - BlocksKit/DynamicDelegate
  - BlocksKit/UIKit (2.2.6):
    - BlocksKit/Core
    - BlocksKit/DynamicDelegate
  - BURelyFoundation/AFNetworking (0.3.0.4)
  - BURelyFoundation/BURelyFoundation (0.3.0.4)
  - BURelyFoundation/CSJ (0.3.0.4):
    - BURelyFoundation/BURelyFoundation
    - BURelyFoundation/HM
    - BURelyFoundation/NETWork
  - BURelyFoundation/HM (0.3.0.4)
  - BURelyFoundation/NETWork (0.3.0.4):
    - BURelyFoundation/AFNetworking
  - CocoaAsyncSocket (7.6.5)
  - CocoaHTTPServer (2.3):
    - CocoaAsyncSocket
    - CocoaLumberjack
  - CocoaLumberjack (3.8.1):
    - CocoaLumberjack/Core (= 3.8.1)
  - CocoaLumberjack/Core (3.8.1)
  - FBAEMKit (16.3.1):
    - FBSDKCoreKit_Basics (= 16.3.1)
  - FBSDKCoreKit (16.3.1):
    - FBAEMKit (= 16.3.1)
    - FBSDKCoreKit_Basics (= 16.3.1)
  - FBSDKCoreKit_Basics (16.3.1)
  - FBSDKLoginKit (16.3.1):
    - FBSDKCoreKit (= 16.3.1)
  - FDFullscreenPopGesture (1.1)
  - GoogleSignIn (7.0.0):
    - AppAuth (~> 1.5)
    - GTMAppAuth (< 3.0, >= 1.3)
    - GTMSessionFetcher/Core (< 4.0, >= 1.1)
  - GTMAppAuth (2.0.0):
    - AppAuth/Core (~> 1.6)
    - GTMSessionFetcher/Core (< 4.0, >= 1.5)
  - GTMSessionFetcher/Core (3.2.0)
  - HMSegmentedControl (1.5.6)
  - IQKeyboardManager (6.5.15)
  - JKCategories (1.9.3):
    - JKCategories/CoreData (= 1.9.3)
    - JKCategories/CoreLocation (= 1.9.3)
    - JKCategories/Foundation (= 1.9.3)
    - JKCategories/MapKit (= 1.9.3)
    - JKCategories/QuartzCore (= 1.9.3)
    - JKCategories/UIKit (= 1.9.3)
  - JKCategories/CoreData (1.9.3):
    - JKCategories/CoreData/NSFetchRequest (= 1.9.3)
    - JKCategories/CoreData/NSManagedObject (= 1.9.3)
    - JKCategories/CoreData/NSManagedObjectContext (= 1.9.3)
  - JKCategories/CoreData/NSFetchRequest (1.9.3)
  - JKCategories/CoreData/NSManagedObject (1.9.3):
    - JKCategories/CoreData/NSManagedObjectContext
  - JKCategories/CoreData/NSManagedObjectContext (1.9.3):
    - JKCategories/CoreData/NSFetchRequest
  - JKCategories/CoreLocation (1.9.3):
    - JKCategories/CoreLocation/CLLocation (= 1.9.3)
  - JKCategories/CoreLocation/CLLocation (1.9.3)
  - JKCategories/Foundation (1.9.3):
    - JKCategories/Foundation/NSArray (= 1.9.3)
    - JKCategories/Foundation/NSBundle (= 1.9.3)
    - JKCategories/Foundation/NSData (= 1.9.3)
    - JKCategories/Foundation/NSDate (= 1.9.3)
    - JKCategories/Foundation/NSDateFormatter (= 1.9.3)
    - JKCategories/Foundation/NSDictionary (= 1.9.3)
    - JKCategories/Foundation/NSException (= 1.9.3)
    - JKCategories/Foundation/NSFileHandle (= 1.9.3)
    - JKCategories/Foundation/NSFileManager (= 1.9.3)
    - JKCategories/Foundation/NSHTTPCookieStorage (= 1.9.3)
    - JKCategories/Foundation/NSIndexPath (= 1.9.3)
    - JKCategories/Foundation/NSInvocation (= 1.9.3)
    - JKCategories/Foundation/NSNotificationCenter (= 1.9.3)
    - JKCategories/Foundation/NSNumber (= 1.9.3)
    - JKCategories/Foundation/NSObject (= 1.9.3)
    - JKCategories/Foundation/NSRunLoop (= 1.9.3)
    - JKCategories/Foundation/NSSet (= 1.9.3)
    - JKCategories/Foundation/NSString (= 1.9.3)
    - JKCategories/Foundation/NSTimer (= 1.9.3)
    - JKCategories/Foundation/NSURL (= 1.9.3)
    - JKCategories/Foundation/NSURLConnection (= 1.9.3)
    - JKCategories/Foundation/NSURLRequest (= 1.9.3)
    - JKCategories/Foundation/NSURLSession (= 1.9.3)
    - JKCategories/Foundation/NSUserDefaults (= 1.9.3)
  - JKCategories/Foundation/NSArray (1.9.3)
  - JKCategories/Foundation/NSBundle (1.9.3)
  - JKCategories/Foundation/NSData (1.9.3)
  - JKCategories/Foundation/NSDate (1.9.3)
  - JKCategories/Foundation/NSDateFormatter (1.9.3)
  - JKCategories/Foundation/NSDictionary (1.9.3)
  - JKCategories/Foundation/NSException (1.9.3)
  - JKCategories/Foundation/NSFileHandle (1.9.3)
  - JKCategories/Foundation/NSFileManager (1.9.3)
  - JKCategories/Foundation/NSHTTPCookieStorage (1.9.3)
  - JKCategories/Foundation/NSIndexPath (1.9.3)
  - JKCategories/Foundation/NSInvocation (1.9.3)
  - JKCategories/Foundation/NSNotificationCenter (1.9.3)
  - JKCategories/Foundation/NSNumber (1.9.3)
  - JKCategories/Foundation/NSObject (1.9.3)
  - JKCategories/Foundation/NSRunLoop (1.9.3)
  - JKCategories/Foundation/NSSet (1.9.3)
  - JKCategories/Foundation/NSString (1.9.3):
    - JKCategories/Foundation/NSData
  - JKCategories/Foundation/NSTimer (1.9.3)
  - JKCategories/Foundation/NSURL (1.9.3)
  - JKCategories/Foundation/NSURLConnection (1.9.3)
  - JKCategories/Foundation/NSURLRequest (1.9.3)
  - JKCategories/Foundation/NSURLSession (1.9.3)
  - JKCategories/Foundation/NSUserDefaults (1.9.3)
  - JKCategories/MapKit (1.9.3):
    - JKCategories/MapKit/MKMapView (= 1.9.3)
  - JKCategories/MapKit/MKMapView (1.9.3)
  - JKCategories/QuartzCore (1.9.3):
    - JKCategories/QuartzCore/CAAnimation (= 1.9.3)
    - JKCategories/QuartzCore/CALayer (= 1.9.3)
    - JKCategories/QuartzCore/CAMediaTimingFunction (= 1.9.3)
    - JKCategories/QuartzCore/CAShapeLayer (= 1.9.3)
    - JKCategories/QuartzCore/CATransaction (= 1.9.3)
  - JKCategories/QuartzCore/CAAnimation (1.9.3)
  - JKCategories/QuartzCore/CALayer (1.9.3)
  - JKCategories/QuartzCore/CAMediaTimingFunction (1.9.3)
  - JKCategories/QuartzCore/CAShapeLayer (1.9.3)
  - JKCategories/QuartzCore/CATransaction (1.9.3)
  - JKCategories/UIKit (1.9.3):
    - JKCategories/UIKit/UIApplication (= 1.9.3)
    - JKCategories/UIKit/UIBarButtonItem (= 1.9.3)
    - JKCategories/UIKit/UIBezierPath (= 1.9.3)
    - JKCategories/UIKit/UIButton (= 1.9.3)
    - JKCategories/UIKit/UIColor (= 1.9.3)
    - JKCategories/UIKit/UIControl (= 1.9.3)
    - JKCategories/UIKit/UIDevice (= 1.9.3)
    - JKCategories/UIKit/UIFont (= 1.9.3)
    - JKCategories/UIKit/UIImage (= 1.9.3)
    - JKCategories/UIKit/UIImageView (= 1.9.3)
    - JKCategories/UIKit/UILable (= 1.9.3)
    - JKCategories/UIKit/UINavigationBar (= 1.9.3)
    - JKCategories/UIKit/UINavigationController (= 1.9.3)
    - JKCategories/UIKit/UINavigationItem (= 1.9.3)
    - JKCategories/UIKit/UIPopoverController (= 1.9.3)
    - JKCategories/UIKit/UIResponder (= 1.9.3)
    - JKCategories/UIKit/UIScreen (= 1.9.3)
    - JKCategories/UIKit/UIScrollView (= 1.9.3)
    - JKCategories/UIKit/UISearchBar (= 1.9.3)
    - JKCategories/UIKit/UISplitViewController (= 1.9.3)
    - JKCategories/UIKit/UITableView (= 1.9.3)
    - JKCategories/UIKit/UITableViewCell (= 1.9.3)
    - JKCategories/UIKit/UITextField (= 1.9.3)
    - JKCategories/UIKit/UITextView (= 1.9.3)
    - JKCategories/UIKit/UIView (= 1.9.3)
    - JKCategories/UIKit/UIViewController (= 1.9.3)
    - JKCategories/UIKit/UIWindow (= 1.9.3)
  - JKCategories/UIKit/UIApplication (1.9.3)
  - JKCategories/UIKit/UIBarButtonItem (1.9.3)
  - JKCategories/UIKit/UIBezierPath (1.9.3)
  - JKCategories/UIKit/UIButton (1.9.3)
  - JKCategories/UIKit/UIColor (1.9.3)
  - JKCategories/UIKit/UIControl (1.9.3)
  - JKCategories/UIKit/UIDevice (1.9.3)
  - JKCategories/UIKit/UIFont (1.9.3)
  - JKCategories/UIKit/UIImage (1.9.3)
  - JKCategories/UIKit/UIImageView (1.9.3)
  - JKCategories/UIKit/UILable (1.9.3)
  - JKCategories/UIKit/UINavigationBar (1.9.3)
  - JKCategories/UIKit/UINavigationController (1.9.3)
  - JKCategories/UIKit/UINavigationItem (1.9.3)
  - JKCategories/UIKit/UIPopoverController (1.9.3)
  - JKCategories/UIKit/UIResponder (1.9.3)
  - JKCategories/UIKit/UIScreen (1.9.3)
  - JKCategories/UIKit/UIScrollView (1.9.3)
  - JKCategories/UIKit/UISearchBar (1.9.3)
  - JKCategories/UIKit/UISplitViewController (1.9.3)
  - JKCategories/UIKit/UITableView (1.9.3)
  - JKCategories/UIKit/UITableViewCell (1.9.3)
  - JKCategories/UIKit/UITextField (1.9.3)
  - JKCategories/UIKit/UITextView (1.9.3)
  - JKCategories/UIKit/UIView (1.9.3)
  - JKCategories/UIKit/UIViewController (1.9.3)
  - JKCategories/UIKit/UIWindow (1.9.3)
  - Masonry (1.1.0)
  - MBProgressHUD (1.2.0)
  - MJExtension (3.4.1)
  - PhoneNetSDK (1.0.12)
  - SDWebImage (4.3.3):
    - SDWebImage/Core (= 4.3.3)
  - SDWebImage/Core (4.3.3)
  - SSZipArchive (2.4.3)
  - SVProgressHUD (2.2.5)
  - TZImagePickerController (3.8.4):
    - TZImagePickerController/Basic (= 3.8.4)
    - TZImagePickerController/Location (= 3.8.4)
  - TZImagePickerController/Basic (3.8.4)
  - TZImagePickerController/Location (3.8.4)
  - UMAPM (1.8.6)
  - UMCommon (7.4.2):
    - UMDevice
  - UMDevice (3.2.0)
  - YBImageBrowser/NOSD (3.0.9):
    - YYImage
  - YYImage (1.0.4):
    - YYImage/Core (= 1.0.4)
  - YYImage/Core (1.0.4)
  - YYModel (1.0.4)

DEPENDENCIES:
  - Ads-CN
  - AFNetworking
  - AWSCore
  - AWSDynamoDB
  - AWSS3
  - BlocksKit (from `https://github.com/Tioks/BlocksKit`)
  - CocoaHTTPServer (from `https://github.com/chbeer/CocoaHTTPServer`)
  - FBSDKCoreKit
  - FBSDKLoginKit
  - FDFullscreenPopGesture
  - GoogleSignIn
  - HMSegmentedControl
  - IQKeyboardManager
  - JKCategories
  - Masonry
  - MBProgressHUD
  - MJExtension
  - PhoneNetSDK
  - SDWebImage (~> 4.3.0)
  - SSZipArchive
  - SVProgressHUD
  - TZImagePickerController
  - UMAPM
  - UMCommon
  - UMDevice
  - YBImageBrowser/NOSD
  - YYModel

SPEC REPOS:
  trunk:
    - Ads-CN
    - AFNetworking
    - AppAuth
    - AWSCore
    - AWSDynamoDB
    - AWSS3
    - BURelyFoundation
    - CocoaAsyncSocket
    - CocoaLumberjack
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - FDFullscreenPopGesture
    - GoogleSignIn
    - GTMAppAuth
    - GTMSessionFetcher
    - HMSegmentedControl
    - IQKeyboardManager
    - JKCategories
    - Masonry
    - MBProgressHUD
    - MJExtension
    - PhoneNetSDK
    - SDWebImage
    - SSZipArchive
    - SVProgressHUD
    - TZImagePickerController
    - UMAPM
    - UMCommon
    - UMDevice
    - YBImageBrowser
    - YYImage
    - YYModel

EXTERNAL SOURCES:
  BlocksKit:
    :git: https://github.com/Tioks/BlocksKit
  CocoaHTTPServer:
    :git: https://github.com/chbeer/CocoaHTTPServer

CHECKOUT OPTIONS:
  BlocksKit:
    :commit: 1700172dcd52949c3d5aa25caa4c9bffb10da29a
    :git: https://github.com/Tioks/BlocksKit
  CocoaHTTPServer:
    :commit: 2c89b8b6bead7f086121ffed95817755dfa498b9
    :git: https://github.com/chbeer/CocoaHTTPServer

SPEC CHECKSUMS:
  Ads-CN: c4c8559c0774f5b23c6f04a9fc3b110a44b5c1ef
  AFNetworking: 3bd23d814e976cd148d7d44c3ab78017b744cd58
  AppAuth: 3bb1d1cd9340bd09f5ed189fb00b1cc28e1e8570
  AWSCore: dfd3136979a57bba64f4771db2e59ed97adb0d3c
  AWSDynamoDB: 629ce4448aabd82c8d42767726f330630c958bd0
  AWSS3: 4de5b953dbb7cdfe0e91fcdf09b6d531480b2f9a
  BlocksKit: 5fae06c13638d9fdb0bbddca3dabfe8f5aa735c5
  BURelyFoundation: f6dc8547c858e7f97c1a0ec31a0c81b62f657892
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  CocoaHTTPServer: fe98e6ec4823bf45ea7d0949f835e849236355b2
  CocoaLumberjack: 5c7e64cdb877770859bddec4d3d5a0d7c9299df9
  FBAEMKit: 6c7b5eb77c96861bb59e040842c6e55bf39512ce
  FBSDKCoreKit: 5e4dd478947ab1bcc887e8cfadeae0727af1a942
  FBSDKCoreKit_Basics: cd7b5f5d1e8868c26706917919d058999ca672c3
  FBSDKLoginKit: 572cca0bc6c90067ef197187697cb3b584310c52
  FDFullscreenPopGesture: a8a620179e3d9c40e8e00256dcee1c1a27c6d0f0
  GoogleSignIn: b232380cf495a429b8095d3178a8d5855b42e842
  GTMAppAuth: 99fb010047ba3973b7026e45393f51f27ab965ae
  GTMSessionFetcher: 41b9ef0b4c08a6db4b7eb51a21ae5183ec99a2c8
  HMSegmentedControl: 34c1f54d822d8308e7b24f5d901ec674dfa31352
  IQKeyboardManager: 22ffab9bd300ad493485a390a095f5db0c841776
  JKCategories: c995567f04ff98680313f56544e6cb62d3152733
  Masonry: 678fab65091a9290e40e2832a55e7ab731aad201
  MBProgressHUD: 3ee5efcc380f6a79a7cc9b363dd669c5e1ae7406
  MJExtension: 21c5f6f8c4d5d8844b7ae8fbae08fed0b501f961
  PhoneNetSDK: d0f24d066e8a7f2dd8e7c1a162d4c6910266def7
  SDWebImage: de4d90b5bff3571eae7bd16202b1f43135409fa5
  SSZipArchive: fe6a26b2a54d5a0890f2567b5cc6de5caa600aef
  SVProgressHUD: 1428aafac632c1f86f62aa4243ec12008d7a51d6
  TZImagePickerController: f1c9f1cae6ac0e30b31aaa9698f9bf4a7cf5b84f
  UMAPM: e69eb6542a7c98f31a0d177d31ecfa1a2ceee1c3
  UMCommon: 35b28c82a3a3be892f895628261edce11b6a9e3e
  UMDevice: b08f6cc32f26798dbac58bd0e91fea1f008bd2b7
  YBImageBrowser: 7ecc8bf33ffa5f3b94c397c29b4f3638dd37f527
  YYImage: 1e1b62a9997399593e4b9c4ecfbbabbf1d3f3b54
  YYModel: 2a7fdd96aaa4b86a824e26d0c517de8928c04b30

PODFILE CHECKSUM: ed70a13315c9775f2610294215db39b910ae37e2

COCOAPODS: 1.16.2
