target 'PhoneCloneV2' do
  
  pod 'AFNetworking'
  pod 'JKCategories'
  pod 'FDFullscreenPopGesture'
  pod 'MBProgressHUD'
  pod 'TZImagePickerController'
  pod 'YBImageBrowser/NOSD'
  pod 'SDWebImage', '~> 4.3.0'
  pod 'MJExtension'
  pod 'SSZipArchive'
  pod 'Masonry'
  pod 'AWSCore'
  pod 'AWSS3'
  pod 'AWSDynamoDB'
  
  pod 'UMCommon'
  pod 'UMDevice'
  pod 'UMAPM'
  
  pod 'CocoaHTTPServer', :git =>'https://github.com/chbeer/CocoaHTTPServer'
  pod 'PhoneNetSDK'
  pod 'HMSegmentedControl'
  pod 'BlocksKit', :git =>'https://github.com/Tioks/BlocksKit'#block回调简化
  pod 'SVProgressHUD'
  pod 'YYModel'
  pod 'IQKeyboardManager'
  pod 'Ads-CN'#广告
  pod 'FBSDKCoreKit'
  pod 'FBSDKLoginKit'
  pod 'GoogleSignIn'
  
  pod 'SSZipArchive'#解压
#  pod 'BUAdTestMeasurement'
  use_frameworks!
end
post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'

    end
  end
end
