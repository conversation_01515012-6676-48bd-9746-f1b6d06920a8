<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="16097" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina5_5" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="LaunchScreen.jpg" translatesAutoresizingMaskIntoConstraints="NO" id="e35-Og-4hC">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
                                <color key="backgroundColor" red="0.050526060159999997" green="0.48335200550000001" blue="0.7725818753" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="e35-Og-4hC" firstAttribute="trailing" secondItem="6Tk-OE-BBY" secondAttribute="trailing" id="B2g-IM-uPN"/>
                            <constraint firstAttribute="bottom" secondItem="e35-Og-4hC" secondAttribute="bottom" id="m2s-xM-Adu"/>
                            <constraint firstItem="e35-Og-4hC" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" id="rqy-qi-srN"/>
                            <constraint firstItem="e35-Og-4hC" firstAttribute="top" secondItem="Ze5-6b-2t3" secondAttribute="top" id="ytn-u0-vCn"/>
                        </constraints>
                        <viewLayoutGuide key="safeArea" id="6Tk-OE-BBY"/>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="52.173913043478265" y="375"/>
        </scene>
    </scenes>
    <resources>
        <image name="LaunchScreen.jpg" width="750" height="1334"/>
    </resources>
</document>
