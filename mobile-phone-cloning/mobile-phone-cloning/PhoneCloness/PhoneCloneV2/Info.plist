<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AWS</key>
	<dict>
		<key>CredentialsProvider</key>
		<dict>
			<key>CognitoIdentity</key>
			<dict>
				<key>Default</key>
				<dict>
					<key>PoolId</key>
					<string>us-east-1:048ee593-3b2b-495a-bd4e-6eb429a44ebc</string>
					<key>Region</key>
					<string>us-east-1</string>
				</dict>
			</dict>
		</dict>
		<key>DynamoDB</key>
		<dict>
			<key>Default</key>
			<dict>
				<key>Region</key>
				<string>us-east-1</string>
			</dict>
		</dict>
		<key>S3</key>
		<dict>
			<key>Default</key>
			<dict>
				<key>Region</key>
				<string>us-east-1</string>
			</dict>
		</dict>
		<key>S3TransferUtility</key>
		<dict>
			<key>Default</key>
			<dict>
				<key>Region</key>
				<string>us-east-1</string>
			</dict>
		</dict>
	</dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSApplicationCategoryType</key>
	<string></string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>To transfer your music</string>
	<key>NSBonjourServices</key>
	<array>
		<string>_rsp-sender._udp</string>
		<string>_rsp-sender._tcp</string>
		<string>_rsp-receiver._udp</string>
		<string>_rsp-receiver._tcp</string>
	</array>
	<key>NSCalendarsUsageDescription</key>
	<string>To transfer your calendars</string>
	<key>NSCameraUsageDescription</key>
	<string>Access your camera to scan qr code</string>
	<key>NSContactsUsageDescription</key>
	<string>To transfer your contacts</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>To transfer your data to other device via local network</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>To transfer your photos &amp; videos or upload to cloudstorage</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>To transfer your photos &amp; videos or upload to cloudstorage</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>需要获取您设备的广告标识符，以偏于提供更好的广告服务</string>
	<key>SKAdNetworkItems</key>
	<array>
		<dict>
			<key>SKAdNetworkIdentifier</key>
			<string>238da6jt44.skadnetwork</string>
		</dict>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
</dict>
</plist>
