//
//  SendListController.m
//  mobiletransfer
//
//  Created by fs0011 on 2023/12/5.
//  Copyright © 2023 Qi<PERSON><PERSON> Chen. All rights reserved.
//

#import "SendListController.h"
#import "File.h"
#import <Photos/Photos.h>
#import "SendFileCellView.h"
#import "UIViewController+DismissExtensions.h"
@interface SendListController ()<UITableViewDelegate,UITableViewDataSource>
@property UIScrollView* sc;
@property NSMutableArray* cells;
@property NSMutableArray* files;
@end

@implementation SendListController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor colorWithHexString:@"#FAFAFA" alpha:1];
    self.sc = [[UIScrollView alloc]initWithFrame:CGRectZero];
    [self.view addSubview:self.sc];
    
    [self.sc mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.top.mas_equalTo(self.view.mas_topMargin);
        make.bottom.mas_equalTo(self.view.mas_bottomMargin);
    }];
    self.cells = [NSMutableArray array];
    self.files = [NSMutableArray array];
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(sendfile:) name:@"sendfile" object:nil];
    
    
    //之前没发送成功的记录
    if([[FileManager share]getsendFileList].count>0)
    {
        AppDelegate* app = APPDELEGATE;
        NSMutableArray* addList = [NSMutableArray array];
        NSMutableArray* newSendList = [[FileManager share]getsendFileList];
        for (File* f in newSendList) {
            if  (f.targetName == app.currentDeviceName){
                [addList addObject:f];
            }
        }
        if (addList.count>0) {
            
            [newSendList removeObjectsInArray:addList];
            [[FileManager share]setSendFilelist:newSendList];
            NSNotification* noti = [NSNotification notificationWithName:@"sendfile" object:addList];
            [self sendfile:noti];
        }
    }
    // Do any additional setup after loading the view.
}


- (void)sendfile:(NSNotification*)noti
{
    NSArray* arr = noti.object;
    CGFloat bottom = 4+self.cells.count*(88+8);
    
    
    for (File* file in arr) {
        SendFileCellView* view =  [[SendFileCellView alloc]initWithFile:file];
        [self.sc addSubview:view];
        [view layoutIfNeeded];
        [view mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(16);
            make.top.mas_equalTo(bottom+8);
        }];
        [view.superview layoutIfNeeded];
        bottom = view.bottom;
        [self getfileData:file complete:^(NSURL *url) {
            [self posturl:url inView:view fileType:file.type];
            
        }];
        [self.cells addObject:view];
        [self.files addObject:file];
        
    }
    self.sc.contentSize = CGSizeMake(kDeviceWidth, bottom+20);
}




- (void)posturl:(NSURL*)url inView:(SendFileCellView*)inview fileType:(NSInteger)type
{
    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    AppDelegate* app = APPDELEGATE;
    
    // 设置请求序列化器
    manager.requestSerializer = [AFHTTPRequestSerializer serializer];
    manager.responseSerializer = [AFJSONResponseSerializer serializer];
    UIDevice *currentDevice = [UIDevice currentDevice];
    NSString *deviceName = currentDevice.name;
    // 创建请求
    [manager.requestSerializer setValue:app.currentLinkToken forHTTPHeaderField:@"authorizationToken"];
    [manager.requestSerializer setValue:[NSString stringWithFormat:@"%ld",type] forHTTPHeaderField:@"fileType"];
    [manager.requestSerializer setValue:deviceName forHTTPHeaderField:@"deviceName"];
    NSMutableSet *acceptableContentTypes = [NSMutableSet setWithSet:manager.responseSerializer.acceptableContentTypes];
    [acceptableContentTypes addObject:@"text/html"];
    [acceptableContentTypes addObject:@"text/plain"];
    manager.responseSerializer.acceptableContentTypes = acceptableContentTypes;
    NSLog(@"%@",[NSString stringWithFormat:@"%@/upload",app.currentLinkUrl]);
    NSString* urlLink = [NSString stringWithFormat:@"%@/upload",app.currentLinkUrl];
    if(![app.currentLinkUrl containsString:@"http"])
    {
        urlLink = [NSString stringWithFormat:@"http://%@/upload",app.currentLinkUrl];
    }
    NSURLSessionDataTask* task = [manager POST:urlLink  parameters:nil headers:nil constructingBodyWithBlock:^(id<AFMultipartFormData>  _Nonnull formData) {
        // 添加文件
        [formData appendPartWithFileURL:url name:@"file" error:nil];
    } progress:^(NSProgress * _Nonnull uploadProgress) {
        double progressPercentage = uploadProgress.fractionCompleted * 100;
        dispatch_async(dispatch_get_main_queue(), ^{
            [inview updatePrecent:progressPercentage];
            if(progressPercentage>=100)
            {
                
                [inview complete];
            }
        });
        
        
        
    } success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [inview complete];
            NSLog(@"完成");
        });
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        NSLog(@"%ld",error.code);
        
        if(error.code==-1004)
        {
            [SVProgressHUD showInfoWithStatus:local(@"对方连接中断请尝试重新加入")];
            [self backIndex];
        }
        dispatch_async(dispatch_get_main_queue(), ^{
            [inview fail];
        });
    }];
    
    [inview updateIcon:url];
    
    inview.cancelCallback = ^{
        [MobClick event:@"Transmit" attributes:@{@"Send_a_list":@"取消传输"}];
        [task cancel];
    };
    
    WeakObj(inview)
    inview.resentCallback = ^{
        [self posturl:url inView:inviewWeak fileType:type];
    };
    
    inview.openCallback = ^{
        [MobClick event:@"Transmit" attributes:@{@"Send_a_list":@"查看"}];
        dispatch_async(dispatch_get_main_queue(), ^{
            NSArray *activityItems = @[url];
            UIActivityViewController *activityViewController = [[UIActivityViewController alloc] initWithActivityItems:activityItems applicationActivities:nil];
            activityViewController.excludedActivityTypes = @[UIActivityTypeMessage, UIActivityTypePostToFacebook, UIActivityTypePostToTwitter];
            activityViewController.popoverPresentationController.sourceView = self.view;
            activityViewController.popoverPresentationController.sourceRect = CGRectMake(CGRectGetMidX(self.view.bounds), CGRectGetMidY(self.view.bounds), 0, 0);
            
            [self presentViewController:activityViewController animated:YES completion:nil];
        });
    };
}



- (void)getfileData:(File*)file complete:(void(^)(NSURL *imageData))complete;
{
    if(file.type == 2||file.type == 4)
    {
        PHFetchResult *assets = [PHAsset fetchAssetsWithLocalIdentifiers:@[file.AlbumId] options:nil];
        PHAsset *asset = assets.firstObject;
        if (asset.mediaType == PHAssetMediaTypeImage) {
            // 这是一张图片
            PHImageRequestOptions *options = [[PHImageRequestOptions alloc] init];
            options.networkAccessAllowed = YES; // 允许从网络下载
            
            [[PHImageManager defaultManager] requestImageDataForAsset:asset options:options resultHandler:^(NSData *imageData, NSString *dataUTI, UIImageOrientation orientation, NSDictionary *info) {
                // 在这里处理获取到的 imageData
                [self completeWithData:imageData completion:^(NSString * pathStr) {
                    complete([NSURL fileURLWithPath:pathStr]);
                }];
                
            }];
        } else if (asset.mediaType == PHAssetMediaTypeVideo) {
            // 这是一个视频
            PHVideoRequestOptions *options = [[PHVideoRequestOptions alloc] init];
            options.networkAccessAllowed = YES; // 允许从网络下载
            
            [[PHImageManager defaultManager] requestAVAssetForVideo:asset options:options resultHandler:^(AVAsset *avAsset, AVAudioMix *audioMix, NSDictionary *info) {
                // 在这里处理获取到的 AVAsset
                // 您可能需要检查 avAsset 类型是否为 AVURLAsset 来获取视频文件的 URL
                if ([avAsset isKindOfClass:[AVURLAsset class]]) {
                    AVURLAsset *urlAsset = (AVURLAsset *)avAsset;
                    NSURL *videoURL = urlAsset.URL;
                    
                    // 从 URL 获取视频数据
                    NSData *videoData = [NSData dataWithContentsOfURL:videoURL];
                    // 现在 videoData 包含了视频文件的数据
                    complete(videoURL);
                }
            }];
        }
        NSArray<PHAssetResource *> *resources = [PHAssetResource assetResourcesForAsset:asset];
        NSString *fileName = resources.firstObject.originalFilename;
    }else
    {
        complete([NSURL fileURLWithPath:file.path]);
    }
}


- (void)completeWithData:(NSData *)imageData completion:(void (^)(NSString *))completionBlock {
    // 确定文件保存位置
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    NSString *documentsDirectory = [paths objectAtIndex:0];
    NSString *ditpath = [documentsDirectory stringByAppendingPathComponent:@"cacheFile"];
    
    NSError *error = nil;
    if (![[NSFileManager defaultManager] fileExistsAtPath:ditpath]) {
        [[NSFileManager defaultManager] createDirectoryAtPath:ditpath withIntermediateDirectories:YES attributes:nil error:&error];
        if (error) {
            // 处理错误
        }
    }
    
    NSString *filePath = [documentsDirectory stringByAppendingPathComponent:[NSString stringWithFormat:@"%lf_Image.png", [[NSDate date] timeIntervalSince1970]]];
    // 自定义文件名
    
    // 将图片数据写入文件
    [imageData writeToFile:filePath options:NSDataWritingAtomic error:&error];
    
    if (error) {
        NSLog(@"Error saving image: %@", error);
        completionBlock(nil); // 处理错误
    } else {
        completionBlock(filePath); // 返回文件路径
    }
}
/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */
//关闭页面的时候记录上次没有传输成功的数据
- (void)close
{
    for (File* file in self.files) {
        if(!file.isCompelete)
        {
            [[FileManager share]addSendFile:file];
        }
    }
}
- (void)backIndex
{
    
    for (File* file in self.files) {
        if(!file.isCompelete)
        {
            [[FileManager share]addSendFile:file];
        }
    }
    
    [[NSNotificationCenter defaultCenter]removeObserver:self];
    [[UIViewController getCurrentVC] dismissToRootViewController];
    [[httpNework shareNetwork] closeServer];
}

-(void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    NSLog(@"delloc");
}
@end
