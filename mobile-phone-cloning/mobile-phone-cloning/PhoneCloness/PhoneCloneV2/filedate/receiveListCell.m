//
//  receiveListCell.m
//  mobiletransfer
//
//  Created by fs0011 on 2023/12/12.
//  Copyright © 2023 <PERSON><PERSON><PERSON>. All rights reserved.
//

#import "receiveListCell.h"
#import <AVFoundation/AVFoundation.h>
@interface receiveListCell ()
@property UIView* bac;
@property UIImageView* imv;
@property UILabel* fileLable;
@property UILabel* receiveLeble;
@property UIButton* openBtn;

@property NSDictionary* FileData;
@end

@implementation receiveListCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if(self==[super initWithStyle:style reuseIdentifier:reuseIdentifier])
    {
        self.bac =  [UIView new];
        self.bac.backgroundColor = [UIColor whiteColor];
        self.contentView.backgroundColor = [UIColor colorWithHexString:@"#FAFAFA" alpha:1];
        [self.contentView addSubview:self.bac];
        [self.bac mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(16);
            make.right.mas_equalTo(-16);
            make.height.mas_equalTo(88);
            make.centerY.mas_equalTo(0);
        }];
        
        self.imv = [UIImageView new];
        [self.bac addSubview:self.imv];
        [self.imv mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(16);
            make.centerY.mas_equalTo(0);
            make.width.height.mas_equalTo(36);
        }];
        
        self.fileLable = [UILabel createLabelWithTitle:@" " textColor:[UIColor colorWithHexString:@"333333" alpha:1] textAlignment:NSTextAlignmentLeft font:[UIFont systemFontOfSize:14 weight:UIFontWeightMedium]];
        self.fileLable.numberOfLines = 2;
        [self.bac addSubview:self.fileLable];
        [self.fileLable mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(self.imv.mas_right).offset(12);
            make.centerY.mas_equalTo(0);
            make.right.mas_equalTo(-96);
            
        }];
        
        self.receiveLeble = [UILabel createLabelWithTitle:local(@"已完成") textColor:[UIColor colorWithHexString:@"#47BC9E" alpha:1] textAlignment:NSTextAlignmentRight font:[UIFont systemFontOfSize:10 weight:UIFontWeightMedium]];
        [self.bac addSubview:self.receiveLeble];
        [self.receiveLeble mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.mas_equalTo(-16);
            make.top.mas_equalTo(23);
        }];
        
        self.openBtn = [UIButton createButtonWithTitle:local(@"查看") color:[UIColor whiteColor] font:[UIFont systemFontOfSize:10 weight:UIFontWeightMedium]];
        self.openBtn.layer.cornerRadius = 2;
        self.openBtn.layer.masksToBounds = YES;
        self.openBtn.backgroundColor = [UIColor colorWithHexString:@"#47BC9E" alpha:1];
        [self.bac addSubview:self.openBtn];
        [self.openBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.mas_equalTo(-32);
            make.bottom.mas_equalTo(-12);
            make.width.mas_equalTo(32);
            make.height.mas_equalTo(16);
        }];
        [self.openBtn bk_whenTapped:^{
            self.openCallback();
        }];
    }
    return self;
}

- (void)loadData:(NSDictionary*)dic
{
    self.FileData = dic;
    NSArray* tyepim = @[@"未知文件",@"未知文件",@"照片文件",@"音乐文件",@"视频文件",@"未知文件"];
    [self.imv setImage:[UIImage imageNamed:tyepim[[dic[@"fileType"] intValue]]]];
    
    self.fileLable.text = [dic[@"file"] lastPathComponent];
    NSString *uploadDirPath = [[NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject] stringByAppendingPathComponent:@"receive"];
    NSString* urlStr = [uploadDirPath stringByAppendingPathComponent:[dic[@"file"]lastPathComponent]];
    [self updateIcon:[NSURL fileURLWithPath:urlStr]];
}



- (void)updateIcon:(NSURL*)url
{
    NSString *extension = [url.pathExtension lowercaseString];
    
    if ([extension isEqualToString:@"jpg"] || [extension isEqualToString:@"png"] || [extension isEqualToString:@"jpeg"]) {
        // It's an image
        NSData *imageData = [NSData dataWithContentsOfURL:url];
        UIImage *image = [UIImage imageWithData:imageData];
        [self.imv setImage:image];
    } else if ([extension isEqualToString:@"mp4"] || [extension isEqualToString:@"mov"] || [extension isEqualToString:@"avi"]) {
        // It's a video
        AVAsset *asset = [AVAsset assetWithURL:url];
        AVAssetImageGenerator *imageGenerator = [[AVAssetImageGenerator alloc] initWithAsset:asset];
        CGImageRef thumbnailImageRef = NULL;
        NSError *error = nil;
        thumbnailImageRef = [imageGenerator copyCGImageAtTime:CMTimeMake(1, 60) actualTime:NULL error:&error];
        
        if (!thumbnailImageRef) {
            NSLog(@"Thumbnail image generation error: %@", error);
        } else {
            UIImage *thumbnailImage = [UIImage imageWithCGImage:thumbnailImageRef];
            [self.imv setImage:thumbnailImage];
            CGImageRelease(thumbnailImageRef);
        }
    } else {
        // URL is not for an image or a video, do nothing
        // Optionally, reset to a default image or state
    }
}
/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */

@end
