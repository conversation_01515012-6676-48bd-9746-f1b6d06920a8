//
//  FileDateViewController.m
//  mobiletransfer
//
//  Created by fs0011 on 2023/12/5.
//  Copyright © 2023 <PERSON><PERSON><PERSON>. All rights reserved.
//

#import "FileDateViewController.h"
#import "MySegmentedControl.h"
#import "mainSendFileViewController.h"
#import "SendListController.h"
#import "ReceiveLIstController.h"
#import "UIViewController+DismissExtensions.h"
@interface FileDateViewController ()

@property MySegmentedControl* mysegment;
@property UIView* natMainSendView;
@property UIView* mainView;
@property UIView* chooseFileView;
@property UIView* sendListView;
@property UIView* reciveListView;

@property SendListController* list;
@end

@implementation FileDateViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor whiteColor];
    [self loadNatView];
    [self loadSegement];
    [self loadUI];
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(sendfile:) name:@"sendfile" object:nil];
    // Do any additional setup after loading the view.
}

- (void)loadNatView{
    self.natMainSendView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kDeviceWidth,64+KDNavH)];
    [self.view addSubview:self.natMainSendView];
    
    UIButton *backButton = [[UIButton alloc]initWithFrame:CGRectMake(15, 25+KDNavH, 30, 30)];
    [backButton setImage:[UIImage imageNamed:@"black_back_icn"] forState:UIControlStateNormal];
    [backButton addTarget:self action:@selector(backMainAction) forControlEvents:UIControlEventTouchUpInside];
    [self.natMainSendView addSubview:backButton];
    
    UILabel *titleLabel = [[UILabel alloc]initWithFrame:CGRectMake(kDeviceWidth/2-50,25+KDNavH,100, 30)];
    titleLabel.text = NSLocalizedString(@"传输",nil);
    titleLabel.textColor = [UIColor colorWithHexString:@"black_back_icn" alpha:1];
    titleLabel.font = [UIFont systemFontOfSize:15];
    titleLabel.textAlignment = NSTextAlignmentCenter;
    [self.view addSubview:titleLabel];
    
    
}



- (void)backMainAction
{
    [self backIndex];
}

- (void)loadUI
{
    UIView *bottom = [UIView new];
    bottom.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:bottom];
    [bottom mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.bottom.mas_equalTo(self.view.mas_bottomMargin);
        make.height.mas_equalTo(16*3);
    }];
    
    UILabel* link = [UILabel createLabelWithTitle:[NSString stringWithFormat:local(@"与%@连接中"),self.targetName] textColor:[UIColor colorWithHexString:@"333333" alpha:1] textAlignment:NSTextAlignmentLeft font:[UIFont systemFontOfSize:14]];
    [bottom addSubview:link];
    [link mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(0);
        make.left.mas_equalTo(16);
    }];
    NSMutableAttributedString* atr = [[NSMutableAttributedString alloc]initWithString:link.text];
    [atr addAttributes:@{NSForegroundColorAttributeName:[UIColor colorWithHexString:@"#4DD0A7" alpha:1]} range:[link.text rangeOfString:self.targetName]];
    link.attributedText  =  atr;
    
    UIButton* btn = [UIButton createButtonWithTitle:local(@"断开") color:[UIColor whiteColor] font:[UIFont systemFontOfSize:14]];
    btn.backgroundColor = [UIColor colorWithHexString:@"#4DD0A7" alpha:1];
    btn.layer.cornerRadius = 2;
    btn.layer.masksToBounds = YES;
    [bottom addSubview:btn];
    [btn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(0);
        make.right.mas_equalTo(-24);
        make.width.mas_equalTo(48);
        make.height.mas_equalTo(16);
    }];
    [btn bk_whenTapped:^{
        [self backIndex];
    }];
    
    UIView* mainView = [UIView new];
    mainView.backgroundColor = [UIColor colorWithHexString:@"#FAFAFA" alpha:1];
    [self.view addSubview:mainView];
    [mainView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.top.mas_equalTo(self.mysegment.mas_bottom).offset(20);
        make.bottom.mas_equalTo(bottom.mas_top);
    }];
    self.mainView = mainView;
    
    [self.mainView.superview layoutIfNeeded];
    
    mainSendFileViewController* send = [mainSendFileViewController new];
    send.view.frame = self.mainView.bounds;
    [self addChildViewController:send];
    [self.mainView addSubview:send.view];
    self.chooseFileView = send.view;
    
    
    SendListController* send1 = [SendListController new];
    send1.view.frame = self.mainView.bounds;
    [self addChildViewController:send1];
    [self.mainView addSubview:send1.view];
    self.sendListView = send1.view;
    self.list = send1;
    
    ReceiveLIstController * send2 = [ReceiveLIstController new];
    send2.view.frame = self.mainView.bounds;
    [self addChildViewController:send2];
    [self.mainView addSubview:send2.view];
    self.reciveListView = send2.view;
    [self.mainView bringSubviewToFront:self.chooseFileView];
    
}
- (void)loadSegement
{
    MySegmentedControl* segment = [[MySegmentedControl alloc]initWithItems:@[NSLocalizedString(@"选择文件", nil),NSLocalizedString(@"发送列表", nil),NSLocalizedString(@"接收列表", nil)]];
    segment.cornerRadius = 44.0*0.5;
    segment.layer.masksToBounds = YES;
    segment.selectedSegmentIndex = 0;
    segment.backgroundColor = [[UIColor whiteColor]colorWithAlphaComponent:0.3];
    [self.view addSubview:segment];
    segment.tintColor = [UIColor colorWithHexString:@"#4DD0A7" alpha:1];;
    [segment setTitleTextAttributes:@{NSForegroundColorAttributeName:[UIColor colorWithHexString:@"#4DD0A7" alpha:1],NSFontAttributeName:[UIFont systemFontOfSize:14 weight:UIFontWeightMedium]} forState:UIControlStateNormal];
    [segment setTitleTextAttributes:@{NSForegroundColorAttributeName:[UIColor whiteColor],NSFontAttributeName:[UIFont systemFontOfSize:14 weight:UIFontWeightMedium]} forState:UIControlStateSelected];
    [segment mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo((kDeviceWidth-16*2)*1.0/3*segment.numberOfSegments);
        make.height.mas_equalTo(44);
        make.top.mas_equalTo(self.natMainSendView.mas_bottom).offset(20);
        make.centerX.mas_equalTo(0);
    }];
    
    [segment bk_addEventHandler:^(id  _Nonnull sender) {
        switch (segment.selectedSegmentIndex) {
            case 0:
            {
                [MobClick event:@"Transmit" attributes:@{@"Source":@"选择文件"}];
                [self.mainView bringSubviewToFront:self.chooseFileView];
            }
                break;
            case 1:
            {
                [MobClick event:@"Transmit" attributes:@{@"Source":@"发送列表"}];
                [self.mainView bringSubviewToFront:self.sendListView];
            }
                break;
            case 2:
            {
                [MobClick event:@"Transmit" attributes:@{@"Source":@"接收列表"}];
                [self.mainView bringSubviewToFront:self.reciveListView];
            }
                break;
                
            default:
                break;
        }
    } forControlEvents:UIControlEventValueChanged];
    [self.mainView bringSubviewToFront:self.chooseFileView];
    self.mysegment = segment;
}


- (void)sendfile:(NSNotification*)noti
{
    self.mysegment.selectedSegmentIndex = 1;
    [self.mainView bringSubviewToFront:self.sendListView];
    
}

- (void)backIndex
{
    [self.list close];
    [[NSNotificationCenter defaultCenter]removeObserver:self.list];
    self.list = nil;
    [self removeChildViewController];
    self.chooseFileView = nil;
    self.sendListView = nil;
    self.reciveListView = nil;
    
    [[NSNotificationCenter defaultCenter]removeObserver:self];
    [self dismissToRootViewController];
    [[httpNework shareNetwork] closeServer];
}


- (void)removeChildViewController {
    // 移除子视图控制器的视图
    [self.sendListView removeFromSuperview];
    [self.reciveListView removeFromSuperview];
    [self.chooseFileView removeFromSuperview];
    
    // 从父视图控制器中移除子视图控制器
    [self.childViewControllers enumerateObjectsUsingBlock:^(__kindof UIViewController * _Nonnull childVC, NSUInteger idx, BOOL * _Nonnull stop) {
        [childVC willMoveToParentViewController:nil];
        [childVC removeFromParentViewController];
    }];
}


/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */

@end
