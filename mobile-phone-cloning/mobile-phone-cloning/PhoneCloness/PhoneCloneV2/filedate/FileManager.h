//
//  FileManager.h
//  mobiletransfer
//
//  Created by fs0011 on 2023/12/12.
//  Copyright © 2023 <PERSON><PERSON><PERSON>. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "File.h"
NS_ASSUME_NONNULL_BEGIN

@interface FileManager : NSObject

@property NSString* needSaveFilePath;
+ (instancetype)share;
- (NSMutableArray*)getreceiveFileList;
- (void)addReceiveFile:(NSDictionary*)file;
- (NSMutableArray*)getsendFileList;
- (void)addSendFile:(File*)file;
- (void)setSendFilelist:(NSMutableArray*)arr;
//收到的文件的处理
- (void)completeGetFile:(NSDictionary*)dic;
@end

NS_ASSUME_NONNULL_END
