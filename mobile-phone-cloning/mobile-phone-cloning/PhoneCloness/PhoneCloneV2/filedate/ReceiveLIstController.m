//
//  ReceiveLIstController.m
//  mobiletransfer
//
//  Created by fs0011 on 2023/12/12.
//  Copyright © 2023 <PERSON><PERSON><PERSON>. All rights reserved.
//

#import "ReceiveLIstController.h"
#import "receiveListCell.h"
@interface ReceiveLIstController ()<UITableViewDelegate,UITableViewDataSource>
@property UIView* natMainSendView;
@property NSMutableArray* dataArray;
@property UITableView* tableView;
@end

@implementation ReceiveLIstController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.dataArray = [NSMutableArray array];
    if(self.isSingelPage)
    {
        [self loadNatView];
    }
    
    [self loadUI];
    
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(updateFileList:) name:@"updateFileList" object:nil];
    // Do any additional setup after loading the view.
}
- (void)loadNatView{
    self.view.backgroundColor = [UIColor colorWithHexString:@"#FAFAFA" alpha:1];
    self.natMainSendView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kDeviceWidth,64+KDNavH)];
    [self.view addSubview:self.natMainSendView];
    
    UIButton *backButton = [[UIButton alloc]initWithFrame:CGRectMake(15, 25+KDNavH, 30, 30)];
    [backButton setImage:[UIImage imageNamed:@"black_back_icn"] forState:UIControlStateNormal];
    [backButton addTarget:self action:@selector(backMainAction) forControlEvents:UIControlEventTouchUpInside];
    [self.natMainSendView addSubview:backButton];
    
    UILabel *titleLabel = [[UILabel alloc]initWithFrame:CGRectMake(kDeviceWidth/2-50,25+KDNavH,100, 30)];
    titleLabel.text = NSLocalizedString(@"接收",nil);
    titleLabel.textColor = [UIColor colorWithHexString:@"333333" alpha:1];
    titleLabel.font = [UIFont systemFontOfSize:15];
    titleLabel.textAlignment = NSTextAlignmentCenter;
    [self.view addSubview:titleLabel];
    
    
}

- (void)loadUI
{
    UITableView* table =  [[UITableView alloc]initWithFrame:CGRectZero style:UITableViewStylePlain];
    [self.view addSubview:table];
    [table mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.mas_equalTo(0);
        if(self.natMainSendView)
        {
            make.top.mas_equalTo(self.natMainSendView.mas_bottom);
        }
        else
        {
            make.top.mas_equalTo(0);
        }
    }];
    self.tableView  = table;
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.tableHeaderView = [UIView new];
    self.tableView.tableFooterView = [UIView new];
    self.tableView.separatorStyle =  UITableViewCellSeparatorStyleNone;
    [self updateFileList:nil];
}


- (void)backMainAction
{
    [self dismissViewControllerAnimated:YES completion:^{
        
    }];
}

- (void)updateFileList:(NSNotification*)noti
{
    [MobClick event:@"Transmit" attributes:@{@"Receive_lists":@"接收成功"}];
    NSString *uploadDirPath = [[NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject] stringByAppendingPathComponent:@"receive"];
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error = nil;
    NSArray<NSString *> *fileNames = [fileManager contentsOfDirectoryAtPath:uploadDirPath error:&error];
    self.dataArray = [[FileManager share]getreceiveFileList];
    NSMutableArray* arr = [NSMutableArray array];
    if (error == nil) {
        // 成功获取文件列表
        for (NSString *fileName in fileNames) {
            NSString *filePath = [uploadDirPath stringByAppendingPathComponent:fileName];
            [arr addObject:@{@"file":filePath,@"deviceName":@"",@"fileType":@"0"}];
            // 在这里处理每个文件的路径 (filePath)
        }
    } else {
        // 获取文件列表时发生错误
        NSLog(@"Error: %@", error);
    }
    self.dataArray  =  arr;
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.tableView reloadData];
    });
    
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.dataArray.count;
}

- (UITableViewCell*)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    receiveListCell* cell = [tableView dequeueReusableCellWithIdentifier:@"cell"];
    if(!cell)
    {
        cell = [[receiveListCell alloc]initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"cell"];
    }
    NSDictionary* dic = self.dataArray[indexPath.row];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    [cell loadData:dic];
    cell.openCallback = ^{
        [MobClick event:@"Transmit" attributes:@{@"Receive_lists":@"查看"}];
        dispatch_async(dispatch_get_main_queue(), ^{
            
            NSString *uploadDirPath = [[NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject] stringByAppendingPathComponent:@"receive"];
            NSString* urlStr = [uploadDirPath stringByAppendingPathComponent:[dic[@"file"]lastPathComponent]];
            
            NSURL* url = [NSURL fileURLWithPath:urlStr];
            NSArray *activityItems = @[url];
            UIActivityViewController *activityViewController = [[UIActivityViewController alloc] initWithActivityItems:activityItems applicationActivities:nil];
            activityViewController.excludedActivityTypes = @[UIActivityTypeMessage, UIActivityTypePostToFacebook, UIActivityTypePostToTwitter];
            activityViewController.popoverPresentationController.sourceView = self.view;
            activityViewController.popoverPresentationController.sourceRect = CGRectMake(CGRectGetMidX(self.view.bounds), CGRectGetMidY(self.view.bounds), 0, 0);

            [self presentViewController:activityViewController animated:YES completion:nil];
        });
    };
    return cell;
}
-(void)viewWillDisappear:(BOOL)animated
{
    
}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 88+16;
}


-(void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    NSLog(@"delloc");
}
/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */

@end
