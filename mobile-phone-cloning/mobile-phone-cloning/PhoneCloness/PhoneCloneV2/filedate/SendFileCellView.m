//
//  SendFileCellView.m
//  mobiletransfer
//
//  Created by fs0011 on 2023/12/6.
//  Copyright © 2023 Qi<PERSON><PERSON>. All rights reserved.
//

#import "SendFileCellView.h"
#import <AVFoundation/AVFoundation.h>
@implementation SendFileCellView
{
    File* _file;
    UIButton* _statusBtn;
    UILabel* _precentLabel;
    UIProgressView* _progress;
    UIImageView* _im;
}
- (instancetype)initWithFile:(File*)file
{
    if (self==[super init]) {
        _file = file;
        self.backgroundColor = [UIColor whiteColor];
        self.layer.cornerRadius = 12;
        self.layer.masksToBounds = YES;
        [self mas_makeConstraints:^(MASConstraintMaker *make) {
            
            make.width.mas_equalTo(kDeviceWidth-16*2);
            make.height.mas_equalTo(88);
        }];
        [self layout];
        
    }
    return self;
}

- (void)layout
{
    NSArray* tyepim = @[@"未知文件",@"未知文件",@"照片文件",@"音乐文件",@"视频文件",@"未知文件"];
    
    UIImageView* im = [UIImageView createSizeFitImageviewName:tyepim[_file.type]];
    [self addSubview:im];
    [im mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(16);
        make.top.mas_equalTo(12);
        make.width.height.mas_equalTo(36);
    }];
    _im  = im;
    if(_file.path)
    {
        UILabel* name = [UILabel createLabelWithTitle:_file.path.lastPathComponent textColor:[UIColor colorWithHexString:@"333333" alpha:1] textAlignment:NSTextAlignmentLeft font:[UIFont systemFontOfSize:14]];
        name.numberOfLines = 2;
        [self addSubview:name];
        [name mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(im);
            make.left.mas_equalTo(im.mas_right).offset(12);
            make.right.mas_equalTo(-96);
        }];
    }
    
    UIProgressView * slider = [UIProgressView new];
    slider.backgroundColor = [UIColor colorWithHexString:@"#47BC9E" alpha:0.24];
    slider.layer.cornerRadius = 2;
    slider.layer.masksToBounds  = YES;
    slider.tintColor = [UIColor colorWithHexString:@"#47BC9E" alpha:1];
    [self addSubview:slider];
    [slider mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(16);
        make.bottom.mas_equalTo(-12);
        make.height.mas_equalTo(16);
        make.right.mas_equalTo(-64);
    }];
    _progress =  slider;
    
    AppDelegate* app = APPDELEGATE;
    UILabel* sendLabel = [UILabel createLabelWithTitle:[NSString stringWithFormat:local(@"发送给%@"),_file.targetName] textColor:[UIColor colorWithHexString:@"#47BC9E" alpha:1] textAlignment:NSTextAlignmentRight font:[UIFont systemFontOfSize:10 weight:UIFontWeightMedium]];
    [self addSubview:sendLabel];
    [sendLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(_im);
        make.right.mas_equalTo(-16);
    }];
    
    UIButton* btn = [UIButton createButtonWithTitle:local(@"取消") color:[UIColor whiteColor] font:[UIFont systemFontOfSize:10 weight:UIFontWeightMedium]];
    btn.backgroundColor = [UIColor colorWithHexString:@"#47BC9E" alpha:1];
    btn.layer.cornerRadius = 2;
    btn.layer.masksToBounds = YES;
    [self  addSubview:btn];
    [btn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(-16);
        make.centerY.mas_equalTo(slider);
        make.width.mas_equalTo(36);
        make.height.mas_equalTo(16);
    }];
    _statusBtn = btn;
    [btn bk_whenTapped:^{
        if([btn.titleLabel.text isEqualToString:@"取消"])
        {
            self.cancelCallback();
        }
        if([btn.titleLabel.text isEqualToString:@"重发"])
        {
            self.resentCallback();
        }
        if([btn.titleLabel.text isEqualToString:@"查看"])
        {
            self.openCallback();
        }
    }];
    
    UILabel* precent = [UILabel createLabelWithTitle:@"0.00%" textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentCenter font:[UIFont systemFontOfSize:8]];
    [self addSubview:precent];
    [precent mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.centerY.mas_equalTo(slider);
    }];
    _precentLabel = precent;
    
}
/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */

- (void)fail {
    [_statusBtn setTitle:local(@"重发") forState:0];
    
}

- (void)updatePrecent:(CGFloat)precent {
    
    
    _progress.progress = precent*1.0/100;
    _precentLabel.text = [NSString stringWithFormat:@"%.2lf%%",precent];
    _file.precent  = precent*1.0/100;
}

- (void)complete {
    [_statusBtn setTitle:local(@"查看") forState:0];
    _progress.hidden = YES;
    _precentLabel.hidden = YES;
    _file.isCompelete = YES;
    [MobClick event:@"Transmit" attributes:@{@"Send_a_list":@"发送成功"}];
}

- (void)updateIcon:(NSURL*)url
{
    NSString *extension = [url.pathExtension lowercaseString];
    
    if ([extension isEqualToString:@"jpg"] || [extension isEqualToString:@"png"] || [extension isEqualToString:@"jpeg"]) {
        // It's an image
        NSData *imageData = [NSData dataWithContentsOfURL:url];
        UIImage *image = [UIImage imageWithData:imageData];
        [_im setImage:image];
    } else if ([extension isEqualToString:@"mp4"] || [extension isEqualToString:@"mov"] || [extension isEqualToString:@"avi"]) {
        // It's a video
        AVAsset *asset = [AVAsset assetWithURL:url];
        AVAssetImageGenerator *imageGenerator = [[AVAssetImageGenerator alloc] initWithAsset:asset];
        CGImageRef thumbnailImageRef = NULL;
        NSError *error = nil;
        thumbnailImageRef = [imageGenerator copyCGImageAtTime:CMTimeMake(1, 60) actualTime:NULL error:&error];
        
        if (!thumbnailImageRef) {
            NSLog(@"Thumbnail image generation error: %@", error);
        } else {
            UIImage *thumbnailImage = [UIImage imageWithCGImage:thumbnailImageRef];
            [_im setImage:thumbnailImage];
            CGImageRelease(thumbnailImageRef);
        }
    } else {
        // URL is not for an image or a video, do nothing
        // Optionally, reset to a default image or state
    }
}
@end
