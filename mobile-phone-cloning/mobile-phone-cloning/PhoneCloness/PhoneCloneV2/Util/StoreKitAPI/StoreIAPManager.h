//
//  ClonePhoneMakeStoreIAPManager.h
//  ReplacePhone
//
//  Created by fang on 2020/2/10.
//  Copyright © 2020 macbookair. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "StoreIAPObserver.h"
#import <StoreKit/StoreKit.h>

NS_ASSUME_NONNULL_BEGIN
@protocol MKStoreKitDelegate <NSObject>
@optional
-(void)buySuccessBack;
- (void)failed;
-(void)buycancelBack;

- (void)productAPurchased;
- (void)productBPurchased;
- (void)productCPurchased;
- (void)productWPurchased;
@end

@interface StoreIAPManager : NSObject<SKProductsRequestDelegate> {
    
    NSMutableArray *purchasableObjects;
    StoreIAPObserver *storeObserver;
    
    id<MKStoreKitDelegate> delegate;
}
@property (nonatomic, retain) id<MKStoreKitDelegate> delegate;
@property (nonatomic, retain) NSMutableArray *purchasableObjects;
@property (nonatomic, retain) StoreIAPObserver *storeObserver;
- (void) requestProductData;
- (void) buyFeatureA;
- (void) buyFeatureB;
- (void) buyFeatureC;
- (void) buyFeatureW;

- (void) buyFeature:(NSString*) featureId;

-(void)paymentCanceled;
-(void)refreshbuy;
- (void) failedTransaction: (SKPaymentTransaction *)transaction;
-(void) provideContent: (NSString*) productIdentifier;

+ (StoreIAPManager*)sharedManager;
-(NSString*)getProductPrice:(int)index;

+ (BOOL) featureBPurchased;
+ (BOOL) featureAPurchased;
+ (BOOL) featureCPurchased;
+ (BOOL) featureWPurchased;
+(void) loadPurchases;
+(void) updatePurchases;
- (void)restoreButClick:(void(^)())completionBlock;
-(void)alertnoitems;

+(void) updateAuyA;
+(void) updateAuyB;
+(void) updateAuyC;

+(void) ChangeOldToBuyA:(BOOL)setbool;

+(void)onlyCheckVipDate;

/// 是否已订阅VIP,且VIP未过期
/// @return YES:已订阅VIP且未过期  NO:未订阅VIP,或者VIP已过期
+(BOOL)featureVip;
-(void)checkVipDate;
@end

NS_ASSUME_NONNULL_END
