//
//  ClonePhoneStoreIAPObserver.m
//  ReplacePhone
//
//  Created by fang on 2020/2/10.
//  Copyright © 2020 macbookair. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <StoreKit/StoreKit.h>

#define MobileCloneExpiresDate @"MobileCloneExpiresDate"

typedef void (^checkReceiptCompleteResponseBlock)(NSString* response,NSError* error);
@interface StoreIAPObserver : NSObject<SKPaymentTransactionObserver> {

	
}
@property (nonatomic) BOOL production;
@property (nonatomic,copy) checkReceiptCompleteResponseBlock checkReceiptCompleteBlock;
@property (nonatomic,strong) NSMutableData* receiptRequestData;

- (void)paymentQueue:(SKPaymentQueue *)queue updatedTransactions:(NSArray *)transactions;
- (void) failedTransaction: (SKPaymentTransaction *)transaction;
- (void) completeTransaction: (SKPaymentTransaction *)transaction;
- (void) restoreTransaction: (SKPaymentTransaction *)transaction;
- (void)paymentQueueRestoreCompletedTransactionsFinished:(SKPaymentQueue *)queue;
-(void)checkVipDate;
@property(nonatomic,strong)NSMutableArray * subscribeArray;
@end
