//
//  ProgressViewUserInfo.m
//  Moment Maker
//
//  Created by macbookair on 31/8/2019.
//  Copyright © 2019 LPInfo. All rights reserved.
//

#import "ProgressViewUserInfo.h"
//#import "ProgressViewLoadingButton.h"
static ProgressViewUserInfo *segtonInstance = nil;

@implementation ProgressViewUserInfo

+(ProgressViewUserInfo *)shareUserInfoArray{
    @synchronized(self)
    {
        if (segtonInstance == nil) {
            segtonInstance = [[[self class] alloc] init];
        }
        
    }
    return  segtonInstance;
}

+ (id)allocWithZone:(NSZone *)zone{
    if (segtonInstance == nil) {
        segtonInstance = [super allocWithZone:zone];
    }
    return segtonInstance;
}
- (void)showProgressView:(UIView *)bgView labelText:(NSString *)text
{
    
//    self.activityButton = [ProgressViewLoadingButton new];
//
//    self.activityButton.rotatorColor = [UIColor redColor];// [UIColor colorWithRed:253.0/255.0 green:224.0/255.0 blue:47.0/255.0 alpha:1];
//
//    if (text) {
//        if (![text isEqualToString:@"favorite"]) {
//            self.activityButton.rotatorColor = mainColor;//[UIColor whiteColor];
//
//        }
//        self.activityButton.frame = CGRectMake(0, 0, 30, 30);
//        [self.activityButton startActivity:2];
//        self.activityButton.center = CGPointMake(bgView.frame.size.width/2, bgView.frame.size.height/2);
//
//
//    }else{
//        self.activityButton.frame = CGRectMake(0, 0, 30, 30);
//        [self.activityButton startActivity:3];
//        self.activityButton.center = CGPointMake(kScreenWidth/2, kScreenHeight/2);
//
//    }
//
//    [bgView addSubview:self.activityButton];
    
}
- (void)hiddenProgressView:(UIView *)bgView
{
//    for (UIView *chView in bgView.subviews) {
//        if ([chView isKindOfClass:[ProgressViewLoadingButton class]]) {
//            
//            [UIView animateWithDuration:0.5
//                                  delay:0
//                                options: UIViewAnimationOptionCurveEaseInOut
//                             animations:^{
//                                 chView.alpha = 0;
//                             }
//                             completion:^(BOOL finished){
//                                 [chView removeFromSuperview];
//                                 
//                             }];
//        }
//        
//    }
    
}
- (void)hiddenActivityView
{
    
//    [UIView animateWithDuration:0.2
//                          delay:0
//                        options: UIViewAnimationOptionCurveEaseInOut
//                     animations:^{
//                         self.activityButton.alpha = 0;
//                     }
//                     completion:^(BOOL finished){
//                         [self.activityButton removeFromSuperview];
//                         
//                     }];
    
    
}
@end

