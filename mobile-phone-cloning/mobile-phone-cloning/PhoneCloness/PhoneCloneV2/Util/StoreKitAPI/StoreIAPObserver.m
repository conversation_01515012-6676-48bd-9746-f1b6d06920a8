//
//  ClonePhoneStoreIAPObserver.m
//  ReplacePhone
//
//  Created by fang on 2020/2/10.
//  Copyright © 2020 macbookair. All rights reserved.
//

#import "StoreIAPObserver.h"
#import "StoreIAPManager.h"
#import "NSString+Base64.h"
NSString *const iTunesVerifyReceiptURL = @"https://buy.itunes.apple.com/verifyReceipt";

NSString *const iTunesSandboxVerifyReceiptURL = @"https://sandbox.itunes.apple.com/verifyReceipt";
/// 秘钥
static NSString *sharedSecret = @"76f704111c28473aa0e52e3d86d6cc9e";

// 订阅有效期
#define ExpiresDateKey @"expires_date"
@implementation StoreIAPObserver
- (instancetype)init
{
    if(self==[super init])
    {
        requestURL = [NSURL URLWithString:iTunesVerifyReceiptURL];
    }
    return self;
}
- (void)paymentQueue:(SKPaymentQueue *)queue updatedTransactions:(NSArray *)transactions
{
	for (SKPaymentTransaction *transaction in transactions)
	{
		switch (transaction.transactionState)
		{
			case SKPaymentTransactionStatePurchased:
				
                [self completeTransaction:transaction];
				
                break;
				
            case SKPaymentTransactionStateFailed:
				
                [self failedTransaction:transaction];
				
                break;
				
            case SKPaymentTransactionStateRestored:
				
                [self restoreTransaction:transaction];
				
            default:
				
                break;
		}			
	}
}

- (void) failedTransaction: (SKPaymentTransaction *)transaction
{	
    if (transaction.error.code != SKErrorPaymentCancelled)		
    {		
        // Optionally, display an error here.		
    }	
	[[StoreIAPManager sharedManager] paymentCanceled];
    [[SKPaymentQueue defaultQueue] finishTransaction: transaction];	
}
-(void)checkVipDate
{
    
    NSURL *receiptURL = [[NSBundle mainBundle] appStoreReceiptURL];
    NSData *receipt = [NSData dataWithContentsOfURL:receiptURL];
    
    [self checkReceipt:receipt AndSharedSecret:sharedSecret Transaction:nil productId:nil onCompletion:^(NSString *response, NSError *error) {
        if (error) {
            return ;
        }
        NSDictionary* rec = [self toJSON:response];
        NSArray * array = rec[@"latest_receipt_info"];
        NSMutableArray * subscribe = [NSMutableArray array];
        for (NSDictionary * dict in array)
        {
            if ([self.subscribeArray containsObject:dict[@"product_id"]]) {
                BOOL isActive = [self checkActiveSubscriptionInfo:dict];
                if (isActive) {
                    [subscribe addObject:dict];
                }
            }
        }
        
        if (subscribe.count == 0)
        {
            [[NSUserDefaults standardUserDefaults] removeObjectForKey:MobileCloneExpiresDate];
        }
        else
        {
            //还在订阅;
            NSDictionary *dic = [subscribe lastObject];
            NSString *expiresStr = dic[ExpiresDateKey];//2016-12-29 08:09:11 Etc/GMT
            
            NSDateFormatter *fmt = [[NSDateFormatter alloc] init];
            // Lxh
            // 发现 "YYYY-MM-dd" 有个坑，请慎用，查阅了一些资料发现在跨年时使用 "YYYY-MM-dd" 可能会出现差一年的问题
            // fmt.dateFormat = @"YYYY-MM-dd HH:mm:ss VV";
            fmt.dateFormat = @"yyyy-MM-dd HH:mm:ss VV";
            NSDate *expiresDate = [fmt dateFromString:expiresStr];
            
            [[NSUserDefaults standardUserDefaults] setObject:expiresDate forKey:MobileCloneExpiresDate];
        }
        
        [[NSUserDefaults standardUserDefaults] synchronize];
    }];
}
#pragma mark --请求网络时间
- (NSDate *)getInternetDate{
    NSString *urlString = @"http://m.baidu.com";
    urlString = [urlString stringByAddingPercentEscapesUsingEncoding: NSUTF8StringEncoding];
    // 实例化NSMutableURLRequest，并进行参数配置
    NSMutableURLRequest *request = [[NSMutableURLRequest alloc] init];
    [request setURL:[NSURL URLWithString: urlString]];
    [request setCachePolicy:NSURLRequestReloadIgnoringCacheData];
    [request setTimeoutInterval: 2];
    [request setHTTPShouldHandleCookies:FALSE];
    [request setHTTPMethod:@"GET"];
    NSError *error = nil;
    NSHTTPURLResponse *response;
    [NSURLConnection sendSynchronousRequest:request
                          returningResponse:&response error:&error];
    // 处理返回的数据 20180928
    if (error ||!response) {
        return [NSDate date];
    }
    NSString *date = [[response allHeaderFields] objectForKey:@"Date"];
    date = [date substringFromIndex:5];//index到这个字符串的结尾
    date = [date substringToIndex:[date length]-4];//从索引0到给定的索引index
    NSDateFormatter *dMatter = [[NSDateFormatter alloc] init];
    dMatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"en_US"];
    [dMatter setDateFormat:@"dd MMM yyyy HH:mm:ss"];
    NSDate *netDate = [dMatter dateFromString:date];//[[dMatter dateFromString:date] dateByAddingTimeInterval:60*60*8];//时间差8小时
    NSTimeZone *zone = [NSTimeZone systemTimeZone];
    NSInteger interval = [zone secondsFromGMTForDate: netDate];
    netDate = [netDate  dateByAddingTimeInterval: interval];
    return netDate;
}
-(void)checkReceiptAndFinishTransaction: (SKPaymentTransaction *)transaction productId:(NSString*)productId{
    if ([self.subscribeArray containsObject:productId]) {
        NSURL *receiptURL = [[NSBundle mainBundle] appStoreReceiptURL];
        NSData *receipt = [NSData dataWithContentsOfURL:receiptURL];
       [self checkReceipt:receipt AndSharedSecret:sharedSecret Transaction:transaction productId:productId  onCompletion:^(NSString *response, NSError *error) {
            NSDictionary* rec = [self toJSON:response];
            NSArray * array = rec[@"latest_receipt_info"];
            
            NSMutableArray * subscribe = [NSMutableArray array];
            for (NSDictionary * dict in array)
            {
                if ([self.subscribeArray containsObject:dict[@"product_id"]]) {
                    BOOL isActive = [self checkActiveSubscriptionInfo:dict];
                    if (isActive) {
                        [subscribe addObject:dict];
                    }
                }
            }
            
            if (subscribe.count == 0)
            {
                [[NSUserDefaults standardUserDefaults] removeObjectForKey:MobileCloneExpiresDate];
        
            }
            else
            {
                //还在订阅;
                NSDictionary *dic = [subscribe lastObject];
                NSString *expiresStr = dic[ExpiresDateKey];//2016-12-29 08:09:11 Etc/GMT
                
                NSDateFormatter *fmt = [[NSDateFormatter alloc] init];
                // Lxh
                // 发现 "YYYY-MM-dd" 有个坑，请慎用，查阅了一些资料发现在跨年时使用 "YYYY-MM-dd" 可能会出现差一年的问题
                // fmt.dateFormat = @"YYYY-MM-dd HH:mm:ss VV";
                fmt.dateFormat = @"yyyy-MM-dd HH:mm:ss VV";
                NSDate *expiresDate = [fmt dateFromString:expiresStr];
                
                [[NSUserDefaults standardUserDefaults] setObject:expiresDate forKey:MobileCloneExpiresDate];
  
            }
            
            [[NSUserDefaults standardUserDefaults] synchronize];


            if([rec[@"status"] integerValue]==0)
            {
                [[StoreIAPManager sharedManager] provideContent: transaction.payment.productIdentifier];
                [[SKPaymentQueue defaultQueue] finishTransaction: transaction];

                //MYLog(@"SUCCESS %@",response);
            }
            else {
                /*states状态值
                    22002:秘钥错误,请在apple开发者平台核对秘钥
                    22007:验证网址有误,请核对其网址输入是否有误
                 */
                NSLog(@"status:%ld", (long)[rec[@"status"] integerValue]);
                [[StoreIAPManager sharedManager] paymentCanceled];
                //MYLog(@"Fail");
            }
        }];

    }else
    {
        [[StoreIAPManager sharedManager] provideContent: transaction.payment.productIdentifier];
        [[SKPaymentQueue defaultQueue] finishTransaction: transaction];

    }


}
- (void) completeTransaction: (SKPaymentTransaction *)transaction
{
    
    NSString * productId = transaction.payment.productIdentifier;
    [self checkReceiptAndFinishTransaction:transaction productId:productId];
    return;
}

- (void) restoreTransaction: (SKPaymentTransaction *)transaction
{
    [self checkReceiptAndFinishTransaction:transaction productId:transaction.originalTransaction.payment.productIdentifier];
    
//    [[SecretStoreIAPManager sharedManager] provideContent: transaction.originalTransaction.payment.productIdentifier];
//    [[SKPaymentQueue defaultQueue] finishTransaction: transaction];
}
- (void)paymentQueueRestoreCompletedTransactionsFinished:(SKPaymentQueue *)queue
{
    if (queue.transactions.count <=0) {
        [[StoreIAPManager sharedManager] alertnoitems];
        return;
    }
}
- (BOOL)checkActiveSubscriptionInfo:(NSDictionary *)dic
{
    BOOL isActive = NO;
    
    if (![dic.allKeys containsObject:@"is_upgraded"]) {
        NSString *expiresStr = dic[ExpiresDateKey];
        NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
        formatter.dateFormat = @"yyyy-MM-dd HH:mm:ss VV";
        NSDate *expiresDate = [formatter dateFromString:expiresStr];
        
        NSDate *nowDate = [NSDate date];
        // 更早的时间
        NSDate *earlierDate = [expiresDate earlierDate:nowDate];
        if ([earlierDate isEqualToDate:expiresDate]) {
            // 过期
            isActive = NO;
        } else {
            isActive = YES;
        }
    } else {
        // 1年转1年内的订阅会在1年的订阅加了个字段"is_upgraded"，为true时说明变更了套餐
        // 测试时只有true这个值，不知是否有别的状态，假设非true为活跃的状态
        if ([dic[@"is_upgraded"] isEqualToString:@"true"]) {
            isActive = NO;
        } else {
            isActive = YES;
        }
    }
    
    return isActive;
}
static  NSURL *requestURL = nil;
- (void)checkReceipt:(NSData*)receiptData AndSharedSecret:(NSString*)secretKey Transaction: (SKPaymentTransaction *)transaction productId:(NSString*)productId onCompletion:(checkReceiptCompleteResponseBlock)completion
{
    
    self.checkReceiptCompleteBlock = completion;
    
    NSError *jsonError = nil;
    NSString *receiptBase64 = [NSString base64StringFromData:receiptData length:[receiptData length]];
    
    
    NSDictionary *jsonData = nil;
    
    if(secretKey !=nil && ![secretKey isEqualToString:@""]) {
        
        jsonData = [NSDictionary dictionaryWithObjectsAndKeys:receiptBase64,@"receipt-data",
                                                            secretKey,@"password",
                                                            nil];
        
    }
    else {
        jsonData = [NSDictionary dictionaryWithObjectsAndKeys:
                                                            receiptBase64,@"receipt-data",
                                                            nil];
    }
    
    
    //    NSString* jsonStr = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    
   

    
//    requestURL = [NSURL URLWithString:@"https://sandbox.itunes.apple.com/verifyReceipt"];
    
    
    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];

       // 设定请求序列化为JSON格式
       manager.requestSerializer = [AFJSONRequestSerializer serializer];
    [manager POST:requestURL.absoluteString parameters:jsonData headers:nil progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
       
        NSDictionary *responseJSON = responseObject;
        static NSInteger sandboxCode = 21007;
        static NSString *statusKey = @"status";
        NSInteger statusCode = [responseJSON[statusKey] integerValue];
        if(statusCode == sandboxCode)
        {
            requestURL = [NSURL URLWithString:iTunesSandboxVerifyReceiptURL];
            if(!transaction)
            {
                [self checkVipDate];
            }
            else
            {
                [self checkReceipt:receiptData AndSharedSecret:secretKey Transaction:transaction productId:productId onCompletion:completion];
            }
        }
        else
        {
            if(_checkReceiptCompleteBlock) {
                _checkReceiptCompleteBlock([self stringFromDictionary:responseJSON] ,nil);
            }
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {

        NSMutableDictionary* errorDetail = [[NSMutableDictionary alloc] init];
        [errorDetail setValue:@"Can't create connection" forKey:NSLocalizedDescriptionKey];
        error = [NSError errorWithDomain:@"IAPHelperError" code:100 userInfo:errorDetail];
        if(_checkReceiptCompleteBlock) {
            _checkReceiptCompleteBlock(nil,error);
        }
        
    }];
    
   
}




-(id)toJSON:(NSString *)json
{
    if(json == nil){
      return nil;
    }
    NSError* e = nil;
    id jsonObject = [NSJSONSerialization JSONObjectWithData: [json dataUsingEncoding:NSUTF8StringEncoding]
                                                    options: NSJSONReadingMutableContainers
                                                      error: &e];

    if(e==nil) {
        return jsonObject;
    }
    else {
        NSLog(@"%@",[e localizedDescription]);
        return nil;
    }
}
- (NSString *)stringFromDictionary:(NSDictionary *)dict {
    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dict options:NSJSONWritingPrettyPrinted error:&error];

    if (!jsonData) {
        NSLog(@"Error converting dictionary to JSON data: %@", error);
        return nil;
    } else {
        return [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    }
}
-(void)connection:(NSURLConnection *)connection didFailWithError:(NSError *)error {
    NSLog(@"Cannot transmit receipt data. %@",[error localizedDescription]);
    
    if(_checkReceiptCompleteBlock) {
        _checkReceiptCompleteBlock(nil,error);
    }
}

-(void)connection:(NSURLConnection *)connection didReceiveResponse:(NSURLResponse *)response {
    [self.receiptRequestData setLength:0];
}

-(void)connection:(NSURLConnection *)connection didReceiveData:(NSData *)data {
    [self.receiptRequestData appendData:data];
}

-(void)connectionDidFinishLoading:(NSURLConnection *)connection {
    NSString *response = [[NSString alloc] initWithData:self.receiptRequestData encoding:NSUTF8StringEncoding];
    
    if(_checkReceiptCompleteBlock) {
        _checkReceiptCompleteBlock(response,nil);
    }
}

@end
