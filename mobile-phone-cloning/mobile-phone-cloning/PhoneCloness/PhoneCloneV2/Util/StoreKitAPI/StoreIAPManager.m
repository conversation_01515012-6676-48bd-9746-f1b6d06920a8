//
//  ClonePhoneMakeStoreIAPManager.m
//  ReplacePhone
//
//  Created by fang on 2020/2/10.
//  Copyright © 2020 macbookair. All rights reserved.
//

#import "StoreIAPManager.h"
#import "NSDictionary+Value.h"
#import "StoreIAPObserver.h"
@implementation StoreIAPManager

@synthesize purchasableObjects;
@synthesize delegate;

// all your features should be managed one and only by StoreManager
//static NSString *featureWId = @"com.Footprintmap.month";//月订阅
static NSString *featureWId = @"com.phoneclone.1weekvip";//月订阅
static NSString *featureAId = @"com.phoneclone.1monthvip";//月订阅
static NSString *featureBId = @"com.phoneclone.1yearvip";//年订阅

static NSString *featureCId = @"com.phoneclone.permanentvip";//永久
BOOL featureWPurchased;
BOOL featureAPurchased;
BOOL featureBPurchased;
BOOL featureCPurchased;

static StoreIAPManager* _sharedStoreManager; // self

// 添加重试机制相关变量
static NSInteger priceRequestRetryCount = 0;
static const NSInteger maxRetryCount = 3;
static NSTimer *retryTimer = nil;


+ (BOOL) featureAPurchased {
    
    return featureAPurchased;
}
+ (BOOL) featureBPurchased {
    
    return featureBPurchased;
}
+ (BOOL) featureCPurchased {
    
    return featureCPurchased;
}
+ (BOOL) featureWPurchased {
    
    return featureWPurchased;
}
+ (StoreIAPManager*)sharedManager
{
    @synchronized(self) {

        if (_sharedStoreManager == nil) {

            _sharedStoreManager = [[self alloc] init]; // assignment not done here
            _sharedStoreManager.purchasableObjects = [[NSMutableArray alloc] init];

            // 重置重试计数器
            priceRequestRetryCount = 0;

            [_sharedStoreManager requestProductData];
            [StoreIAPManager loadPurchases];
            _sharedStoreManager.storeObserver = [[StoreIAPObserver alloc] init];
            _sharedStoreManager.storeObserver.subscribeArray = [NSMutableArray arrayWithObjects:featureWId,featureAId,featureBId,nil];
            [[SKPaymentQueue defaultQueue] addTransactionObserver:_sharedStoreManager.storeObserver];
            [_sharedStoreManager.storeObserver checkVipDate];

            // 延迟检查是否需要设置默认价格（给网络请求一些时间）
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [_sharedStoreManager checkAndSetDefaultPricesIfNeeded];
            });
        }
    }
    return _sharedStoreManager;
}

+(void)onlyCheckVipDate
{
    dispatch_async(dispatch_get_global_queue(0, 0), ^{
        [_sharedStoreManager.storeObserver checkVipDate];
    });
}
-(void)checkVipDate
{
    [self.storeObserver checkVipDate];
}

/// 是否已订阅VIP,且VIP未过期
/// @return YES:已订阅VIP且未过期  NO:未订阅VIP,或者VIP已过期
+ (BOOL)featureVip
{
#if DEBUG
    return YES;
#endif
    
    if ([self featureCPurchased]) {
        return YES;
    }
    NSUserDefaults * userDefaults = [NSUserDefaults standardUserDefaults];
    NSDate * expiresDate = [userDefaults objectForKey:MobileCloneExpiresDate];
    if (!expiresDate) {
        [userDefaults setInteger:0 forKey:@"Payment"];
        return NO;
    }
    NSDate * nowDate = [self getInternetDate];//[NSDate date];
    //更早的时间
    NSDate *earlierDate = [expiresDate earlierDate:nowDate];
    
    if ([earlierDate isEqualToDate:expiresDate] ) {
        //过期;
         [userDefaults setInteger:0 forKey:@"Payment"];
        return NO;
    }
    else
    {
        //还在订阅;
         [userDefaults setInteger:1 forKey:@"Payment"];
        return YES;
    }
    
}
#pragma mark --请求网络时间
+ (NSDate *)getInternetDate{
    NSString *urlString = @"http://m.baidu.com";
    urlString = [urlString stringByAddingPercentEscapesUsingEncoding: NSUTF8StringEncoding];
    // 实例化NSMutableURLRequest，并进行参数配置
    NSMutableURLRequest *request = [[NSMutableURLRequest alloc] init];
    [request setURL:[NSURL URLWithString: urlString]];
    [request setCachePolicy:NSURLRequestReloadIgnoringCacheData];
    [request setTimeoutInterval: 2];
    [request setHTTPShouldHandleCookies:FALSE];
    [request setHTTPMethod:@"GET"];
    NSError *error = nil;
    NSHTTPURLResponse *response;
    [NSURLConnection sendSynchronousRequest:request
                          returningResponse:&response error:&error];
    if (error ||!response) {
        return [NSDate date];
    }
    NSString *date = [[response allHeaderFields] objectForKey:@"Date"];
    date = [date substringFromIndex:5];//index到这个字符串的结尾
    date = [date substringToIndex:[date length]-4];//从索引0到给定的索引index
    NSDateFormatter *dMatter = [[NSDateFormatter alloc] init];
    dMatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"en_US"];
    [dMatter setDateFormat:@"dd MMM yyyy HH:mm:ss"];
    NSDate *netDate = [dMatter dateFromString:date];//[[dMatter dateFromString:date] dateByAddingTimeInterval:60*60*8];//时间差8小时
    NSTimeZone *zone = [NSTimeZone systemTimeZone];
    NSInteger interval = [zone secondsFromGMTForDate: netDate];
    netDate = [netDate  dateByAddingTimeInterval: interval];
    return netDate;
}
#pragma mark Singleton Methods

+ (id)allocWithZone:(NSZone *)zone

{
    @synchronized(self) {
        
        if (_sharedStoreManager == nil) {
            
            _sharedStoreManager = [super allocWithZone:zone];
            return _sharedStoreManager;  // assignment and return on first allocation
        }
    }
    
    return nil; //on subsequent allocation attempts return nil
}


- (id)copyWithZone:(NSZone *)zone
{
    return self;
}

- (void) requestProductData
{

    SKProductsRequest *request= [[SKProductsRequest alloc] initWithProductIdentifiers:
                                 [NSSet setWithObjects:featureWId,featureAId, featureBId,featureCId,nil]]; // add any other product here
    request.delegate = self;
    [request start];
}

// 设置默认价格的方法
- (void)setDefaultPrices
{
    NSLog(@"设置默认价格，因为多次获取价格失败");

    // 检查是否已经有价格，如果没有则设置默认价格
    NSString *weekPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureWId"];
    NSString *monthPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureAId"];
    NSString *yearPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureBId"];
    NSString *permanentPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureCId"];

    if (!weekPrice) {
        [[NSUserDefaults standardUserDefaults] setObject:@"¥12" forKey:@"featureWId"];
        NSLog(@"设置默认周价格: ¥12");
    }

    if (!monthPrice) {
        [[NSUserDefaults standardUserDefaults] setObject:@"¥22" forKey:@"featureAId"];
        NSLog(@"设置默认月价格: ¥22");
    }

    if (!yearPrice) {
        [[NSUserDefaults standardUserDefaults] setObject:@"¥138" forKey:@"featureBId"];
        NSLog(@"设置默认年价格: ¥138");
    }

    if (!permanentPrice) {
        [[NSUserDefaults standardUserDefaults] setObject:@"¥168" forKey:@"featureCId"];
        NSLog(@"设置默认永久价格: ¥168");
        [self checkKeyifOn:@"¥168"];
    }

    [[NSUserDefaults standardUserDefaults] synchronize];
}

// 重试获取产品价格
- (void)retryRequestProductData
{
    if (priceRequestRetryCount < maxRetryCount) {
        priceRequestRetryCount++;
        NSLog(@"重试获取产品价格，第 %ld 次", (long)priceRequestRetryCount);

        // 延迟2秒后重试
        retryTimer = [NSTimer scheduledTimerWithTimeInterval:2.0 target:self selector:@selector(requestProductData) userInfo:nil repeats:NO];
    } else {
        NSLog(@"达到最大重试次数，使用默认价格");
        [self setDefaultPrices];
    }
}

// 检查是否需要设置默认价格
- (void)checkAndSetDefaultPricesIfNeeded
{
    NSString *weekPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureWId"];
    NSString *monthPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureAId"];
    NSString *yearPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureBId"];
    NSString *permanentPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureCId"];

    // 如果任何一个价格为空，说明网络请求可能失败了，设置默认价格
    if (!weekPrice || !monthPrice || !yearPrice || !permanentPrice) {
        NSLog(@"检测到部分价格缺失，设置默认价格");
        [self setDefaultPrices];
    } else {
        NSLog(@"所有价格都已获取: 周=%@, 月=%@, 年=%@, 永久=%@", weekPrice, monthPrice, yearPrice, permanentPrice);
    }
}
- (void)restoreButClick:(void(^)())completionBlock
{
    [[SKPaymentQueue defaultQueue] restoreCompletedTransactions];
    completionBlock();
}
-(void)alertnoitems
{
    UIAlertView *alert = [[UIAlertView alloc] initWithTitle:@"Message" message:@"You haven't purchased any items!"
                                                   delegate:self cancelButtonTitle:@"OK" otherButtonTitles: nil];
    [alert show];
}

- (void)productsRequest:(SKProductsRequest *)request didReceiveResponse:(SKProductsResponse *)response
{
    NSLog(@"成功获取产品信息，产品数量: %lu", (unsigned long)response.products.count);

    // 重置重试计数器，因为请求成功了
    priceRequestRetryCount = 0;
    if (retryTimer) {
        [retryTimer invalidate];
        retryTimer = nil;
    }

    NSMutableArray * array = [NSMutableArray arrayWithObjects:featureWId,featureAId, featureBId,featureCId,nil];
    [purchasableObjects addObjectsFromArray:response.products];

    // 检查是否获取到了所有产品
    if (response.products.count == 0) {
        NSLog(@"未获取到任何产品信息，使用重试机制");
        [self retryRequestProductData];
        return;
    }

    // 标记哪些产品成功获取到了价格
    NSMutableSet *receivedProductIds = [NSMutableSet set];

    for(int i=0;i<[purchasableObjects count];i++)
    {
        NSString *price = [self getProductPrice:i];
        SKProduct *product = [purchasableObjects objectAtIndex:i];

        if ([[product productIdentifier] isEqual:featureWId]) {
            [[NSUserDefaults standardUserDefaults] setObject:price forKey:@"featureWId"];
            [receivedProductIds addObject:featureWId];
            NSLog(@"获取到周价格: %@", price);
        }
        if ([[product productIdentifier] isEqual:featureAId]) {
            [[NSUserDefaults standardUserDefaults] setObject:price forKey:@"featureAId"];
            [receivedProductIds addObject:featureAId];
            NSLog(@"获取到月价格: %@", price);
        }
        if ([[product productIdentifier] isEqual:featureBId]) {
            [[NSUserDefaults standardUserDefaults] setObject:price forKey:@"featureBId"];
            [receivedProductIds addObject:featureBId];
            NSLog(@"获取到年价格: %@", price);
        }
        if ([[product productIdentifier] isEqual:featureCId]) {
            [[NSUserDefaults standardUserDefaults] setObject:price forKey:@"featureCId"];
            [receivedProductIds addObject:featureCId];
            [self checkKeyifOn:price];
            NSLog(@"获取到永久价格: %@", price);
        }
        [[NSUserDefaults standardUserDefaults] synchronize];
    }

    // 检查是否有产品没有获取到价格，如果有则为其设置默认价格
    NSArray *allProductIds = @[featureWId, featureAId, featureBId, featureCId];
    for (NSString *productId in allProductIds) {
        if (![receivedProductIds containsObject:productId]) {
            NSLog(@"产品 %@ 未获取到价格，设置默认价格", productId);
            [self setDefaultPriceForProductId:productId];
        }
    }
}

// 为特定产品设置默认价格
- (void)setDefaultPriceForProductId:(NSString *)productId
{
    if ([productId isEqual:featureWId]) {
        [[NSUserDefaults standardUserDefaults] setObject:@"¥12" forKey:@"featureWId"];
    } else if ([productId isEqual:featureAId]) {
        [[NSUserDefaults standardUserDefaults] setObject:@"¥22" forKey:@"featureAId"];
    } else if ([productId isEqual:featureBId]) {
        [[NSUserDefaults standardUserDefaults] setObject:@"¥138" forKey:@"featureBId"];
    } else if ([productId isEqual:featureCId]) {
        [[NSUserDefaults standardUserDefaults] setObject:@"¥168" forKey:@"featureCId"];
        [self checkKeyifOn:@"¥168"];
    }
    [[NSUserDefaults standardUserDefaults] synchronize];
}

// 添加SKProductsRequestDelegate的失败处理方法
- (void)request:(SKRequest *)request didFailWithError:(NSError *)error
{
    NSLog(@"获取产品信息失败: %@", error.localizedDescription);

    // 取消之前的重试定时器
    if (retryTimer) {
        [retryTimer invalidate];
        retryTimer = nil;
    }

    // 尝试重试或使用默认价格
    [self retryRequestProductData];
}

-(void)checkKeyifOn:(NSString*)checkStr
{
    if (!checkStr) {
        return;
    }
    if (![@"确认" isEqualToString:NSLocalizedString(@"确认", nil)]) {
        return;
    }
    
    if (([checkStr rangeOfString:@"¥"].location!=NSNotFound)) {
        NSMutableString *valStr = [NSMutableString stringWithString:checkStr];
        [valStr deleteCharactersInRange: [checkStr rangeOfString:@"¥"]];
        int value = [valStr floatValue];
        if (value > 288) {
            [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"checkKeyifOn"];
            if (value > 328) {
                [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"checkKeyifOn1"];
            }
            [[NSUserDefaults standardUserDefaults] synchronize];
        }
    }
}

-(NSString*)getProductPrice:(int)index
{
    if (_sharedStoreManager&&_sharedStoreManager.purchasableObjects) {
        if (_sharedStoreManager.purchasableObjects.count > 0&&index >=0 && index <_sharedStoreManager.purchasableObjects.count) {
            SKProduct *product = [_sharedStoreManager.purchasableObjects objectAtIndex:index];
            NSNumberFormatter *numberFormatter = [[NSNumberFormatter alloc] init];
            [numberFormatter setFormatterBehavior:NSNumberFormatterBehavior10_4];
            [numberFormatter setNumberStyle:NSNumberFormatterCurrencyStyle];
            [numberFormatter setLocale:product.priceLocale];
            NSString *formattedPrice = [numberFormatter stringFromNumber:product.price];
            return formattedPrice;
        }
    }
    return @"$1.99";
}


- (void) buyFeatureA
{
    [self buyFeature:featureAId];
}
- (void) buyFeatureB
{
    [self buyFeature:featureBId];
}
- (void) buyFeatureC
{
    [self buyFeature:featureCId];
}
- (void) buyFeatureW
{
    [self buyFeature:featureWId];
}
- (void) buyFeature:(NSString*) featureId
{
    if ([SKPaymentQueue canMakePayments])
    {
        SKPayment *payment = [SKPayment paymentWithProductIdentifier:featureId];
        [[SKPaymentQueue defaultQueue] addPayment:payment];
    }
    else
    {
        UIAlertView *alert = [[UIAlertView alloc] initWithTitle:@"MyApp" message:@"You are not authorized to purchase from AppStore"
                                                       delegate:self cancelButtonTitle:@"OK" otherButtonTitles: nil];
        [alert show];
    }
}

-(void)paymentCanceled
{
    if([delegate respondsToSelector:@selector(failed)])
        [delegate failed];
}

- (void) failedTransaction: (SKPaymentTransaction *)transaction
{
    if([delegate respondsToSelector:@selector(failed)])
        [delegate failed];
    NSString *messageToBeShown = [NSString stringWithFormat:@"Reason: %@, You can try: %@", [transaction.error localizedFailureReason], [transaction.error localizedRecoverySuggestion]];
    UIAlertView *alert = [[UIAlertView alloc] initWithTitle:@"Unable to complete your purchase" message:messageToBeShown
                                                   delegate:self cancelButtonTitle:@"OK" otherButtonTitles: nil];
    [alert show];
}

-(void) provideContent: (NSString*) productIdentifier
{
    if([productIdentifier isEqualToString:featureAId])
    {
        featureAPurchased = YES;
        if([delegate respondsToSelector:@selector(productAPurchased)])
            [delegate productAPurchased];
    }
    if([productIdentifier isEqualToString:featureBId])
    {
        featureBPurchased = YES;
        if([delegate respondsToSelector:@selector(productBPurchased)])
            [delegate productBPurchased];
    }
    if([productIdentifier isEqualToString:featureCId])
    {
        featureCPurchased = YES;
        if([delegate respondsToSelector:@selector(productCPurchased)])
            [delegate productCPurchased];
    }
    if([productIdentifier isEqualToString:featureWId])
    {
        featureWPurchased = YES;
        if([delegate respondsToSelector:@selector(productWPurchased)])
            [delegate productWPurchased];
    }
    [StoreIAPManager updatePurchases];
    // [self refreshbuy];
}


+(void) loadPurchases
{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    featureAPurchased = [userDefaults boolForKey:featureAId];
    featureBPurchased = [userDefaults boolForKey:featureBId];
    featureCPurchased = [userDefaults boolForKey:featureCId];
    
}
+(void) updatePurchases
{
    NSLog(@"updatePurchases------------");
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setBool:featureAPurchased forKey:featureAId];
    [userDefaults setBool:featureBPurchased forKey:featureBId];
    [userDefaults setBool:featureCPurchased forKey:featureCId];
}
+(void) ChangeOldToBuyA:(BOOL)setbool
{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setBool:setbool forKey:featureAId];
    featureAPurchased = setbool;
}
+(void) updateAuyA
{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setBool:NO forKey:featureAId];
    featureAPurchased = NO;
}

@end
