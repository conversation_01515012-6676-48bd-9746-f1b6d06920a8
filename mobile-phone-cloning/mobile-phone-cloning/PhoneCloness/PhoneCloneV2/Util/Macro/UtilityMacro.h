//
//  UtilityMacro.h
//  PhoneClone
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#ifndef UtilityMacro_h
#define UtilityMacro_h

//获取屏幕的信息
#define kScreenWidth   [UIScreen mainScreen].bounds.size.width
#define kScreenHeight  [UIScreen mainScreen].bounds.size.height
#define kScreenSize    [UIScreen mainScreen].bounds.size
#define kScreenRect    [UIScreen mainScreen].bounds

#define kIsiOS11Available ([UIDevice currentDevice].systemVersion.floatValue >= 11.0)
#define kIsiPhoneX (([[UIScreen mainScreen] bounds].size.height<812)?NO:YES)
#define kIsiPad ([[UIDevice currentDevice].model isEqualToString:@"iPad"]?YES:NO)
#define kStatusBarHeight (kIsiPhoneX ? 44.f : 20.f)
#define kNavgationBarHeight 44.f
#define kStatusBarAndNavgationBarHeight (kIsiPhoneX ? 88.f : 64.f)
#define kTabbarHeight (kIsiPhoneX ? (49.f + 34.f) : 49.f)
#define kBottomSpaceHeight (kIsiPhoneX ? 34.f : 0.f)

//获取窗口
#define kWindow [UIApplication sharedApplication].keyWindow

//自定义log
#ifdef DEBUG
#define BSLog(fmt, ...)  fprintf(stderr,"--- time:%s  line:%d  file:%s\nmethod:%s\n%s \n", __TIME__,__LINE__,[[[NSString stringWithUTF8String:__FILE__] lastPathComponent] UTF8String],__func__, [[NSString stringWithFormat:fmt, ##__VA_ARGS__] UTF8String])
#else
#define BSLog(...)
#endif

//设置View的圆角
#define BSViewRadius(View, Radius)\
\
[View.layer setCornerRadius:(Radius)];\
[View.layer setMasksToBounds:YES];

//设置View的圆角与边框
#define BSViewBorderRadius(View, Radius, Width, Color)\
\
[View.layer setCornerRadius:(Radius)];\
[View.layer setMasksToBounds:YES];\
[View.layer setBorderWidth:(Width)];\
[View.layer setBorderColor:[Color CGColor]]

#define MSG(msg) if(msg != nil && [msg length] > 0) {\
[MBProgressHUD bwm_showTitle:msg toView:[UIApplication sharedApplication].keyWindow hideAfter:1.5];}\

/**颜色控制快捷键**/
#define BKgetRandomColor [UIColor colorWithRed:arc4random_uniform(256)/255.0 green:arc4random_uniform(256)/255.0 blue:arc4random_uniform(256)/255.0 alpha:1]
#define BKgetColorFrom(r,g,b,a) [UIColor colorWithRed:r/255.0 green:g/255.0 blue:b/255.0 alpha:a]
#define BKpickColorFrom(a) [UIColor a]

/**其他类型转成字符串的宏定义**/
#define BKStrWithObj(a) [NSString stringWithFormat:@"%@", a]
#define BKStrWithNum(a) [NSString stringWithFormat:@"%ld", a]
#define BKStrWithFloat(a) [NSString stringWithFormat:@"%lf", a]
#define BKStr(a, b) [NSString stringWithFormat:a, b]

/**打印快捷键**/
#ifdef DEBUG  //调试状态,打开Log功能
#define BKdislog(a)  NSLog(@"\n%@\n====================================",a)
#define BKdisplayError(a)   NSLog(@"\n错误信息:\n%@\n====================================",a)
#define BKdisNum(a)   NSLog(@"\n%ld\n====================================",a)
#define BKdisFloat(a)   NSLog(@"\n%lf\n====================================",a)
#define WOWPOWER     [NSString stringWithFormat:@"Lok'tar O'gar!\n===================================="]
#else  //发布状态,关闭Log功能
#define BKdislog(a)
#define BKdisplayError(a)
#define BKdisNum(a)
#define BKdisFloat(a)
#define WOWPOWER
#endif

#endif

