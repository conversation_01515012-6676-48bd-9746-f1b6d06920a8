//
//  WeakObjMacro.h
//  PhoneClone
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#ifndef WeakObjMacro_h
#define WeakObjMacro_h

#define kWeakSelf  __weak __typeof(self) weakSelf = self;

#ifndef weakObj
#if __has_feature(objc_arc)
#define weakObj(object)  __weak __typeof__(object) weak##_##object = object;
#else
#define weakObj(object)  __block __typeof__(object) block##_##object = object;
#endif
#endif

#ifndef strongObj
#if __has_feature(objc_arc)
#define strongObj(object)  __typeof__(object) object = weak##_##object; if(!object) return;
#else
#define strongObj(object)  __typeof__(object) object = block##_##object; if(!object) return;
#endif
#endif

#endif
