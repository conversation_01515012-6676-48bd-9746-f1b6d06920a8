//
//  MeasureNetTool.h
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef void (^measureBlock) (float speed);
typedef void (^finishMeasureBlock) (float speed);

@interface MeasureNetTool : NSObject

- (instancetype)initWithblock:(measureBlock)measureBlock finishMeasureBlock:(finishMeasureBlock)finishMeasureBlock
                  failedBlock:(void (^) (NSError *error))failedBlock;

- (void)startMeasure;

- (void)stopMeasure;

@end

