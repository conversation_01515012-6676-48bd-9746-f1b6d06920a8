//
//  MeasureNetTool.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "MeasureNetTool.h"

#define kURLString @"http://down.qq.com/lol/dltools/LOL_V4.1.8.0_FULL_0_tgod_signed.exe"

@interface MeasureNetTool () <NSURLSessionDataDelegate>

@property (nonatomic, copy) measureBlock infoBlock;
@property (nonatomic, copy) finishMeasureBlock fmeasureBlock;

@property (nonatomic, strong) NSMutableData *connectData;
@property (nonatomic, strong) NSMutableData *oneMinData;

@property (nonatomic, strong) NSURLSession *session;
@property (nonatomic, strong) NSURLSessionTask *task;
@property (nonatomic, strong) NSTimer *timer;

@property (nonatomic, assign) NSInteger second;

@property (nonatomic, copy) void (^faildBlock) (NSError *error);

@end

@implementation MeasureNetTool

- (instancetype)initWithblock:(measureBlock)measureBlock finishMeasureBlock:(finishMeasureBlock)finishMeasureBlock
                  failedBlock:(void (^) (NSError *error))failedBlock {
    
    if (self = [super init]) {
        _infoBlock = measureBlock;
        _fmeasureBlock = finishMeasureBlock;
        _faildBlock = failedBlock;
        _connectData = [[NSMutableData alloc] init];
        _oneMinData = [[NSMutableData alloc] init];
    }
    return self;
    
}

- (void)startMeasure {
    [self meausureNet];
}

- (void)stopMeasure {
    [self finishMeasure];
}

- (void)meausureNet {
    self.timer = [NSTimer scheduledTimerWithTimeInterval:1.0 target:self selector:@selector(countTime) userInfo:nil repeats:YES];
    
    NSURL *url = [NSURL URLWithString:kURLString];
    
    NSURLSessionConfiguration *config = [NSURLSessionConfiguration defaultSessionConfiguration];
    self.session = [NSURLSession sessionWithConfiguration:config delegate:self delegateQueue:[NSOperationQueue mainQueue]];
    self.task = [self.session dataTaskWithURL:url];
    [self.task resume];
    
    [self.timer fire];
    self.second = 0;
}

- (void)finishMeasure {
    [self.timer invalidate];
    self.timer = nil;
    if(self.second != 0){
        float finishSpeed = self.connectData.length / self.second;
        self.fmeasureBlock(finishSpeed);
    }
    
    [self.task cancel];
    self.connectData = nil;
    self.session = nil;
    self.task = nil;
}

- (void)countTime{
    self.second++;
    if (self.second == 15) {
        [self finishMeasure];
        return;
    }
    float speed = self.oneMinData.length;

    self.infoBlock(speed);
    
    [self.oneMinData resetBytesInRange:NSMakeRange(0, _oneMinData.length)];
    [self.oneMinData setLength:0];
}

#pragma mark - NSURLSessionDataDelegate
- (void)URLSession:(NSURLSession *)session dataTask:(NSURLSessionDataTask *)dataTask didReceiveData:(NSData *)data {
    
    [self.connectData appendData:data];
    [self.oneMinData appendData:data];
    
}

- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didCompleteWithError:(NSError *)error {
    
    if (error) {
        
        if (self.faildBlock) {
            self.faildBlock(error);
        }
        
    }else {
        [self finishMeasure];
    }

}


@end
