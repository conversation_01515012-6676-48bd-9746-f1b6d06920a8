//
//  BSRetReciveFileServer.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSRetReciveFileServer.h"
#import "BSTransferFileProgressVC.h"

#import <TZImageManager.h>
#import <MJExtension.h>
#import <Contacts/Contacts.h>
#import <ContactsUI/ContactsUI.h>
#import <EventKit/EventKit.h>

#import <GCDAsyncSocket.h>

#define kDivisionStr     @"05162012"

@interface BSRetReciveFileServer () <GCDAsyncSocketDelegate>

@property (strong, nonatomic) GCDAsyncSocket *serverSocket;
@property (strong, nonatomic) GCDAsyncSocket *clientSocket;
@property (nonatomic, strong) EKEventStore *eventStore;

@property (nonatomic, strong) BSTransferFileProgressVC *progressVC;

@property (nonatomic, copy) void(^refreshProgress)(NSInteger currentIndex, NSInteger totalCount);

@property (nonatomic, assign) NSInteger packetLength;

@property (nonatomic, assign) NSInteger reciveFileNum;
@property (nonatomic, assign) NSInteger fileCount;

@property (nonatomic, assign) BOOL transfSuccessed;

@end

@implementation BSRetReciveFileServer


- (instancetype)init {
    if (self = [super init]) {
        _serverSocket = [[GCDAsyncSocket alloc] initWithDelegate:self delegateQueue:dispatch_get_main_queue()];
        _eventStore = [[EKEventStore alloc] init];
        
        _packetLength = -1;
        
        NSError *error;
        BOOL result = [_serverSocket acceptOnPort:kHostPort error:&error];
        
        if (result) {
            NSLog(@"conn result: %d, host: %@", result, _serverSocket.connectedHost);
        }else {
            NSLog(@"conn error: %@", error);
        }
        
    }
    return self;
}

- (void)dealloc  {
    
    self.serverSocket.delegate = nil;
    self.serverSocket = nil;
    
    self.clientSocket = nil;
    
}

- (int)byteWithInt:(nullable const void *)bytes {
    Byte *byte = (Byte *)bytes;
    int returnValue = 0;
    int byteCount = sizeof(byte)/2;
    for (int i=0; i<byteCount; i++) {
        if (i == 0) {
            returnValue = byte[byteCount -1];
        }else{
            returnValue += pow(256, i) * byte[byteCount-i-1];
        }
    }
    return returnValue;
}

- (void)sendData:(NSData *)data {
    
    NSMutableData *bodyData =  [NSMutableData dataWithData:data];
    [bodyData appendData:[kDivisionStr dataUsingEncoding:NSUTF8StringEncoding]];
    
    NSUInteger value = bodyData.length + 1;
    Byte byte[4] = {};
    byte[0] = (Byte) ((value>>24) & 0xFF);
    byte[1] = (Byte) ((value>>16) & 0xFF);
    byte[2] = (Byte) ((value>>8) & 0xFF);
    byte[3] = (Byte) (value & 0xFF);

    NSData *lengthData = [NSData dataWithBytes:byte length:sizeof(byte)];
    
    NSMutableData *mData = [NSMutableData dataWithData:lengthData];
    [mData appendData:[@"\n" dataUsingEncoding:NSUTF8StringEncoding]];
    [mData appendData:bodyData];
    
    [self.clientSocket writeData:mData withTimeout:-1 tag:0];

}

- (void)showInvitationVCWithSocket:(GCDAsyncSocket *)newSocket deviceName:(NSString *)deviceName
{
    
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:nil message:[NSString stringWithFormat:NSLocalizedString(@"receiveFileTips", nil), deviceName] preferredStyle:UIAlertControllerStyleAlert];
    
    kWeakSelf
    UIAlertAction *reciveAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"accept", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [weakSelf receiveInvitationSocket:newSocket accept:YES];
    }];
    
    [alertController addAction:reciveAction];
    
    UIAlertAction *rejectAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"reject", nil) style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
        [weakSelf receiveInvitationSocket:newSocket accept:NO];
    }];
    [alertController addAction:rejectAction];
    
    UITabBarController *tabController = (UITabBarController *)[AppDelegate getAppDelegate].window.rootViewController;
    UINavigationController *navContoller = (UINavigationController *)tabController.viewControllers[tabController.selectedIndex];
    
    [navContoller presentViewController:alertController animated:YES completion:nil];
}

- (void)receiveInvitationSocket:(GCDAsyncSocket *)newSocket accept:(BOOL)accept {

    if (accept) {
        NSDictionary *infoDict = @{@"option":@(1)};
        [self sendData:infoDict.mj_JSONData];
        
    }else {
        [newSocket disconnect];
    }
    
}

#pragma mark - GCDAsyncSocketDelegate
- (void)socket:(GCDAsyncSocket *)sock didAcceptNewSocket:(GCDAsyncSocket *)newSocket {
    //连接新的客户的Socket
    if (self.clientSocket) {
        return;
    }
    
    self.clientSocket = newSocket;
    [newSocket readDataToData:[@"\n" dataUsingEncoding:NSUTF8StringEncoding] withTimeout:-1 tag:0];

}

- (void)socket:(GCDAsyncSocket *)sock didReadData:(NSData *)data withTag:(long)tag {
    //读取客户端发送的数据
    
    self.transfSuccessed = NO;
    
    if (self.packetLength < 0) {
        self.packetLength = [self byteWithInt:data.bytes];
        
        if (self.packetLength < 0) {
            NSLog(@"error: 当前数据包的头为空");
            return;
        }
        
        [sock readDataToLength:self.packetLength - 1 withTimeout:-1 tag:0];
        return;
    }

    NSString *result = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    
    NSArray *components = [result componentsSeparatedByString:kDivisionStr];
    
    NSError *error;
    NSDictionary *infoDict = [NSJSONSerialization JSONObjectWithData:[components[0] dataUsingEncoding:NSUTF8StringEncoding] options:kNilOptions error:&error];
    
    if (error) {
        [self.clientSocket disconnect];
        return;
    }
    
    NSInteger option = [infoDict[@"option"] integerValue];
    
    if (option == 0) {
        
        NSString *deviceName = infoDict[@"deviceName"];
        
        PHAuthorizationStatus status = [PHPhotoLibrary authorizationStatus];
        if (status == PHAuthorizationStatusAuthorized) {
            [self showInvitationVCWithSocket:sock deviceName:deviceName];
            
        }else if (status == PHAuthorizationStatusNotDetermined) {
            kWeakSelf
            [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    if (status == PHAuthorizationStatusAuthorized) {
                        [weakSelf showInvitationVCWithSocket:sock deviceName:deviceName];
                        
                    }else {
                        [weakSelf receiveInvitationSocket:sock accept:NO];
                    }
                });
            }];
        }else {
            
            [self showAlertViewWithFileType:BSFileTypePhoto];
            [self receiveInvitationSocket:sock accept:NO];
        }
        
        self.packetLength = -1;
        [sock readDataToData:[@"\n" dataUsingEncoding:NSUTF8StringEncoding] withTimeout:-1 tag:0];
        return;
    }else if (option == 4) {
     
        self.transfSuccessed = YES;
        
        kWeakSelf
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            
            [weakSelf.clientSocket disconnect];
            weakSelf.clientSocket = nil;
            
            [weakSelf.progressVC.view removeFromSuperview];
            weakSelf.progressVC = nil;
        
        });
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [[NSNotificationCenter defaultCenter] postNotificationName:@"reciveDataSuccessed" object:nil];
        });
        
        self.packetLength = -1;
        [sock readDataToData:[@"\n" dataUsingEncoding:NSUTF8StringEncoding] withTimeout:-1 tag:0];
        return;
    }
    
    BSFileType reciveFileType = [infoDict[@"type"] integerValue];
    self.fileCount = [infoDict[@"fileCount"] integerValue];
    self.reciveFileNum = [infoDict[@"curIndex"] integerValue];
    
    kWeakSelf
    dispatch_async(dispatch_get_main_queue(), ^{
       
        if (!weakSelf.progressVC) {
            weakSelf.progressVC = [[BSTransferFileProgressVC alloc] init];
            weakSelf.progressVC.isSendFile = NO;
            weakSelf.progressVC.view.frame = [UIScreen mainScreen].bounds;
            [[AppDelegate getAppDelegate].window addSubview:weakSelf.progressVC.view];
            weakSelf.refreshProgress = weakSelf.progressVC.refreshProgress;
           
        }
       
        weakSelf.progressVC.fileType = reciveFileType;
        weakSelf.refreshProgress(weakSelf.reciveFileNum, weakSelf.fileCount);
   
    });
    
    NSData *bodyData;
    if (reciveFileType == 0 || reciveFileType == 1 || reciveFileType == 3) {
        bodyData = [components[1] jk_base64DecodedData];
    }else {
        bodyData = [components[1] dataUsingEncoding:NSUTF8StringEncoding];
    }
    
    switch (reciveFileType) {
        case BSFileTypePhoto:
        {
            //照片
            kWeakSelf
            [self recivePhotoData:bodyData callback:^{
                [weakSelf reciveDataSuccess];
            }];
        }
            break;
        case BSFileTypeVideo:
        {
            //视频
            kWeakSelf
            [self reciveVideoData:bodyData callback:^{
                [weakSelf reciveDataSuccess];
            }];
        }
            break;
        case  BSFileTypeContact:
        {
            //联系人
            kWeakSelf
            [self reciveContactData:bodyData callback:^{
                [weakSelf reciveDataSuccess];
            }];
        }
            break;
        case BSFileTypeMusic:
        {
            //音乐
            NSString *fileName = infoDict[@"fileName"];
            
            kWeakSelf
            [self reciveMusicData:bodyData fileName:fileName callback:^{
                [weakSelf reciveDataSuccess];
            }];
        }
            break;
        case BSFileTypeCalendar:
        {
            //日历
            if ([EKEventStore authorizationStatusForEntityType:EKEntityTypeEvent] == EKAuthorizationStatusAuthorized) {
                kWeakSelf
                [self reciveCalendarData:bodyData callback:^{
                    [weakSelf reciveDataSuccess];
                }];
                
            }else {
                kWeakSelf
                [self.eventStore requestAccessToEntityType:EKEntityTypeEvent completion:^(BOOL granted, NSError * _Nullable error) {
                    
                    if (granted) {
                        [weakSelf reciveCalendarData:bodyData callback:^{
                            [weakSelf reciveDataSuccess];
                        }];
                    }
                    
                }];
            }
            
        }
            break;
            
        default:
            break;
    }

    self.packetLength = -1;
    [sock readDataToData:[@"\n" dataUsingEncoding:NSUTF8StringEncoding] withTimeout:-1 tag:0];
}

- (void)socketDidDisconnect:(GCDAsyncSocket *)sock withError:(NSError *)err {
    //客户端socket断开连接
//    NSLog(@"-->>> 客户端socket断开连接 err: %@", err);
//    
//    self.clientSocket = nil;
//    
//    [self.progressVC.view removeFromSuperview];
//    self.progressVC = nil;
//    
//    if (!self.transfSuccessed && err) {
//        
//        UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"提示" message:@"连接中断，停止传输" preferredStyle:UIAlertControllerStyleAlert];
//        
//        kWeakSelf
//        UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:@"知道了" style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
//            
//             [weakSelf.progressVC.view removeFromSuperview];
//             weakSelf.progressVC = nil;
//            
//        }];
//        [alertController addAction:cancleAction];
//           
//        UITabBarController *tabController = (UITabBarController *)[AppDelegate getAppDelegate].window.rootViewController;
//        UINavigationController *navContoller = (UINavigationController *)tabController.viewControllers[tabController.selectedIndex];
//           
//        [navContoller presentViewController:alertController animated:YES completion:nil];
//    }

}

- (void)reciveDataSuccess {
    
    self.reciveFileNum++;
    
    kWeakSelf
    if (self.refreshProgress) {
        dispatch_async(dispatch_get_main_queue(), ^{
            weakSelf.refreshProgress(weakSelf.reciveFileNum, self.fileCount);
        });
    }
    
    
    NSDictionary *infoDict = @{
                               @"option":@(3)
                               };
    
    [self sendData:infoDict.mj_JSONData];
}


#pragma mark - Recive Data
- (void)recivePhotoData:(NSData *)data callback:(void(^)(void))callback{
    
    [[TZImageManager manager] savePhotoWithImage:[UIImage imageWithData:data] completion:^(PHAsset *asset, NSError *error) {
        callback();
    }];
    
}

- (void)reciveVideoData:(NSData *)data callback:(void(^)(void))callback{
    
    NSString *fileName = [NSString stringWithFormat:@"%f", [NSDate date].timeIntervalSince1970].jk_md5String;
    
    NSString *filtPath = [NSString stringWithFormat:@"%@.mp4", [[self getVideoDirectory] stringByAppendingPathComponent:fileName]];
    
    [data writeToFile:filtPath atomically:YES];
    
    [[TZImageManager manager] saveVideoWithUrl:[NSURL fileURLWithPath:filtPath] completion:^(PHAsset *asset, NSError *error) {
        callback();
    }];
   
}

- (void)reciveContactData:(NSData *)data callback:(void(^)(void))callback{
    
    NSDictionary *contactDict = data.mj_JSONObject;
    
    CNMutableContact *contact = [[CNMutableContact alloc] init];
    contact.familyName = contactDict[@"familyName"];
    contact.givenName = contactDict[@"givenName"];
    
    NSMutableArray<CNLabeledValue<CNPhoneNumber*>*> *phoneNumbers = @[].mutableCopy;
    NSArray *phones = contactDict[@"phones"];
    for (NSString *phone in phones) {
        CNPhoneNumber *phoneNumber = [CNPhoneNumber phoneNumberWithStringValue:phone];
        CNLabeledValue *labeledValue = [[CNLabeledValue alloc] initWithLabel:CNLabelPhoneNumberMobile value:phoneNumber];
        [phoneNumbers addObject:labeledValue];
    }
    
    contact.phoneNumbers = phoneNumbers.copy;
    
    CNSaveRequest *saveRequest = [[CNSaveRequest alloc] init];
    [saveRequest addContact:contact toContainerWithIdentifier:nil];
    
    CNContactStore *store = [[CNContactStore alloc] init];
    [store executeSaveRequest:saveRequest error:nil];
    
    callback();
    
}

- (void)reciveCalendarData:(NSData *)data callback:(void(^)(void))callback{
    
    NSDictionary *infoDict = data.mj_JSONObject;

    EKEvent *event = [EKEvent eventWithEventStore:self.eventStore];
    event.title = infoDict[@"title"];
    event.allDay = [infoDict[@"allDay"] boolValue];
    
    NSString *startDate = infoDict[@"startDate"];
    NSString *endDate = infoDict[@"endDate"];
    
    event.startDate = [[NSDate alloc] initWithTimeIntervalSince1970:[startDate doubleValue] / 1000];
    event.endDate = [[NSDate alloc] initWithTimeIntervalSince1970:[endDate doubleValue] / 1000];
    
    [event setCalendar:[self.eventStore defaultCalendarForNewEvents]];
    [self.eventStore saveEvent:event span:EKSpanThisEvent commit:YES error:nil];
    
    callback();
}

- (void)reciveMusicData:(NSData *)data fileName:(NSString *)fileName callback:(void(^)(void))callback{
    
    NSString *musicDirPath =  [[NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) firstObject] stringByAppendingPathComponent:@"music"];
    
    if (![[NSFileManager defaultManager] fileExistsAtPath:musicDirPath]) {
        [[NSFileManager defaultManager] createDirectoryAtPath:musicDirPath withIntermediateDirectories:YES attributes:nil error:nil];
    }
    
    NSString *musicPath = [musicDirPath stringByAppendingPathComponent:fileName];
    
    NSString *suffixName = [[fileName componentsSeparatedByString:@"."] lastObject];
    NSString *musicTitle = [fileName stringByReplacingOccurrencesOfString:[NSString stringWithFormat:@".%@", suffixName] withString:@""];
    
    if ([[NSFileManager defaultManager] fileExistsAtPath:musicPath]) {
        
        for (NSInteger i = 1; i < 10000; i++) {
            
            NSString *tempName = [NSString stringWithFormat:@"%@ (%ld).m4a", musicTitle, i];
            musicPath = [musicDirPath stringByAppendingPathComponent:tempName];
            
            if (![[NSFileManager defaultManager] fileExistsAtPath:musicPath]) {
                break;
            }
        }
    }
    
    [data writeToFile:musicPath atomically:YES];

    callback();
}


- (void)session:(nonnull MCSession *)session didReceiveStream:(nonnull NSInputStream *)stream withName:(nonnull NSString *)streamName fromPeer:(nonnull MCPeerID *)peerID {
    
}

- (void)session:(nonnull MCSession *)session didStartReceivingResourceWithName:(nonnull NSString *)resourceName fromPeer:(nonnull MCPeerID *)peerID withProgress:(nonnull NSProgress *)progress {
    
}


- (void)session:(nonnull MCSession *)session didFinishReceivingResourceWithName:(nonnull NSString *)resourceName fromPeer:(nonnull MCPeerID *)peerID atURL:(nullable NSURL *)localURL withError:(nullable NSError *)error {
    
}

- (NSString *)getVideoDirectory {
    
    NSString *cachesPath = [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES) lastObject];
    
    NSString *directory = [cachesPath stringByAppendingPathComponent:@"Videos"];
    
    BOOL fileExists = [[NSFileManager defaultManager] fileExistsAtPath:directory isDirectory:nil];
    if (!fileExists) {
        NSError *error = nil;
        [[NSFileManager defaultManager] createDirectoryAtPath:directory withIntermediateDirectories:YES attributes:nil error:&error];
        if (error) {
            NSLog(@"------>文件创建失败: %@", error);
        } else {
            
        }
    }
    
    return directory;
}

- (void)showAlertViewWithFileType:(BSFileType)fileType {
    
    NSString *title;
    NSString *message;
    
    switch (fileType) {
        case BSFileTypePhoto:
        {
            //相册权限
            title = NSLocalizedString(@"请授权相册权限", nil);
            message = NSLocalizedString(@"请在iPhone的\"设置-隐私-相册\"选项中,允许App访问你的相册", nil);
        }
            break;
        case BSFileTypeVideo:
        {
            //视频权限
            //相册权限
            title = NSLocalizedString(@"请授权相册权限", nil);
            message = NSLocalizedString(@"请在iPhone的\"设置-隐私-相册\"选项中,允许App访问你的相册", nil);
        }
            break;
        case BSFileTypeContact:
        {
            //通讯录权限
            title = NSLocalizedString(@"请授权通讯录权限", nil);
            message = NSLocalizedString(@"请在iPhone的\"设置-隐私-通讯录\"选项中,允许App访问你的通讯录", nil);
        }
            break;
        case BSFileTypeMusic:
        {
            //多媒体权限
            title = NSLocalizedString(@"请授权媒体与Apple Music权限", nil);
            message = NSLocalizedString(@"请在iPhone的\"设置-隐私-媒体与Apple Music\"选项中,允许App访问你的媒体与Apple Music", nil);
        }
            break;
        case BSFileTypeCalendar:
        {
            //日历权限
            title = NSLocalizedString(@"请授权日历权限", nil);
            message = NSLocalizedString(@"请在iPhone的\"设置-隐私-日历\"选项中,允许App访问你的日历", nil);
        }
            break;
            
        default:
            break;
    }
    
    if (!title) {
        return;
    }
    
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:title message:message preferredStyle: UIAlertControllerStyleAlert];
    
    UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"gotIt", @"知道了") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
    }];
    [alertController addAction:cancleAction];
    
    UIAlertAction *settingAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"toSettings", @"去设置") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        
        NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
        if ([[UIApplication sharedApplication] canOpenURL:url]) {
            [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
        }
    
    }];
    
    [alertController addAction:settingAction];
    
    UITabBarController *tabBarController = (UITabBarController *)[AppDelegate getAppDelegate].window.rootViewController;
    UINavigationController *navController = (UINavigationController *)tabBarController.viewControllers[tabBarController.selectedIndex];
    
    [navController presentViewController:alertController animated:YES completion:nil];
    
}


@end
