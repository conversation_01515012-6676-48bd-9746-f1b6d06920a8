//
//  BSAWSDynamoDBMgr.m
//  PhoneClone
//
//  Created by macbookair on 18/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSAWSDynamoDBMgr.h"

/// 亚马逊DynamoDB数据库中关联的表名
NSString *const AWSDynamoDBTable = @"phoneclone-mobilehub-*********-AccountTable";

@implementation BSAWSDynamoDBMgr
/// 查询DynamoDB数据库中的记录-根据[email]作为条件
/// @param email 邮箱
/// @param completionHandler 查询后的回调
+ (void)queryWithEmail:(NSString*)email
     completionHandler:(void (^)(AWSDynamoDBQueryOutput *response, NSError *error))completionHandler{
    AWSDynamoDB *dynamoDB = [AWSDynamoDB defaultDynamoDB];
    
    AWSDynamoDBCondition *conditon = [AWSDynamoDBCondition new];
    AWSDynamoDBAttributeValue *hashValue = [AWSDynamoDBAttributeValue new];
    hashValue.S = email;
    conditon.attributeValueList = @[hashValue];
    conditon.comparisonOperator = AWSDynamoDBComparisonOperatorEQ;
    
    AWSDynamoDBQueryInput *queryInput = [AWSDynamoDBQueryInput new];
    queryInput.tableName = AWSDynamoDBTable;
    queryInput.consistentRead = @YES;
    queryInput.keyConditions = @{Email_Of_DynamoDB_Key: conditon};
    [dynamoDB query:queryInput completionHandler:completionHandler];
}

/// 插入注册账号
/// @param accountModel 账号model对象
/// @param completionHandler 插入完成后的回调
+ (void)putItemWithAccountModel:(BSAccountModel*)accountModel
              completionHandler:(void (^)(AWSDynamoDBPutItemOutput *response, NSError *error))completionHandler{
    AWSDynamoDBAttributeValue *email = [AWSDynamoDBAttributeValue new];
    email.S = accountModel.email;
    
    AWSDynamoDBAttributeValue *createTime = [AWSDynamoDBAttributeValue new];
    createTime.S = accountModel.createTime;
    
    AWSDynamoDBAttributeValue *password = [AWSDynamoDBAttributeValue new];
    password.S = accountModel.password;
    
    AWSDynamoDBAttributeValue *path = [AWSDynamoDBAttributeValue new];
    path.S = accountModel.path;
    
    AWSDynamoDBAttributeValue *q1_ans = [AWSDynamoDBAttributeValue new];
    q1_ans.S = accountModel.q1_ans;
    
    AWSDynamoDBAttributeValue *q2_ans = [AWSDynamoDBAttributeValue new];
    q2_ans.S = accountModel.q2_ans;
    
    AWSDynamoDBAttributeValue *q3_ans = [AWSDynamoDBAttributeValue new];
    q3_ans.S = accountModel.q3_ans;
    
    AWSDynamoDBPutItemInput *putInput = [AWSDynamoDBPutItemInput new];
    putInput.tableName = AWSDynamoDBTable;
    putInput.item = @{
        Email_Of_DynamoDB_Key : email,
        CreateTime_Of_DynamoDB_Key : createTime,
        Password_Of_DynamoDB_Key : password,
        Path_Of_DynamoDB_Key : path,
        Q1_Ans_Of_DynamoDB_Key : q1_ans,
        Q2_Ans_Of_DynamoDB_Key : q2_ans,
        Q3_Ans_Of_DynamoDB_Key : q3_ans
    };
    [[AWSDynamoDB defaultDynamoDB] putItem:putInput completionHandler:completionHandler];
}

/// 修改密码
/// @param emailStr 用户账号(邮箱) - 主键
/// @param createTimeStr 注册时间 - 排序键
/// @param newPwdStr 新密码
/// @param completionHandler 修改完成后的回调
+ (void)updateItemWithEmail:(NSString*)emailStr
                 createTime:(NSString*)createTimeStr
                newPassword:(NSString*)newPwdStr
          completionHandler:(void (^)(AWSDynamoDBUpdateItemOutput *response, NSError *error))completionHandler{
    AWSDynamoDBUpdateItemInput *updateInput = [AWSDynamoDBUpdateItemInput new];
    updateInput.tableName = AWSDynamoDBTable;
    AWSDynamoDBAttributeValue *email = [AWSDynamoDBAttributeValue new];
    email.S = emailStr;
    AWSDynamoDBAttributeValue *createTime = [AWSDynamoDBAttributeValue new];
    createTime.S = createTimeStr;
    updateInput.key = @{
        Email_Of_DynamoDB_Key: email,
        CreateTime_Of_DynamoDB_Key:createTime,
    };
    
    AWSDynamoDBAttributeValue *updatedValue = [AWSDynamoDBAttributeValue new];
    updatedValue.S = newPwdStr;
    AWSDynamoDBAttributeValueUpdate *valueUpdate = [AWSDynamoDBAttributeValueUpdate new];
    valueUpdate.value = updatedValue;
    valueUpdate.action = AWSDynamoDBAttributeActionPut;
    
    updateInput.attributeUpdates = @{Password_Of_DynamoDB_Key : valueUpdate};
    updateInput.returnValues = AWSDynamoDBReturnValueUpdatedNew;
    
    [[AWSDynamoDB defaultDynamoDB] updateItem:updateInput completionHandler:completionHandler];
}



+ (void)deleteUserWithEmail:(NSString*)emailStr
                 createTime:(NSString*)createTimeStr
          completionHandler:(void (^)(AWSDynamoDBDeleteItemOutput *response, NSError *error))completionHandler{
          
    [BSAWSDynamoDBMgr queryWithEmail:emailStr completionHandler:^(AWSDynamoDBQueryOutput * _Nonnull response, NSError * _Nonnull error) {
         if(response.count>0)
         {
             AWSDynamoDBQueryOutput *getItemOutput = response;
             NSDictionary *itemDictionary = [getItemOutput.items firstObject];
             AWSDynamoDBDeleteItemInput *deleteInput = [AWSDynamoDBDeleteItemInput new];
             deleteInput.tableName = AWSDynamoDBTable;
             deleteInput.key = @{
                 Email_Of_DynamoDB_Key: itemDictionary[@"email"],
                 CreateTime_Of_DynamoDB_Key:itemDictionary[@"createTime"],
             };;
             [[AWSDynamoDB defaultDynamoDB] deleteItem:deleteInput completionHandler:completionHandler];
         }
    }];
    
    
    
  
    
    
}
@end
