//
//  BSNewSendServer.m
//  PhoneCloneV2
//
//  Created by fs0011 on 2024/1/15.
//  Copyright © 2024 PhoneClone. All rights reserved.
//

#import "BSNewSendServer.h"

//
//  BSRetSendFileServer.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSRetSendFileServer.h"

#import "BSTransferFileProgressVC.h"

#import "BSPeerModel.h"
#import "BSMusic.h"
#import "BSContact.h"
#import "BSCalender.h"

#import "BSTransferRes.h"
#import "BSResGroupModel.h"

#import <TZImageManager.h>
#import <MJExtension.h>
#import <GCDAsyncSocket.h>

#define kDivisionStr     @"05162012"

@interface BSNewSendServer ()

@property (nonatomic, strong) BSTransferFileProgressVC *progressVC;

@property (nonatomic, strong) BSTransferRes *transferRes;
@property (nonatomic, strong) NSArray *resGroupArray;

@property (nonatomic, copy) void(^refreshProgress)(NSInteger currentIndex, NSInteger totalCount);

@property (nonatomic, assign) NSInteger packetLength;
@property (nonatomic, assign) BOOL transfSuccessed;

@end

@implementation BSNewSendServer





- (void)connectToHost:(NSString *)host datas:(NSArray *)resGroupArray {
    
    self.transfSuccessed = NO;
    
    NSError *error;
//    BOOL result = [self.clientSocket connectToHost:host onPort:kHostPort error:&error];
    
//    if (result) {
        self.resGroupArray = resGroupArray;
//    }
    BSResType resType = 0;
       
    for (NSInteger i = 0; i < self.resGroupArray.count; i++) {
        BSResGroupModel *resGroup = self.resGroupArray[i];
        if (resGroup.isSelected) {
            resType = resGroup.resType;
            break;
        }
    }
       
   
    
    self.transferRes = [[BSTransferRes alloc] init];
//    NSLog(@"conn result: %d error: %@", result, error);
    [self sendResData];
}




#pragma mark - GCDAsyncSocketDelegate




#pragma mark - Send Data
- (void)sendResData {
    
    if (self.transferRes.resArray.count == 0) {
        
        [self pkgResArray];
        
        self.transferRes.total = self.transferRes.resArray.count;
        self.transferRes.index = 0;
        
        if (self.transferRes.resArray.count == 0) {
            //内容传输完成
            self.transfSuccessed = YES;
            
//            [self sendData:nil option:4 userInfo:nil];
                   
           kWeakSelf
            
            
           dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
               
//               [[NSNotificationCenter defaultCenter] postNotificationName:@"reciveDataSuccessed" object:nil userInfo:nil];
               
               [self.progressVC.view removeFromSuperview];
               UIView* v = self.progressVC.view;
               weakSelf.progressVC = nil;
    
//               [weakSelf.clientSocket disconnect];
//               weakSelf.clientSocket = nil;
               
               
           });
            
            return;
        }
        
        
        if (!self.progressVC) {
             self.progressVC = [[BSTransferFileProgressVC alloc] init];
             self.progressVC.isSendFile = YES;
             self.progressVC.view.frame = [UIScreen mainScreen].bounds;
             [[AppDelegate getAppDelegate].window addSubview:self.progressVC.view];
             self.refreshProgress = self.progressVC.refreshProgress;
            
         }
        
        
        self.progressVC.fileType = self.transferRes.fileType;
        kWeakSelf
        if (self.refreshProgress) {
            dispatch_async(dispatch_get_main_queue(), ^{
                weakSelf.refreshProgress(weakSelf.transferRes.index, weakSelf.transferRes.total);
            });
       }
    
    }
    
    switch (self.transferRes.fileType) {
        case BSFileTypePhoto:
        {
            //照片
            [self sendPhotoData];
        }
            break;
        case BSFileTypeVideo:
        {
            //视频
             [self sendVideoData];
        }
            break;
        case BSFileTypeContact:
        {
            //联系人
            [self sendContactData];
        }
            break;
        case BSFileTypeMusic:
        {
            //音乐
            [self sendMusicData];
        }
            break;
        case BSFileTypeCalendar:
        {
            //日历
            [self sendCalendartData];
        }
            break;
            
        default:
            break;
    }
    
    kWeakSelf
     if (self.refreshProgress) {
         dispatch_async(dispatch_get_main_queue(), ^{
             weakSelf.refreshProgress(weakSelf.transferRes.index, weakSelf.transferRes.total);
         });
    }
    
}

- (void)pkgResArray {
    
    for (NSInteger i = self.transferRes.cursor; i < self.resGroupArray.count; i++) {
        BSResGroupModel *resGroup = self.resGroupArray[i];
        
        if (resGroup.isSelected) {
            
            self.transferRes.cursor = i;
            
            switch (resGroup.resType) {
                case BSResTypePhoto:
                {
                    //照片
                    self.transferRes.fileType = BSFileTypePhoto;
                    
                    for (TZAssetModel *model in resGroup.resArray) {
                        if (model.isSelected) {
                            [self.transferRes.resArray addObject:model];
                        }
                    }
                    
                }
                    break;
                case BSResTypeVideo:
                {
                    //视频
                    self.transferRes.fileType = BSFileTypeVideo;
                    
                    for (TZAssetModel *model in resGroup.resArray) {
                        if (model.isSelected) {
                            [self.transferRes.resArray addObject:model];
                        }
                    }
                    
                }
                    break;
                case BSResTypeContact:
                {
                    //联系人
                    self.transferRes.fileType = BSFileTypeContact;
                    NSMutableArray* arr = [NSMutableArray array];
                    for (BSContact *model in resGroup.resArray) {
                        if (model.isSelected) {
                            
                            NSMutableArray *phones = @[].mutableCopy;
                            
                            for (CNLabeledValue *labelValue in model.contact.phoneNumbers) {
                                CNPhoneNumber *phoneNumber = labelValue.value;
                                [phones addObject:phoneNumber.stringValue];
                            }
                            NSString *nameStr = [NSString stringWithFormat:@"%@%@",model.contact.familyName,model.contact.givenName];
                            NSDictionary *infoDict = @{
                                                       @"ContactName":nameStr,
                                                       @"ContactPhotoNumberArray":phones
                                                       };
                            
                            [arr addObject:infoDict];
                        }
                    }
                    [self.transferRes.resArray addObject:arr];
                }
                    break;
                case BSResTypeMusic:
                {
                    //音乐
                    self.transferRes.fileType = BSFileTypeMusic;
                    
                    for (BSMusic *model in resGroup.resArray) {
                           if (model.isSelected) {
                               [self.transferRes.resArray addObject:model];
                           }
                       }
                }
                    break;
                case BSResTypeCalendar:
                {
                    //日历
                    self.transferRes.fileType = BSFileTypeCalendar;
                    NSMutableArray * arr = [NSMutableArray array];
                    for (BSCalender *model in resGroup.resArray) {
                        if (model.isSelected) {
                            [arr addObject:model.infoDict];
                        }
                    }
                    [self.transferRes.resArray addObject:arr];
                }
                    break;
                    
                default:
                    break;
            }
            
            break;
            
        }
        
    }
    
    self.transferRes.cursor += 1;
}

- (void)sendPhotoData {
    
    TZAssetModel *model = [self.transferRes.resArray lastObject];
    
    kWeakSelf
    [[TZImageManager manager] getOriginalPhotoDataWithAsset:model.asset completion:^(NSData *data, NSDictionary *info, BOOL isDegraded) {
      
        dispatch_sync(dispatch_get_global_queue(0, 0), ^{
           
            NSURL *fileURL = info[@"PHImageFileURLKey"];
            NSString *fileURLString = fileURL.absoluteString;
            NSString *suffixName = [[fileURLString componentsSeparatedByString:@"."] lastObject];
            
            if (suffixName.length == 0) {
                suffixName = @"jpg";
            }
            
            NSString *fileName = [NSString stringWithFormat:@"%@.%@", [NSString stringWithFormat:@"%f", [NSDate date].timeIntervalSince1970].jk_md5String, suffixName];
            [self completeWithData:data completion:^(NSString *outputPath) {
                [self posturl:[NSURL fileURLWithPath:outputPath] fileType:2];
            }];
            
            
           
            
        });
    }];
  
}


- (void)completeWithData:(NSData *)imageData completion:(void (^)(NSString *))completionBlock {
    // 确定文件保存位置
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    NSString *documentsDirectory = [paths objectAtIndex:0];
    NSString *ditpath = [documentsDirectory stringByAppendingPathComponent:@"cacheFile"];
    
    NSError *error = nil;
    if (![[NSFileManager defaultManager] fileExistsAtPath:ditpath]) {
        [[NSFileManager defaultManager] createDirectoryAtPath:ditpath withIntermediateDirectories:YES attributes:nil error:&error];
        if (error) {
            // 处理错误
        }
    }
    
    NSString *filePath = [documentsDirectory stringByAppendingPathComponent:[NSString stringWithFormat:@"%lf_Image.png", [[NSDate date] timeIntervalSince1970]]];
    // 自定义文件名
    
    // 将图片数据写入文件
    [imageData writeToFile:filePath options:NSDataWritingAtomic error:&error];
    
    if (error) {
        NSLog(@"Error saving image: %@", error);
        completionBlock(nil); // 处理错误
    } else {
        completionBlock(filePath); // 返回文件路径
    }
}

- (void)sendVideoData {
    
    TZAssetModel *model = [self.transferRes.resArray lastObject];
    
    kWeakSelf
    [[TZImageManager manager] getVideoOutputPathWithAsset:model.asset success:^(NSString *outputPath) {
        
        dispatch_sync(dispatch_get_global_queue(0, 0), ^{
            
            NSData *data = [[NSData alloc] initWithContentsOfURL:[NSURL fileURLWithPath:outputPath]];
            
            NSString *suffixName = [[outputPath componentsSeparatedByString:@"."] lastObject];
            
            NSString *fileName = [NSString stringWithFormat:@"%@.%@", [NSString stringWithFormat:@"%f", [NSDate date].timeIntervalSince1970].jk_md5String, suffixName];
            
            [self posturl:[NSURL fileURLWithPath:outputPath] fileType:4];
            
        });
        
    } failure:^(NSString *errorMessage, NSError *error) {
        
    }];
    
}

- (void)sendContactData {
    
    kWeakSelf
    dispatch_sync(dispatch_get_global_queue(0, 0), ^{
        
        NSArray *infoDict = [weakSelf.transferRes.resArray lastObject];

        
        
        
        NSString* calendarEventPath = [SAVEIMAGEFILE stringByAppendingPathComponent:@"myContacts.json"];
        if ([[NSFileManager defaultManager] fileExistsAtPath:calendarEventPath]) {
            [[NSFileManager defaultManager] removeItemAtPath:calendarEventPath error:nil];
        }
        NSData *plist_data = [NSJSONSerialization dataWithJSONObject:infoDict options:NSJSONWritingPrettyPrinted error:nil];
        BOOL a =   [plist_data writeToFile:calendarEventPath atomically:YES];
        if (a) {
            NSLog(@"路径：%@",calendarEventPath);
            [self posturl:[NSURL fileURLWithPath:calendarEventPath] fileType:1];
        }else {
            NSLog(@"存储失败");
        }
        
    });
    
}

- (void)sendCalendartData {
    
    kWeakSelf
    dispatch_sync(dispatch_get_global_queue(0, 0), ^{
        
        NSArray *infoDict = [weakSelf.transferRes.resArray lastObject];
        
        
        
        
        NSString* calendarEventPath = [SAVEIMAGEFILE stringByAppendingPathComponent:@"myCalendarEvent.json"];
        if ([[NSFileManager defaultManager] fileExistsAtPath:calendarEventPath]) {
            [[NSFileManager defaultManager] removeItemAtPath:calendarEventPath error:nil];
        }
        NSData *plist_data = [NSJSONSerialization dataWithJSONObject:infoDict options:NSJSONWritingPrettyPrinted error:nil];
        BOOL a =   [plist_data writeToFile:calendarEventPath atomically:YES];
        if (a) {
            NSLog(@"路径：%@",calendarEventPath);
            [self posturl:[NSURL fileURLWithPath:calendarEventPath] fileType:5];
        }else {
            NSLog(@"存储失败");
        }
//        NSString *startDateStr = mInfoDict[@"startDate"];
//        NSString *endDateStr = mInfoDict[@"endDate"];
//        
//        NSDate *startDate = [NSDate jk_dateWithString:startDateStr format:@"yyyy-MM-dd HH:mm:ss"];
//        NSDate *endDate = [NSDate jk_dateWithString:endDateStr format:@"yyyy-MM-dd HH:mm:ss"];
//        
//        [mInfoDict setObject:@(startDate.timeIntervalSince1970 * 1000) forKey:@"startDate"];
//        [mInfoDict setObject:@(endDate.timeIntervalSince1970 * 1000) forKey:@"endDate"];
        
//        [weakSelf sendData:mInfoDict.mj_JSONData option:2 userInfo:nil];
        
    });
    
}

- (void)sendMusicData {
    
    BSMusic *music = [self.transferRes.resArray lastObject];
    
    NSString *musicTitle = music.infoDict[@"title"];
    NSURL *musicUrl = music.infoDict[@"fileURL"];
    
    NSString *musicFileName = [NSString stringWithFormat:@"%@.m4a", musicTitle];
    
    NSString *cachesPath = [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES) lastObject];
    
    //Music
    AVURLAsset *songAsset = [AVURLAsset URLAssetWithURL:musicUrl options:nil];
    [AVAssetExportSession exportPresetsCompatibleWithAsset:songAsset];
    
    AVAssetExportSession *session = [[AVAssetExportSession alloc] initWithAsset:songAsset presetName:AVAssetExportPresetAppleM4A];
    session.outputFileType=@"com.apple.m4a-audio";
    
    NSString *musicPath = [cachesPath stringByAppendingPathComponent:musicFileName];
    if ([[NSFileManager defaultManager] fileExistsAtPath:musicPath]) {
        [[NSFileManager defaultManager] removeItemAtPath:musicPath error:nil];
    }
    
    session.outputURL = [NSURL fileURLWithPath:musicPath];
    
    kWeakSelf
    [session exportAsynchronouslyWithCompletionHandler:^{
        
        dispatch_sync(dispatch_get_global_queue(0, 0), ^{
            
//            [weakSelf sendData:[NSData dataWithContentsOfURL:[NSURL fileURLWithPath:musicPath]] option:2 userInfo:@{@"fileName": musicFileName}];
            [self posturl:[NSURL fileURLWithPath:musicPath] fileType:3];
            
        });
        
    }];
    
}

- (void)posturl:(NSURL*)url  fileType:(NSInteger)type
{
    if (self.transferRes.resArray.count > 0){
        [self.transferRes.resArray removeLastObject];
    }
    
    self.transferRes.index++;
    
    
    
    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    AppDelegate* app = APPDELEGATE;
    
    // 设置请求序列化器
    manager.requestSerializer = [AFHTTPRequestSerializer serializer];
    manager.responseSerializer = [AFJSONResponseSerializer serializer];
    UIDevice *currentDevice = [UIDevice currentDevice];
    NSString *deviceName = currentDevice.name;
    // 创建请求
    [manager.requestSerializer setValue:app.currentLinkToken forHTTPHeaderField:@"authorizationToken"];
    [manager.requestSerializer setValue:[NSString stringWithFormat:@"%ld",type] forHTTPHeaderField:@"fileType"];
    [manager.requestSerializer setValue:deviceName forHTTPHeaderField:@"deviceName"];
    NSMutableSet *acceptableContentTypes = [NSMutableSet setWithSet:manager.responseSerializer.acceptableContentTypes];
    [acceptableContentTypes addObject:@"text/html"];
    [acceptableContentTypes addObject:@"text/plain"];
    manager.responseSerializer.acceptableContentTypes = acceptableContentTypes;
    NSLog(@"%@",[NSString stringWithFormat:@"%@/upload",app.currentLinkUrl]);
    NSString* urlLink = [NSString stringWithFormat:@"%@/upload",app.currentLinkUrl];
    if(![app.currentLinkUrl containsString:@"http"])
    {
        urlLink = [NSString stringWithFormat:@"http://%@/upload",app.currentLinkUrl];
    }
    NSURLSessionDataTask* task = [manager POST:urlLink  parameters:nil headers:nil constructingBodyWithBlock:^(id<AFMultipartFormData>  _Nonnull formData) {
        // 添加文件
        [formData appendPartWithFileURL:url name:@"file" error:nil];
    } progress:^(NSProgress * _Nonnull uploadProgress) {
//        double progressPercentage = uploadProgress.fractionCompleted * 100;
//        dispatch_async(dispatch_get_main_queue(), ^{
//            [inview updatePrecent:progressPercentage];
//            if(progressPercentage>=100)
//            {
//
//                [inview complete];
//            }
//        });
        
        
        
    } success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self sendResData];
            NSLog(@"完成");
        });
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        NSLog(@"%ld",error.code);
        [self sendResData];
        if(error.code==-1004)
        {
            
            if (self.progressVC && !self.transfSuccessed && error) {
                UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"提示" message:@"连接中断，停止传输" preferredStyle:UIAlertControllerStyleAlert];
                
                kWeakSelf
                UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:@"知道了" style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
                    
                    [weakSelf.progressVC.view removeFromSuperview];
                    weakSelf.progressVC = nil;
                    
                }];
                [alertController addAction:cancleAction];
                
                UITabBarController *tabController = (UITabBarController *)[AppDelegate getAppDelegate].window.rootViewController;
                UINavigationController *navContoller = (UINavigationController *)tabController.viewControllers[tabController.selectedIndex];
                
                [navContoller presentViewController:alertController animated:YES completion:nil];
            }
            
            [self.progressVC.view removeFromSuperview];
            self.progressVC = nil;
            
            [SVProgressHUD showInfoWithStatus:local(@"对方连接中断请尝试重新加入")];
            
        }


    }];
    


    
 
}
@end

