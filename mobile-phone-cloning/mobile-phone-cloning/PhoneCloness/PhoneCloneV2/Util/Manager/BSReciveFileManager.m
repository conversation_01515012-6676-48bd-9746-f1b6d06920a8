//
//  BSReciveFileManager.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSReciveFileManager.h"
#import "BSTransferFileProgressVC.h"

#import <SSZipArchive.h>
#import <TZImageManager.h>
#import <MJExtension.h>
#import <Contacts/Contacts.h>
#import <ContactsUI/ContactsUI.h>
#import <EventKit/EventKit.h>
#import <MultipeerConnectivity/MultipeerConnectivity.h>

@interface BSReciveFileManager () <MCSessionDelegate, MCNearbyServiceAdvertiserDelegate, MCNearbyServiceBrowserDelegate>

@property (strong,nonatomic) MCSession *session;
@property (strong,nonatomic) MCNearbyServiceAdvertiser *nearbyServiceAdveriser;
@property (strong, nonatomic) MCNearbyServiceBrowser *nearbyServiceBrowser;

@property (nonatomic, strong) EKEventStore *eventStore;

@property (nonatomic, strong) BSPeerModel *peerModel;
@property (nonatomic, assign) NSInteger fileCount;
@property (nonatomic, assign) NSInteger reciveFileNum;
@property (nonatomic, assign) BSFileType reciveFileType;

@property (nonatomic, strong) BSTransferFileProgressVC *progressVC;

@property (nonatomic, copy) void(^refreshProgress)(NSInteger currentIndex, NSInteger totalCount);

@end

@implementation BSReciveFileManager

static BSReciveFileManager *instance;

+ (instancetype)manager {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[BSReciveFileManager alloc] init];
        [instance initConfig];
    });
    return instance;
}

- (void)initConfig {
    
    //创建会话(两边的回话类型必须一致)
    MCPeerID *peerID = [[MCPeerID alloc] initWithDisplayName:[[UIDevice currentDevice] name]];
    self.session = [[MCSession alloc] initWithPeer:peerID securityIdentity:nil encryptionPreference:MCEncryptionNone];
    self.session.delegate = self;

    //广播通知
    self.nearbyServiceAdveriser = [[MCNearbyServiceAdvertiser alloc] initWithPeer:peerID discoveryInfo:@{@"ids": [UIDevice currentDevice].identifierForVendor.UUIDString} serviceType:@"rsp-receiver"];
    self.nearbyServiceAdveriser.delegate = self;
    [self.nearbyServiceAdveriser startAdvertisingPeer];
    
    //监听广播
    self.nearbyServiceBrowser = [[MCNearbyServiceBrowser alloc] initWithPeer:peerID serviceType:@"rsp-sender"];
    self.nearbyServiceBrowser.delegate = self;
    [self.nearbyServiceBrowser startBrowsingForPeers];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleNotification) name:@"applicationDidEnterBackground" object:nil];
    
    self.eventStore = [[EKEventStore alloc] init];
    
}

- (void)handleNotification {
    
    if (self.peerModel.peerID) {
        
        [self.session disconnect];
        [self.progressVC.view removeFromSuperview];
        self.progressVC = nil;
        
        UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"提示" message:@"连接中断，停止传输" preferredStyle:UIAlertControllerStyleAlert];
        
        kWeakSelf
        UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:@"知道了" style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
            weakSelf.peerModel = nil;
        
        }];
        [alertController addAction:cancleAction];
        
        UITabBarController *tabController = (UITabBarController *)[AppDelegate getAppDelegate].window.rootViewController;
        UINavigationController *navContoller = (UINavigationController *)tabController.viewControllers[tabController.selectedIndex];
        
        [navContoller presentViewController:alertController animated:YES completion:nil];
        
    }
    
}

#pragma mark - MCNearbyServiceBrowserDelegate
- (void)browser:(MCNearbyServiceBrowser *)browser foundPeer:(MCPeerID *)peerID
withDiscoveryInfo:(nullable NSDictionary<NSString *, NSString *> *)info {
    // 发现了附近的广播节点
}

- (void)browser:(MCNearbyServiceBrowser *)browser lostPeer:(MCPeerID *)peerID {
    // 广播节点丢失
    
    if (self.peerModel.peerID && [self.peerModel.peerID.displayName isEqualToString:peerID.displayName]) {
        
        [self.session disconnect];
        [self.progressVC.view removeFromSuperview];
        self.progressVC = nil;
        
        UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"提示" message:@"连接中断，停止传输" preferredStyle:UIAlertControllerStyleAlert];
        
        kWeakSelf
        UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:@"知道了" style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
            weakSelf.peerModel = nil;
            
        }];
        [alertController addAction:cancleAction];
        
        UITabBarController *tabController = (UITabBarController *)[AppDelegate getAppDelegate].window.rootViewController;
        UINavigationController *navContoller = (UINavigationController *)tabController.viewControllers[tabController.selectedIndex];
        
        [navContoller presentViewController:alertController animated:YES completion:nil];
        
        
    }
    
}

- (void)browser:(MCNearbyServiceBrowser *)browser didNotStartBrowsingForPeers:(NSError *)error {
    // 搜索失败回调
    NSLog(@"搜索出错：%@", error.localizedDescription);
}


#pragma mark - MCNearbyServiceAdvertiserDelegate
- (void)advertiser:(MCNearbyServiceAdvertiser *)advertiser
didReceiveInvitationFromPeer:(MCPeerID *)peerID
       withContext:(nullable NSData *)context
 invitationHandler:(void (^)(BOOL accept, MCSession * __nullable session))invitationHandler {
    // 收到节点邀请回调(发送者发出邀请，接收者接收邀请)
    
    if (self.peerModel) {
        invitationHandler(NO, self.session);
        return;
    }
    
    PHAuthorizationStatus status = [PHPhotoLibrary authorizationStatus];
    if (status == PHAuthorizationStatusAuthorized) {
        
        [self receiveInvitationFromPeer:peerID withContext:context invitationHandler:invitationHandler];
        
    }else if (status == PHAuthorizationStatusNotDetermined) {
        kWeakSelf
        [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (status == PHAuthorizationStatusAuthorized) {
                    [weakSelf receiveInvitationFromPeer:peerID withContext:context invitationHandler:invitationHandler];
                    
                }else {
                    invitationHandler(NO, weakSelf.session);
                }
            });
        }];
    }else {
        
        [self showAlertViewWithFileType:BSFileTypePhoto];
        invitationHandler(NO, self.session);
    }

    
}

- (void)advertiser:(MCNearbyServiceAdvertiser *)advertiser didNotStartAdvertisingPeer:(NSError *)error {
    NSLog(@"%@节点广播失败", advertiser.myPeerID.displayName);
}

- (void)receiveInvitationFromPeer:(MCPeerID *)peerID
                         withContext:(nullable NSData *)context
invitationHandler:(void (^)(BOOL accept, MCSession * __nullable session))invitationHandler {
    
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:nil message:[NSString stringWithFormat:NSLocalizedString(@"receiveFileTips", nil), peerID.displayName] preferredStyle:UIAlertControllerStyleAlert];
    
    kWeakSelf
    UIAlertAction *reciveAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"accept", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        
        NSDictionary *infoDict = [context mj_JSONObject];
        NSString *ids = infoDict[@"ids"];
        
        BSPeerModel *peerModel = [[BSPeerModel alloc] init];
        peerModel.peerID = peerID;
        peerModel.ids = ids;
        weakSelf.peerModel = peerModel;
        
        invitationHandler(YES, weakSelf.session);
    }];
    
    [alertController addAction:reciveAction];
    
    UIAlertAction *rejectAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"reject", nil) style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
        invitationHandler(NO, weakSelf.session);
    }];
    [alertController addAction:rejectAction];
    
    UITabBarController *tabController = (UITabBarController *)[AppDelegate getAppDelegate].window.rootViewController;
    UINavigationController *navContoller = (UINavigationController *)tabController.viewControllers[tabController.selectedIndex];
    
    [navContoller presentViewController:alertController animated:YES completion:nil];
}

#pragma mark - MCSessionDelegate
- (void)session:(MCSession *)session peer:(MCPeerID *)peerID didChangeState:(MCSessionState)state{
    
    switch (state) {
        case MCSessionStateNotConnected:
        {
            //未连接
            NSLog(@"未连接");
        }
            break;
        case MCSessionStateConnecting:
        {
            //连接中
            NSLog(@"连接中");
        }
            break;
        case MCSessionStateConnected:
        {
            //连接完成
            NSLog(@"连接完成");
            
        }
            break;
    }
}

- (void)session:(MCSession *)session didReceiveData:(NSData *)data fromPeer:(MCPeerID *)peerID {
    
    //效验
    NSError *error;
    NSDictionary *infoDict = [NSJSONSerialization JSONObjectWithData:data options:kNilOptions error:&error];
    
    if (!error && [infoDict.allKeys containsObject:@"sendSuccess"]) {
        
        BOOL sendSuccess = [infoDict[@"sendSuccess"] boolValue];
        
        if (sendSuccess) {
            
            kWeakSelf
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [weakSelf.progressVC.view removeFromSuperview];
                weakSelf.progressVC = nil;
                
                [weakSelf.session disconnect];
                weakSelf.peerModel = nil;
            });
            
            dispatch_async(dispatch_get_main_queue(), ^{
                [[NSNotificationCenter defaultCenter] postNotificationName:@"reciveDataSuccessed" object:nil userInfo:@{@"reciveType": @(self.reciveFileType)}];
            });
            
        }else {
            
            
            if (self.fileCount > 0) {
                
                dispatch_async(dispatch_get_main_queue(), ^{
                    [[NSNotificationCenter defaultCenter] postNotificationName:@"reciveDataSuccessed" object:nil userInfo:@{@"reciveType": @(self.reciveFileType)}];
                });
                
            }
            
            self.fileCount = [infoDict[@"fileCount"] integerValue];
            self.reciveFileType = [infoDict[@"fileType"] integerValue];
            self.reciveFileNum = 0;
            
            NSDictionary *infoDict = @{
                                       @"ids": self.peerModel.ids,
                                       @"receiveSuccess":@(1)
                                       };
            
            [self.session sendData:infoDict.mj_JSONData toPeers:@[self.peerModel.peerID] withMode:MCSessionSendDataReliable error:nil];
            
            kWeakSelf
            dispatch_async(dispatch_get_main_queue(), ^{
                
                if (!weakSelf.progressVC) {
                    weakSelf.progressVC = [[BSTransferFileProgressVC alloc] init];
                    weakSelf.progressVC.isSendFile = NO;
                    weakSelf.progressVC.view.frame = [UIScreen mainScreen].bounds;
                    [[AppDelegate getAppDelegate].window addSubview:weakSelf.progressVC.view];
                    weakSelf.refreshProgress = self.progressVC.refreshProgress;
                    
                }
                
                weakSelf.progressVC.fileType = weakSelf.reciveFileType;
                weakSelf.refreshProgress(weakSelf.reciveFileNum, weakSelf.fileCount);
            
            });

        }
        
        return;
        
    }
    
    switch (self.reciveFileType) {
        case BSFileTypePhoto:
        {
            //照片
            kWeakSelf
            [self recivePhotoData:data callback:^{
                [weakSelf reciveDataSuccess];
            }];
        }
            break;
        case BSFileTypeVideo:
        {
            //视频
            kWeakSelf
            [self reciveVideoData:data callback:^{
                [weakSelf reciveDataSuccess];
            }];
        }
            break;
        case  BSFileTypeContact:
        {
            //联系人
            kWeakSelf
            [self reciveContactData:data callback:^{
                [weakSelf reciveDataSuccess];
            }];
        }
            break;
        case BSFileTypeMusic:
        {
            //音乐
            kWeakSelf
            [self reciveMusicData:data callback:^{
                [weakSelf reciveDataSuccess];
            }];
        }
            break;
        case BSFileTypeCalendar:
        {
            //日历
            if ([EKEventStore authorizationStatusForEntityType:EKEntityTypeEvent] == EKAuthorizationStatusAuthorized) {
                kWeakSelf
                [self reciveCalendarData:data callback:^{
                    [weakSelf reciveDataSuccess];
                }];
                
            }else {
                kWeakSelf
                [self.eventStore requestAccessToEntityType:EKEntityTypeEvent completion:^(BOOL granted, NSError * _Nullable error) {
                    
                    if (granted) {
                        [weakSelf reciveCalendarData:data callback:^{
                            [weakSelf reciveDataSuccess];
                        }];
                    }
                    
                }];
            }
            
        }
            break;
            
        default:
            break;
    }

}

- (void)reciveDataSuccess {
    
    self.reciveFileNum++;
    
    kWeakSelf
    if (self.refreshProgress) {
        dispatch_async(dispatch_get_main_queue(), ^{
            weakSelf.refreshProgress(weakSelf.reciveFileNum, self.fileCount);
        });
    }
    
    
    NSDictionary *infoDict = @{
                               @"receiveSuccess":@(1)
                               };
    
    [self.session sendData:infoDict.mj_JSONData toPeers:@[self.peerModel.peerID] withMode:MCSessionSendDataReliable error:nil];
}

#pragma mark - Recive Data
- (void)recivePhotoData:(NSData *)data callback:(void(^)(void))callback{
    
    [[TZImageManager manager] savePhotoWithImage:[UIImage imageWithData:data] completion:^(PHAsset *asset, NSError *error) {
        callback();
    }];
    
}

- (void)reciveVideoData:(NSData *)data callback:(void(^)(void))callback{
    
    NSString *fileName = [NSString stringWithFormat:@"%f", [NSDate date].timeIntervalSince1970].jk_md5String;
    
    NSString *filtPath = [NSString stringWithFormat:@"%@.mp4", [[self getVideoDirectory] stringByAppendingPathComponent:fileName]];
    
    [data writeToFile:filtPath atomically:YES];
    
    [[TZImageManager manager] saveVideoWithUrl:[NSURL fileURLWithPath:filtPath] completion:^(PHAsset *asset, NSError *error) {
        callback();
    }];
   
}

- (void)reciveContactData:(NSData *)data callback:(void(^)(void))callback{
    
    NSDictionary *contactDict = data.mj_JSONObject;
    
    CNMutableContact *contact = [[CNMutableContact alloc] init];
    contact.familyName = contactDict[@"familyName"];
    contact.givenName = contactDict[@"givenName"];
    
    NSMutableArray<CNLabeledValue<CNPhoneNumber*>*> *phoneNumbers = @[].mutableCopy;
    NSArray *phones = contactDict[@"phones"];
    for (NSString *phone in phones) {
        CNPhoneNumber *phoneNumber = [CNPhoneNumber phoneNumberWithStringValue:phone];
        CNLabeledValue *labeledValue = [[CNLabeledValue alloc] initWithLabel:CNLabelPhoneNumberMobile value:phoneNumber];
        [phoneNumbers addObject:labeledValue];
    }
    
    contact.phoneNumbers = phoneNumbers.copy;
    
    CNSaveRequest *saveRequest = [[CNSaveRequest alloc] init];
    [saveRequest addContact:contact toContainerWithIdentifier:nil];
    
    CNContactStore *store = [[CNContactStore alloc] init];
    [store executeSaveRequest:saveRequest error:nil];
    
    callback();
    
}

- (void)reciveCalendarData:(NSData *)data callback:(void(^)(void))callback{
    
    NSDictionary *infoDict = data.mj_JSONObject;

    EKEvent *event = [EKEvent eventWithEventStore:self.eventStore];
    event.title = infoDict[@"title"];
    event.allDay = [infoDict[@"allDay"] boolValue];
    
    NSString *startDate = infoDict[@"startDate"];
    NSString *endDate = infoDict[@"endDate"];
    
    event.startDate = [NSDate jk_dateWithString:startDate format:@"yyyy-MM-dd HH:mm:ss"];
    event.endDate = [NSDate jk_dateWithString:endDate format:@"yyyy-MM-dd HH:mm:ss"];
    
    [event setCalendar:[self.eventStore defaultCalendarForNewEvents]];
    [self.eventStore saveEvent:event span:EKSpanThisEvent commit:YES error:nil];
    
    callback();
}

- (void)reciveMusicData:(NSData *)data callback:(void(^)(void))callback{
    
    NSString *cachesPath = [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES) lastObject];
    NSString *zipPath = [cachesPath stringByAppendingPathComponent:@"SSZipArchive.zip"];
    
    [data writeToFile:zipPath atomically:YES];
    
    NSString *destinationPath =[cachesPath stringByAppendingPathComponent:@"SSZipArchive"];
    
    if ([[NSFileManager defaultManager] fileExistsAtPath:destinationPath]) {
        [[NSFileManager defaultManager] removeItemAtPath:destinationPath error:nil];
    }
    
    [SSZipArchive unzipFileAtPath:zipPath toDestination:destinationPath];
    
    
    NSDictionary *infoDict = [NSDictionary dictionaryWithContentsOfFile:[destinationPath stringByAppendingPathComponent:@"config.plist"]];
    NSString *musicName = infoDict[@"musicName"];
    NSString *musicTitle = infoDict[@"title"];
    
    NSString *musicDirPath =  [[NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) firstObject] stringByAppendingPathComponent:@"music"];
    
    if (![[NSFileManager defaultManager] fileExistsAtPath:musicDirPath]) {
        [[NSFileManager defaultManager] createDirectoryAtPath:musicDirPath withIntermediateDirectories:YES attributes:nil error:nil];
    }
    
    NSString *musicPath = [musicDirPath stringByAppendingPathComponent:musicName];
    
    if ([[NSFileManager defaultManager] fileExistsAtPath:musicPath]) {
        
        for (NSInteger i = 1; i < 10000; i++) {
            
            NSString *tempName = [NSString stringWithFormat:@"%@ (%ld).m4a", musicTitle, i];
            musicPath = [musicDirPath stringByAppendingPathComponent:tempName];
            
            if (![[NSFileManager defaultManager] fileExistsAtPath:musicPath]) {
                break;
            }
        }
    }
    
    [[NSFileManager defaultManager] moveItemAtPath:[destinationPath stringByAppendingPathComponent:musicName] toPath:musicPath error:nil];

    callback();
}


- (void)session:(nonnull MCSession *)session didReceiveStream:(nonnull NSInputStream *)stream withName:(nonnull NSString *)streamName fromPeer:(nonnull MCPeerID *)peerID {
    
}

- (void)session:(nonnull MCSession *)session didStartReceivingResourceWithName:(nonnull NSString *)resourceName fromPeer:(nonnull MCPeerID *)peerID withProgress:(nonnull NSProgress *)progress {
    
}


- (void)session:(nonnull MCSession *)session didFinishReceivingResourceWithName:(nonnull NSString *)resourceName fromPeer:(nonnull MCPeerID *)peerID atURL:(nullable NSURL *)localURL withError:(nullable NSError *)error {
    
}

- (NSString *)getVideoDirectory {
    
    NSString *cachesPath = [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES) lastObject];
    
    NSString *directory = [cachesPath stringByAppendingPathComponent:@"Videos"];
    
    BOOL fileExists = [[NSFileManager defaultManager] fileExistsAtPath:directory isDirectory:nil];
    if (!fileExists) {
        NSError *error = nil;
        [[NSFileManager defaultManager] createDirectoryAtPath:directory withIntermediateDirectories:YES attributes:nil error:&error];
        if (error) {
            NSLog(@"------>文件创建失败: %@", error);
        } else {
            
        }
    }
    
    return directory;
}

- (void)showAlertViewWithFileType:(BSFileType)fileType {
    
    NSString *title;
    NSString *message;
    
    switch (fileType) {
        case BSFileTypePhoto:
        {
            //相册权限
            title = NSLocalizedString(@"请授权相册权限", nil);
            message = NSLocalizedString(@"请在iPhone的\"设置-隐私-相册\"选项中,允许App访问你的相册", nil);
        }
            break;
        case BSFileTypeVideo:
        {
            //视频权限
            //相册权限
            title = NSLocalizedString(@"请授权相册权限", nil);
            message = NSLocalizedString(@"请在iPhone的\"设置-隐私-相册\"选项中,允许App访问你的相册", nil);
        }
            break;
        case BSFileTypeContact:
        {
            //通讯录权限
            title = NSLocalizedString(@"请授权通讯录权限", nil);
            message = NSLocalizedString(@"请在iPhone的\"设置-隐私-通讯录\"选项中,允许App访问你的通讯录", nil);
        }
            break;
        case BSFileTypeMusic:
        {
            //多媒体权限
            title = NSLocalizedString(@"请授权媒体与Apple Music权限", nil);
            message = NSLocalizedString(@"请在iPhone的\"设置-隐私-媒体与Apple Music\"选项中,允许App访问你的媒体与Apple Music", nil);
        }
            break;
        case BSFileTypeCalendar:
        {
            //日历权限
            title = NSLocalizedString(@"请授权日历权限", nil);
            message = NSLocalizedString(@"请在iPhone的\"设置-隐私-日历\"选项中,允许App访问你的日历", nil);
        }
            break;
            
        default:
            break;
    }
    
    if (!title) {
        return;
    }
    
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:title message:message preferredStyle: UIAlertControllerStyleAlert];
    
    UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"gotIt", @"知道了") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
    }];
    [alertController addAction:cancleAction];
    
    UIAlertAction *settingAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"toSettings", @"去设置") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        
        NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
        if ([[UIApplication sharedApplication] canOpenURL:url]) {
            [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
        }
    
    }];
    
    [alertController addAction:settingAction];
    
    UITabBarController *tabBarController = (UITabBarController *)[AppDelegate getAppDelegate].window.rootViewController;
    UINavigationController *navController = (UINavigationController *)tabBarController.viewControllers[tabBarController.selectedIndex];
    
    [navController presentViewController:alertController animated:YES completion:nil];
    
}



@end
