//
//  BSAWSS3Manager.m
//  PhoneClone
//
//  Created by macbookair on 22/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSAWSS3Manager.h"
//存储桶名称,可在AWS开发者平台找到,每个工程有唯一存储桶
#define S3BucketName @"phoneclone-userfiles-mobilehub-304070156"
//[上传文件]到云端的路径以及文件名
#define S3UploadPath @"public/CloudStorage/"
@implementation BSAWSS3Manager
/// 查询AWS云存储的所有文件信息
/// @param path 云存储的路径(用户账号本地保存的path属性)
/// @param completionHandler 查询完成后的回调代码块
+ (void)getListObjectsWithPath:(NSString*)path completionHandler:(void (^)(AWSS3ListObjectsOutput *response, NSError *error))completionHandler{
    AWSS3 *s3 = [AWSS3 defaultS3];
    AWSS3ListObjectsRequest *listObjectReq = [AWSS3ListObjectsRequest new];
    //bucket:存储桶名称
    listObjectReq.bucket = S3BucketName;
    //prefix:目录路径
    listObjectReq.prefix = [NSString stringWithFormat:@"public/CloudStorage/%@", path];
    [s3 listObjects:listObjectReq completionHandler:completionHandler];
}

/// 本地上传图像到云存储
/// @param image 图像对象
/// @param name 图像名称
/// @param path 保存的路径
/// @param completionHandler 完成上传后的回调代码块
/// @param progressBlock 上传过程中的回调代码块
/// @param block 开始上传的回调代码块
+ (void)uploadWithImage:(UIImage*)image
                   name:(NSString*)name
                   path:(NSString*)path
      completionHandler:(AWSS3TransferUtilityUploadCompletionHandlerBlock)completionHandler
          progressBlock:(AWSS3TransferUtilityProgressBlock)progressBlock
      continueWithBlock:(AWSContinuationBlock)block{
    NSData *imageData = UIImageJPEGRepresentation(image,1);
    
    AWSS3TransferUtilityUploadExpression *expression = [AWSS3TransferUtilityUploadExpression new];
    expression.progressBlock = progressBlock;
    
    //上传操作的核心block代码块
    AWSS3TransferUtility *transferUtility = [AWSS3TransferUtility defaultS3TransferUtility];
    [[transferUtility uploadData:imageData
                          bucket:S3BucketName
                             key:[NSString stringWithFormat:@"%@%@/%@", S3UploadPath, path, name]
                     contentType:@"text/plain"
                      expression:expression
               completionHandler:completionHandler] continueWithBlock:block];
}

/// 从云存储下载文件到本地
/// @param key 云存储的路径以及文件名
/// @param fileName 下载到本地的文件名
/// @param completionHandler 下载完成后的回调
+ (void)downloadWithKey:(NSString*)key
         saveToFileName:(NSString*)fileName
      completionHandler:(void (^)(AWSS3GetObjectOutput *response, NSError *error))completionHandler{
    AWSS3 *s3 = [AWSS3 defaultS3];
    AWSS3GetObjectRequest *getObjectRequest = [AWSS3GetObjectRequest new];
    //bucket:存储桶名称
    getObjectRequest.bucket = S3BucketName;
    //key:预下载文件在云存储的路径以及文件名
    getObjectRequest.key = key;
    //filePath:文件下载到本地的路径以及文件名
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    NSString *documentsPath = [paths objectAtIndex:0]; //Get the docs directory
    NSString *filePath = [documentsPath stringByAppendingPathComponent:fileName];
    getObjectRequest.downloadingFileURL = [NSURL fileURLWithPath:filePath];
    BKdislog(filePath);
    [s3 getObject:getObjectRequest completionHandler:completionHandler];
}

+ (void)deletePathWithPaths:(AWSS3ListObjectsOutput*)listObjectsOutput
{
           
           for (AWSS3Object *s3Object in listObjectsOutput.contents) {
               // Delete the object
               AWSS3DeleteObjectRequest *deleteObjectRequest = [AWSS3DeleteObjectRequest new];
               deleteObjectRequest.bucket = S3BucketName;
               deleteObjectRequest.key = s3Object.key;
               AWSS3 *s3 = [AWSS3 defaultS3];
               [[s3 deleteObject:deleteObjectRequest] continueWithBlock:^id(AWSTask *task) {
                   if (task.error){
                       NSLog(@"deleteObject failed, %@", task.error);
                   }
                   return nil;
               }];
           }
}
@end
