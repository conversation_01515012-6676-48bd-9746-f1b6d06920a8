//
//  BSSendFileManager.h
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "BSPeerModel.h"

@interface BSSendFileManager : NSObject

@property (nonatomic, copy) void(^foundPeer)(BSPeerModel *model);
@property (nonatomic, copy) void(^lostPeer)(BSPeerModel *model);

@property (nonatomic, strong) NSMutableArray<BSPeerModel *> *peerArray;

+ (instancetype)manager;
- (void)startScan;
- (void)sendFileWithPeerModel:(BSPeerModel *)peerModel datas:(NSArray *)dataArray sendFileType:(BSFileType)fileType;

- (void)sendFileWithPeerModel:(BSPeerModel *)peerModel datas:(NSArray *)resGroupArray;

@end

