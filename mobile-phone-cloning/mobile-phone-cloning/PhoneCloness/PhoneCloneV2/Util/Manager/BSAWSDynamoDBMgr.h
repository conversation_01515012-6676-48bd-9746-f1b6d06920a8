//
//  BSAWSDynamoDBMgr.h
//  PhoneClone
//
//  Created by macbookair on 18/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <AWSDynamoDB/AWSDynamoDB.h>

NS_ASSUME_NONNULL_BEGIN

/// DynamoDB数据库的用户列表的email的key
#define Email_Of_DynamoDB_Key @"email"

/// DynamoDB数据库的用户列表的createTime的key
#define CreateTime_Of_DynamoDB_Key @"createTime"

/// DynamoDB数据库的用户列表的password的key
//NSString const *Password_Of_DynamoDB_Key = @"password";
#define Password_Of_DynamoDB_Key @"password"

/// DynamoDB数据库的用户列表的Path的key
#define Path_Of_DynamoDB_Key @"path"

/// DynamoDB数据库的用户列表的q1_ans的key
#define Q1_Ans_Of_DynamoDB_Key @"q1_ans"

/// DynamoDB数据库的用户列表的q2_ans的key
#define Q2_Ans_Of_DynamoDB_Key @"q2_ans"

/// DynamoDB数据库的用户列表的q3_ans的key
#define Q3_Ans_Of_DynamoDB_Key @"q3_ans"

/// AWS的DynamoDB的管理类
@interface BSAWSDynamoDBMgr : NSObject
/// 查询DynamoDB数据库中的记录-根据[email]作为条件
/// @param email 邮箱
/// @param completionHandler 查询后的回调
+ (void)queryWithEmail:(NSString*)email completionHandler:(void (^)(AWSDynamoDBQueryOutput *response, NSError *error))completionHandler;

/// 插入注册账号
/// @param accountModel 账号model对象
/// @param completionHandler 插入完成后的回调
+ (void)putItemWithAccountModel:(BSAccountModel*)accountModel completionHandler:(void (^)(AWSDynamoDBPutItemOutput *response, NSError *error))completionHandler;

/// 修改密码
/// @param emailStr 用户账号(邮箱) - 主键
/// @param createTimeStr 注册时间 - 排序键
/// @param newPwdStr 新密码
/// @param completionHandler 修改完成后的回调
+ (void)updateItemWithEmail:(NSString*)emailStr
                 createTime:(NSString*)createTimeStr
                newPassword:(NSString*)newPwdStr
          completionHandler:(void (^)(AWSDynamoDBUpdateItemOutput *response, NSError *error))completionHandler;
/// 注销功能
/// @param emailStr 用户账号(邮箱) - 主键
/// @param createTimeStr 注册时间 - 排序键
+ (void)deleteUserWithEmail:(NSString*)emailStr
                 createTime:(NSString*)createTimeStr
          completionHandler:(void (^)(AWSDynamoDBDeleteItemOutput *response, NSError *error))completionHandler;

@end

NS_ASSUME_NONNULL_END
