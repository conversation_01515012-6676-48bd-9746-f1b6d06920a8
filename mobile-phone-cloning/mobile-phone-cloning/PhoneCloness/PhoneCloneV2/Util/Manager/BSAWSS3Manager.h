//
//  BSAWSS3Manager.h
//  PhoneClone
//
//  Created by macbookair on 22/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <AWSS3/AWSS3.h>
NS_ASSUME_NONNULL_BEGIN

@interface BSAWSS3Manager : NSObject
/// 查询AWS云存储的所有文件信息
/// @param path 云存储的路径(用户账号本地保存的path属性)
/// @param completionHandler 查询完成后的回调代码块
+ (void)getListObjectsWithPath:(NSString*)path completionHandler:(void (^)(AWSS3ListObjectsOutput *response, NSError *error))completionHandler;

/// 本地上传图像到云存储
/// @param image 图像对象
/// @param name 图像名称
/// @param path 保存的路径
/// @param completionHandler 完成上传后的回调代码块
/// @param progressBlock 上传过程中的回调代码块
/// @param block 开始上传的回调代码块
+ (void)uploadWithImage:(UIImage*)image
                   name:(NSString*)name
                   path:(NSString*)path
      completionHandler:(AWSS3TransferUtilityUploadCompletionHandlerBlock)completionHandler
          progressBlock:(AWSS3TransferUtilityProgressBlock)progressBlock
      continueWithBlock:(AWSContinuationBlock)block;

/// 从云存储下载文件到本地
/// @param key 云存储的路径以及文件名
/// @param fileName 下载到本地的文件名
/// @param completionHandler 下载完成后的回调
+ (void)downloadWithKey:(NSString*)key
         saveToFileName:(NSString*)fileName
      completionHandler:(void (^)(AWSS3GetObjectOutput *response, NSError *error))completionHandler;
+ (void)deletePathWithPaths:(AWSS3ListObjectsOutput*)listObjectsOutput;
@end
NS_ASSUME_NONNULL_END
