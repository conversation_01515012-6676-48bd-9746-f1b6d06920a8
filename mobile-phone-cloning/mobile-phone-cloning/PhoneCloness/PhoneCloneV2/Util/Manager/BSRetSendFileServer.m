//
//  BSRetSendFileServer.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSRetSendFileServer.h"

#import "BSTransferFileProgressVC.h"

#import "BSPeerModel.h"
#import "BSMusic.h"
#import "BSContact.h"
#import "BSCalender.h"

#import "BSTransferRes.h"
#import "BSResGroupModel.h"

#import <TZImageManager.h>
#import <MJExtension.h>
#import <GCDAsyncSocket.h>

#define kDivisionStr     @"05162012"

@interface BSRetSendFileServer () <GCDAsyncSocketDelegate>

@property (nonatomic, strong) BSTransferFileProgressVC *progressVC;

@property (nonatomic, strong) GCDAsyncSocket *clientSocket;

@property (nonatomic, strong) BSTransferRes *transferRes;
@property (nonatomic, strong) NSArray *resGroupArray;

@property (nonatomic, copy) void(^refreshProgress)(NSInteger currentIndex, NSInteger totalCount);

@property (nonatomic, assign) NSInteger packetLength;
@property (nonatomic, assign) BOOL transfSuccessed;

@end

@implementation BSRetSendFileServer

- (instancetype)init {
    if (self = [super init]) {
        _clientSocket = [[GCDAsyncSocket alloc] initWithDelegate:self delegateQueue:dispatch_get_main_queue()];
        _packetLength = -1;
        
    }
    return self;
}

- (void)dealloc {
    self.clientSocket.delegate = nil;
    self.clientSocket = nil;
    
}

- (void)connectToHost:(NSString *)host datas:(NSArray *)resGroupArray {
    
    self.transfSuccessed = NO;
    
    NSError *error;
    BOOL result = [self.clientSocket connectToHost:host onPort:kHostPort error:&error];
    
    if (result) {
        self.resGroupArray = resGroupArray;
    }
    
    NSLog(@"conn result: %d error: %@", result, error);
}




- (void)sendData:(NSData *)bodyData option:(NSInteger)option userInfo:(NSDictionary *)userInfo{

    NSMutableDictionary *headDict;
    if (userInfo) {
        headDict = [NSMutableDictionary dictionaryWithDictionary:userInfo];
    }else {
        headDict = [NSMutableDictionary dictionary];
    }
    
    [headDict setObject:@(option) forKey:@"option"];
    [headDict setObject:[[UIDevice currentDevice] name] forKey:@"deviceName"];
    [headDict setObject:@"ios" forKey:@"deviceOS"];
    
    if (option == 2) {
        [headDict setObject:@(self.transferRes.fileType) forKey:@"type"];
        [headDict setObject:@(self.transferRes.total) forKey:@"fileCount"];
        [headDict setObject:@(self.transferRes.index) forKey:@"curIndex"];
        
        if (self.transferRes.resArray.count > 0){
            [self.transferRes.resArray removeLastObject];
        }
        
        self.transferRes.index++;
        
    }
    
    NSData *headData = headDict.mj_JSONData;
    
    NSMutableData *transData = [NSMutableData data];
    
    [transData appendData:headData];
    [transData appendData:[kDivisionStr dataUsingEncoding:NSUTF8StringEncoding]];
    
    if (bodyData) {
        
        if (self.transferRes.fileType == 0 || self.transferRes.fileType == 1 || self.transferRes.fileType == 3) {
            bodyData = [[bodyData jk_base64EncodedString] dataUsingEncoding:NSUTF8StringEncoding];
        }
        
        [transData appendData:bodyData];
    }
   
    NSUInteger value = transData.length + 1;
    Byte byte[4] = {};
    byte[0] = (Byte) ((value>>24) & 0xFF);
    byte[1] = (Byte) ((value>>16) & 0xFF);
    byte[2] = (Byte) ((value>>8) & 0xFF);
    byte[3] = (Byte) (value & 0xFF);

    NSData *lengthData = [NSData dataWithBytes:byte length:sizeof(byte)];
    
    NSMutableData *mData = [NSMutableData data];
    [mData appendData:lengthData];
    [mData appendData:[@"\n" dataUsingEncoding:NSUTF8StringEncoding]];
    [mData appendData:transData];
    
    [self.clientSocket writeData:mData withTimeout:-1 tag:0];
    
    self.packetLength = -1;
    [self.clientSocket readDataToData:[@"\n" dataUsingEncoding:NSUTF8StringEncoding] withTimeout:-1 tag:0];

}

- (int)byteWithInt:(nullable const void *)bytes {
    Byte *byte = (Byte *)bytes;
    int returnValue = 0;
    int byteCount = sizeof(byte)/2;
    for (int i=0; i<byteCount; i++) {
        if (i == 0) {
            returnValue = byte[byteCount -1];
        }else{
            returnValue += pow(256, i) * byte[byteCount-i-1];
        }
    }
    return returnValue;
}

#pragma mark - GCDAsyncSocketDelegate
- (void)socket:(GCDAsyncSocket *)sock didConnectToHost:(NSString *)host port:(uint16_t)port {
    NSLog(@"-->>>host: %@, port %d", host, port);

    BSResType resType = 0;
       
    for (NSInteger i = 0; i < self.resGroupArray.count; i++) {
        BSResGroupModel *resGroup = self.resGroupArray[i];
        if (resGroup.isSelected) {
            resType = resGroup.resType;
            break;
        }
    }
       
    [self sendData:nil option:0 userInfo:@{@"type":@(resType)}];
    
    self.transferRes = [[BSTransferRes alloc] init];
    
}

- (void)socket:(GCDAsyncSocket *)sock didReadData:(NSData *)data withTag:(long)tag {

    if (self.packetLength < 0) {
        self.packetLength = [self byteWithInt:data.bytes];
           
        if (self.packetLength < 0) {
            NSLog(@"error: 当前数据包的头为空");
            return;
        }
        
        [sock readDataToLength:self.packetLength - 1 withTimeout:-1 tag:0];
        return;
    }

    NSString *result = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
       
    NSArray *components = [result componentsSeparatedByString:kDivisionStr];
       
    NSError *error;
    NSDictionary *infoDict = [NSJSONSerialization JSONObjectWithData:[components[0] dataUsingEncoding:NSUTF8StringEncoding] options:kNilOptions error:&error];
       
    NSInteger option = [infoDict[@"option"] integerValue];
    
       
    [self sendResData];
    
}

- (void)socketDidDisconnect:(GCDAsyncSocket *)sock withError:(NSError *)err {
    //服务端socket断开连接
    NSLog(@"-->>> 服务端socket断开连接 err: %@", err);
    
//    self.clientSocket.delegate = nil;
//    self.clientSocket = nil;
//    
//    if (self.progressVC && !self.transfSuccessed && err) {
//        UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"提示" message:@"连接中断，停止传输" preferredStyle:UIAlertControllerStyleAlert];
//        
//        kWeakSelf
//        UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:@"知道了" style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
//            
//            [weakSelf.progressVC.view removeFromSuperview];
//            weakSelf.progressVC = nil;
//            
//        }];
//        [alertController addAction:cancleAction];
//        
//        UITabBarController *tabController = (UITabBarController *)[AppDelegate getAppDelegate].window.rootViewController;
//        UINavigationController *navContoller = (UINavigationController *)tabController.viewControllers[tabController.selectedIndex];
//        
//        [navContoller presentViewController:alertController animated:YES completion:nil];
//    }
//    
//    [self.progressVC.view removeFromSuperview];
//    self.progressVC = nil;
    
}

#pragma mark - Send Data
- (void)sendResData {
    
    if (self.transferRes.resArray.count == 0) {
        
        [self pkgResArray];
        
        self.transferRes.total = self.transferRes.resArray.count;
        self.transferRes.index = 0;
        
        if (self.transferRes.resArray.count == 0) {
            //内容传输完成
            self.transfSuccessed = YES;
            
            [self sendData:nil option:4 userInfo:nil];
                   
           kWeakSelf
           dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
               
               [[NSNotificationCenter defaultCenter] postNotificationName:@"reciveDataSuccessed" object:nil userInfo:nil];
               
               [weakSelf.progressVC.view removeFromSuperview];
               weakSelf.progressVC = nil;
    
               [weakSelf.clientSocket disconnect];
               weakSelf.clientSocket = nil;
               
               
           });
            
            return;
        }
        
        
        if (!self.progressVC) {
             self.progressVC = [[BSTransferFileProgressVC alloc] init];
             self.progressVC.isSendFile = YES;
             self.progressVC.view.frame = [UIScreen mainScreen].bounds;
             [[AppDelegate getAppDelegate].window addSubview:self.progressVC.view];
             self.refreshProgress = self.progressVC.refreshProgress;
            
         }
        
        
        self.progressVC.fileType = self.transferRes.fileType;
        kWeakSelf
         if (self.refreshProgress) {
             dispatch_async(dispatch_get_main_queue(), ^{
                 weakSelf.refreshProgress(weakSelf.transferRes.index, weakSelf.transferRes.total);
             });
        }
    
    }
    
    switch (self.transferRes.fileType) {
        case BSFileTypePhoto:
        {
            //照片
            [self sendPhotoData];
        }
            break;
        case BSFileTypeVideo:
        {
            //视频
             [self sendVideoData];
        }
            break;
        case BSFileTypeContact:
        {
            //联系人
            [self sendContactData];
        }
            break;
        case BSFileTypeMusic:
        {
            //音乐
            [self sendMusicData];
        }
            break;
        case BSFileTypeCalendar:
        {
            //日历
            [self sendCalendartData];
        }
            break;
            
        default:
            break;
    }
    
    kWeakSelf
     if (self.refreshProgress) {
         dispatch_async(dispatch_get_main_queue(), ^{
             weakSelf.refreshProgress(weakSelf.transferRes.index, weakSelf.transferRes.total);
         });
    }
    
}

- (void)pkgResArray {
    
    for (NSInteger i = self.transferRes.cursor; i < self.resGroupArray.count; i++) {
        BSResGroupModel *resGroup = self.resGroupArray[i];
        
        if (resGroup.isSelected) {
            
            self.transferRes.cursor = i;
            
            switch (resGroup.resType) {
                case BSResTypePhoto:
                {
                    //照片
                    self.transferRes.fileType = BSFileTypePhoto;
                    
                    for (TZAssetModel *model in resGroup.resArray) {
                        if (model.isSelected) {
                            [self.transferRes.resArray addObject:model];
                        }
                    }
                    
                }
                    break;
                case BSResTypeVideo:
                {
                    //视频
                    self.transferRes.fileType = BSFileTypeVideo;
                    
                    for (TZAssetModel *model in resGroup.resArray) {
                        if (model.isSelected) {
                            [self.transferRes.resArray addObject:model];
                        }
                    }
                    
                }
                    break;
                case BSResTypeContact:
                {
                    //联系人
                    self.transferRes.fileType = BSFileTypeContact;
                    
                    for (BSContact *model in resGroup.resArray) {
                        if (model.isSelected) {
                            
                            NSMutableArray *phones = @[].mutableCopy;
                            
                            for (CNLabeledValue *labelValue in model.contact.phoneNumbers) {
                                CNPhoneNumber *phoneNumber = labelValue.value;
                                [phones addObject:phoneNumber.stringValue];
                            }
                            NSString *nameStr = [NSString stringWithFormat:@"%@%@",model.contact.familyName,model.contact.givenName];
                            NSDictionary *infoDict = @{
                                                       @"ContactName":nameStr,
                                                       @"ContactPhotoNumberArray":phones
                                                       };
                            [self.transferRes.resArray addObject:infoDict];
                        }
                    }
                }
                    break;
                case BSResTypeMusic:
                {
                    //音乐
                    self.transferRes.fileType = BSFileTypeMusic;
                    
                    for (BSMusic *model in resGroup.resArray) {
                           if (model.isSelected) {
                               [self.transferRes.resArray addObject:model];
                           }
                       }
                }
                    break;
                case BSResTypeCalendar:
                {
                    //日历
                    self.transferRes.fileType = BSFileTypeCalendar;
                    
                    for (BSCalender *model in resGroup.resArray) {
                        if (model.isSelected) {
                            [self.transferRes.resArray addObject:model.infoDict];
                        }
                    }
                }
                    break;
                    
                default:
                    break;
            }
            
            break;
            
        }
        
    }
    
    self.transferRes.cursor += 1;
}

- (void)sendPhotoData {
    
    TZAssetModel *model = [self.transferRes.resArray lastObject];
    
    kWeakSelf
    [[TZImageManager manager] getOriginalPhotoDataWithAsset:model.asset completion:^(NSData *data, NSDictionary *info, BOOL isDegraded) {
      
        dispatch_sync(dispatch_get_global_queue(0, 0), ^{
           
            NSURL *fileURL = info[@"PHImageFileURLKey"];
            NSString *fileURLString = fileURL.absoluteString;
            NSString *suffixName = [[fileURLString componentsSeparatedByString:@"."] lastObject];
            
            if (suffixName.length == 0) {
                suffixName = @"jpg";
            }
            
            NSString *fileName = [NSString stringWithFormat:@"%@.%@", [NSString stringWithFormat:@"%f", [NSDate date].timeIntervalSince1970].jk_md5String, suffixName];
            
            [weakSelf sendData:data option:2 userInfo:@{@"fileName": fileName}];
            
        });
    }];
  
}

- (void)sendVideoData {
    
    TZAssetModel *model = [self.transferRes.resArray lastObject];
    
    kWeakSelf
    [[TZImageManager manager] getVideoOutputPathWithAsset:model.asset success:^(NSString *outputPath) {
        
        dispatch_sync(dispatch_get_global_queue(0, 0), ^{
            
            NSData *data = [[NSData alloc] initWithContentsOfURL:[NSURL fileURLWithPath:outputPath]];
            
            NSString *suffixName = [[outputPath componentsSeparatedByString:@"."] lastObject];
            
            NSString *fileName = [NSString stringWithFormat:@"%@.%@", [NSString stringWithFormat:@"%f", [NSDate date].timeIntervalSince1970].jk_md5String, suffixName];
            
            [weakSelf sendData:data option:2 userInfo:@{@"fileName": fileName}];
            
        });
        
    } failure:^(NSString *errorMessage, NSError *error) {
        
    }];
    
}

- (void)sendContactData {
    
    kWeakSelf
    dispatch_sync(dispatch_get_global_queue(0, 0), ^{
        
        NSDictionary *infoDict = [weakSelf.transferRes.resArray lastObject];
        [weakSelf sendData:infoDict.mj_JSONData option:2 userInfo:nil];
        
    });
    
}

- (void)sendCalendartData {
    
    kWeakSelf
    dispatch_sync(dispatch_get_global_queue(0, 0), ^{
        
        NSDictionary *infoDict = [weakSelf.transferRes.resArray lastObject];
        NSMutableDictionary *mInfoDict = [NSMutableDictionary dictionaryWithDictionary:infoDict];
        
        NSString *startDateStr = mInfoDict[@"startDate"];
        NSString *endDateStr = mInfoDict[@"endDate"];
        
        NSDate *startDate = [NSDate jk_dateWithString:startDateStr format:@"yyyy-MM-dd HH:mm:ss"];
        NSDate *endDate = [NSDate jk_dateWithString:endDateStr format:@"yyyy-MM-dd HH:mm:ss"];
        
        [mInfoDict setObject:@(startDate.timeIntervalSince1970 * 1000) forKey:@"startDate"];
        [mInfoDict setObject:@(endDate.timeIntervalSince1970 * 1000) forKey:@"endDate"];
        
        [weakSelf sendData:mInfoDict.mj_JSONData option:2 userInfo:nil];
        
    });
    
}

- (void)sendMusicData {
    
    BSMusic *music = [self.transferRes.resArray lastObject];
    
    NSString *musicTitle = music.infoDict[@"title"];
    NSURL *musicUrl = music.infoDict[@"fileURL"];
    
    NSString *musicFileName = [NSString stringWithFormat:@"%@.m4a", musicTitle];
    
    NSString *cachesPath = [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES) lastObject];
    
    //Music
    AVURLAsset *songAsset = [AVURLAsset URLAssetWithURL:musicUrl options:nil];
    [AVAssetExportSession exportPresetsCompatibleWithAsset:songAsset];
    
    AVAssetExportSession *session = [[AVAssetExportSession alloc] initWithAsset:songAsset presetName:AVAssetExportPresetAppleM4A];
    session.outputFileType=@"com.apple.m4a-audio";
    
    NSString *musicPath = [cachesPath stringByAppendingPathComponent:musicFileName];
    if ([[NSFileManager defaultManager] fileExistsAtPath:musicPath]) {
        [[NSFileManager defaultManager] removeItemAtPath:musicPath error:nil];
    }
    
    session.outputURL = [NSURL fileURLWithPath:musicPath];
    
    kWeakSelf
    [session exportAsynchronouslyWithCompletionHandler:^{
        
        dispatch_sync(dispatch_get_global_queue(0, 0), ^{
            
            [weakSelf sendData:[NSData dataWithContentsOfURL:[NSURL fileURLWithPath:musicPath]] option:2 userInfo:@{@"fileName": musicFileName}];
            
        });
        
    }];
    
}



@end
