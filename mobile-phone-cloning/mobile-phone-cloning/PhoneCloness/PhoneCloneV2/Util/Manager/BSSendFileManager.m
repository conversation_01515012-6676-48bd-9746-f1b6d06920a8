//
//  BSSendFileManager.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSSendFileManager.h"
#import "BSTransferFileProgressVC.h"

#import "BSPeerModel.h"
#import "BSMusic.h"
#import "BSContact.h"
#import "BSCalender.h"

#import "BSTransferRes.h"
#import "BSResGroupModel.h"

#import <TZImageManager.h>
#import <MJExtension.h>
#import <SSZipArchive.h>
#import <MultipeerConnectivity/MultipeerConnectivity.h>

@interface BSSendFileManager () <MCSessionDelegate, MCNearbyServiceAdvertiserDelegate, MCNearbyServiceBrowserDelegate,BSConnectManagerDelegate>

@property (strong,nonatomic) MCSession *session;
@property (strong,nonatomic) MCNearbyServiceAdvertiser *nearbyServiceAdveriser;
@property (strong, nonatomic) MCNearbyServiceBrowser *nearbyServiceBrowser;
@property NSString* host;
@property (nonatomic, strong) BSPeerModel *peerModel;
@property (nonatomic, strong) NSArray *resGroupArray;
@property (nonatomic, strong) BSTransferRes *transferRes;

@property (nonatomic, strong) BSTransferFileProgressVC *progressVC;

@property (nonatomic, copy) void(^refreshProgress)(NSInteger currentIndex, NSInteger totalCount);

@end

@implementation BSSendFileManager

static BSSendFileManager *instance;

+ (instancetype)manager {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[BSSendFileManager alloc] init];
        [instance initConfig];
    });
    return instance;
}

- (void)initConfig {

    //创建会话(两边的回话类型必须一致)
    MCPeerID *peerID = [[MCPeerID alloc] initWithDisplayName:[[UIDevice currentDevice] name]];
    self.session = [[MCSession alloc] initWithPeer:peerID securityIdentity:nil encryptionPreference:MCEncryptionNone];
    self.session.delegate = self;
    
    //广播通知(广播是通过serviceType来区分，所以监听广播的serviceType必须相同，不然监听不到)
    self.nearbyServiceAdveriser = [[MCNearbyServiceAdvertiser alloc] initWithPeer:peerID discoveryInfo:@{@"ids": [UIDevice currentDevice].identifierForVendor.UUIDString} serviceType:@"rsp-sender"];
    self.nearbyServiceAdveriser.delegate = self;
    [self.nearbyServiceAdveriser startAdvertisingPeer];
    
    //监听广播
    self.nearbyServiceBrowser = [[MCNearbyServiceBrowser alloc] initWithPeer:peerID serviceType:@"rsp-receiver"];
    self.nearbyServiceBrowser.delegate = self;
//    [self.nearbyServiceBrowser startBrowsingForPeers];
    
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleNotification) name:@"applicationDidEnterBackground" object:nil];
    
    
    
    
    
    
}


- (void)startScan
{
    NSString *IPAdress = [DHIPAdress deviceIPAdress];
    NSArray *serveriparr = [IPAdress componentsSeparatedByString:@"."];
    
    
    if (serveriparr.count == 4) {
        NSString *beginStr = [IPAdress stringByReplacingOccurrencesOfString:serveriparr.lastObject withString:@""];
        NSMutableArray *addressIds = [NSMutableArray array];
        for (int i = 0; i <= 255; i++) {
            [addressIds addObject:[beginStr stringByAppendingFormat:@"%d", i]];
        }
        for (NSString* url in addressIds) {
            [self getOtherNames:url compeletBlock:^(id  _Nonnull responseObject, NSError * _Nonnull error) {
                if(!error)
                {
                    NSLog(@"找到了%@",responseObject[@"data"][@"url"]);
                    if(![responseObject[@"data"][@"url"] isEqualToString:IPAdress]&&![responseObject[@"data"][@"url"]  isKindOfClass:[NSNull class]]&&responseObject[@"data"][@"url"])
                    {
                        BOOL hasExist = NO;
                        for (BSPeerModel *model in self.peerArray) {
                            if ([model.ids isEqualToString:responseObject[@"data"][@"url"]]) {
                                hasExist = YES;
                                break;
                            }
                        }
                        
                        if (!hasExist) {
                            BSPeerModel *model = [[BSPeerModel alloc] init];
                            model.peerID = responseObject[@"data"][@"deviceName"];
                            model.ids = responseObject[@"data"][@"url"];
                            [self.peerArray addObject:model];
                            
                            if (self.foundPeer) {
                                self.foundPeer(model);
                            }
                            
                        }
                        
                    }
                }
            }];
        }
    }
}


- (void)getOtherNames:(NSString*)url  compeletBlock:(CompletionHandler)block
{
    
    [[httpNework shareNetwork]sendGETRequestWithURL:[NSString stringWithFormat:@"http://%@:8888/getServerName",url] parameters:nil completed:^(id  _Nonnull responseObject, NSError * _Nonnull error) {
        block(responseObject,error);
    }];
}


- (void)handleNotification {
    
    [self.peerArray removeAllObjects];
    
    if (self.peerModel.peerID) {
        
        [self.session disconnect];
        [self.progressVC.view removeFromSuperview];
        self.progressVC = nil;
        
        UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"提示" message:@"连接中断，停止传输" preferredStyle:UIAlertControllerStyleAlert];
        
        kWeakSelf
        UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:@"知道了" style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
            weakSelf.peerModel = nil;
            
            UITabBarController *tabController = (UITabBarController *)[AppDelegate getAppDelegate].window.rootViewController;
            UINavigationController *navContoller = (UINavigationController *)tabController.viewControllers[tabController.selectedIndex];
            
            [navContoller popToRootViewControllerAnimated:YES];
            
        }];
        [alertController addAction:cancleAction];
        
        UITabBarController *tabController = (UITabBarController *)[AppDelegate getAppDelegate].window.rootViewController;
        UINavigationController *navContoller = (UINavigationController *)tabController.viewControllers[tabController.selectedIndex];
        
        [navContoller presentViewController:alertController animated:YES completion:nil];
        
    }
    
}

- (void)sendFileWithPeerModel:(BSPeerModel *)peerModel datas:(NSArray *)dataArray sendFileType:(BSFileType)fileType {
    
}

- (void)sendFileWithPeerModel:(BSPeerModel *)peerModel datas:(NSArray *)resGroupArray {
    
    if (![self.peerArray containsObject:peerModel]) {
         return;
     }
    
    self.peerModel = peerModel;
    self.resGroupArray = resGroupArray;
    
    NSDictionary *infoDidct = @{
        @"ids":peerModel.ids
                                };
    NSString* host = [peerModel.ids containsString:@"8888"]?peerModel.ids:[NSString stringWithFormat:@"http://%@:8888",peerModel.ids];
//    [self.nearbyServiceBrowser invitePeer:peerModel.peerID toSession:self.session withContext:[infoDidct mj_JSONData] timeout:30];
    self.host = host;
//    [BSConnectManager shareManager].delegate = self;
    [[BSConnectManager shareManager]joinHttpServer:host];
    
    [MobClick event:@"Connect" attributes:@{@"Click_Connect":@"点击头像"}];
    
}
- (void)connectSuccess
{
    BSNewSendServer* send = [BSNewSendServer new];
    [send connectToHost:self.host datas:self.resGroupArray];
}

#pragma mark - MCNearbyServiceBrowserDelegate
- (void)browser:(MCNearbyServiceBrowser *)browser foundPeer:(MCPeerID *)peerID
withDiscoveryInfo:(nullable NSDictionary<NSString *, NSString *> *)info {
    // 发现了附近的广播节点
    NSString *ids = info[@"ids"];
    
    if (!ids || [ids isEqualToString:[UIDevice currentDevice].identifierForVendor.UUIDString]) {
        return;
    }
    
    NSLog(@"发现了节点：%@", peerID.displayName);

    BOOL hasExist = NO;

    for (BSPeerModel *model in self.peerArray) {
        if ([model.ids isEqualToString:ids]) {
            hasExist = YES;
            break;
        }
    }
    
    if (!hasExist) {
        BSPeerModel *model = [[BSPeerModel alloc] init];
        model.peerID = peerID;
        model.ids = info[@"ids"];
        [self.peerArray addObject:model];
        
        if (self.foundPeer) {
            self.foundPeer(model);
        }
        
    }
    
    NSLog(@"------发现了附近的广播节点 >>>> %@", self.peerArray);
}

- (void)browser:(MCNearbyServiceBrowser *)browser lostPeer:(MCPeerID *)peerID {
    // 广播节点丢失
    NSLog(@"丢失了节点：%@", peerID.displayName);
    
    if (self.peerModel.peerID == peerID) {
        
        [self.session disconnect];
        [self.progressVC.view removeFromSuperview];
        self.progressVC = nil;
        
    }
    
    for (BSPeerModel *model in self.peerArray) {
        
        if (model.peerID == peerID) {
            
            [self.peerArray removeObject:model];
            
            if (self.lostPeer) {
                self.lostPeer(model);
            }
            
            break;
        }

    }
    
    NSLog(@"------广播节点丢失 >>>> %@", self.peerArray);
    
}

- (void)browser:(MCNearbyServiceBrowser *)browser didNotStartBrowsingForPeers:(NSError *)error {
    // 搜索失败回调
    NSLog(@"搜索出错：%@", error.localizedDescription);
}


#pragma mark - MCNearbyServiceAdvertiserDelegate
- (void)advertiser:(MCNearbyServiceAdvertiser *)advertiser
didReceiveInvitationFromPeer:(MCPeerID *)peerID
       withContext:(nullable NSData *)context
 invitationHandler:(void (^)(BOOL accept, MCSession * __nullable session))invitationHandler {
    // 收到节点邀请回调(发送者发出邀请，接收者接收邀请)
    
}

- (void)advertiser:(MCNearbyServiceAdvertiser *)advertiser didNotStartAdvertisingPeer:(NSError *)error {
    NSLog(@"%@节点广播失败", advertiser.myPeerID.displayName);
}

#pragma mark - MCSessionDelegate
- (void)session:(MCSession *)session peer:(MCPeerID *)peerID didChangeState:(MCSessionState)state{
    
    switch (state) {
        case MCSessionStateNotConnected:
        {
            //未连接
            NSLog(@"未连接");
            [self.session disconnect];
        }
            break;
        case MCSessionStateConnecting:
        {
            //连接中
            NSLog(@"连接中");
        }
            break;
        case MCSessionStateConnected:
        {
            //连接完成
            NSLog(@"连接完成");
    
            self.transferRes = [[BSTransferRes alloc] init];
            [self sendResData];
            
            kWeakSelf
            dispatch_async(dispatch_get_main_queue(), ^{
                
                if (!weakSelf.progressVC) {
                    weakSelf.progressVC = [[BSTransferFileProgressVC alloc] init];
                    weakSelf.progressVC.isSendFile = YES;
                    weakSelf.progressVC.view.frame = [UIScreen mainScreen].bounds;
                    
                }
                weakSelf.refreshProgress = self.progressVC.refreshProgress;
                [[AppDelegate getAppDelegate].window addSubview:weakSelf.progressVC.view];
            });
            
        }
            break;
    }
}

- (void)session:(MCSession *)session didReceiveData:(NSData *)data fromPeer:(MCPeerID *)peerID {

//    NSDictionary *infoDict = data.mj_JSONObject;
//
//    if (infoDict) {
//        BOOL receiveSuccess = [infoDict[@"receiveSuccess"] boolValue];
//    }
    
    [self sendResData];
}

- (void)session:(nonnull MCSession *)session didReceiveStream:(nonnull NSInputStream *)stream withName:(nonnull NSString *)streamName fromPeer:(nonnull MCPeerID *)peerID {
    
}

- (void)session:(nonnull MCSession *)session didStartReceivingResourceWithName:(nonnull NSString *)resourceName fromPeer:(nonnull MCPeerID *)peerID withProgress:(nonnull NSProgress *)progress {
    
}


- (void)session:(nonnull MCSession *)session didFinishReceivingResourceWithName:(nonnull NSString *)resourceName fromPeer:(nonnull MCPeerID *)peerID atURL:(nullable NSURL *)localURL withError:(nullable NSError *)error {
    
}

#pragma mark - Send Data
- (void)sendResData {
    
    if (self.transferRes.resArray.count == 0) {
        
        [self pkgResArray];
        
        self.transferRes.total = self.transferRes.resArray.count;
        self.transferRes.index = 0;
        
        if (self.transferRes.resArray.count == 0) {
            //内容传输完成
            NSDictionary *infoDidct = @{
                @"sendSuccess":@(1)
            };
            
            [self.session sendData:[infoDidct mj_JSONData] toPeers:@[self.peerModel.peerID] withMode:MCSessionSendDataReliable error:nil];
                   
           kWeakSelf
           dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
               
               [weakSelf.progressVC.view removeFromSuperview];
               weakSelf.progressVC = nil;
               
               weakSelf.peerModel = nil;
               [weakSelf.session disconnect];
               
               [[NSNotificationCenter defaultCenter] postNotificationName:@"sendDataSuccessed" object:nil userInfo:nil];
           });
            
           
        }else {
            //传输新类型内容
            NSDictionary *infoDidct = @{
                @"fileCount": @(self.transferRes.total),
                @"fileType": @(self.transferRes.fileType),
                @"sendSuccess":@(0)
            };
            
            [self.session sendData:[infoDidct mj_JSONData] toPeers:@[self.peerModel.peerID] withMode:MCSessionSendDataReliable error:nil];
        }
        
         return;
        
    }
    
    switch (self.transferRes.fileType) {
        case BSFileTypePhoto:
        {
            //照片
            [self sendPhotoData];
        }
            break;
        case BSFileTypeVideo:
        {
            //视频
             [self sendVideoData];
        }
            break;
        case BSFileTypeContact:
        {
            //联系人
            [self sendContactData];
        }
            break;
        case BSFileTypeMusic:
        {
            //音乐
            [self sendMusicData];
        }
            break;
        case BSFileTypeCalendar:
        {
            //日历
            [self sendCalendartData];
        }
            break;
            
        default:
            break;
    }
    
    self.progressVC.fileType = self.transferRes.fileType;
    
    kWeakSelf
     if (self.refreshProgress) {
         dispatch_async(dispatch_get_main_queue(), ^{
             weakSelf.refreshProgress(weakSelf.transferRes.index, weakSelf.transferRes.total);
         });
    }
    
    if (self.transferRes.resArray.count > 0){
        [self.transferRes.resArray removeLastObject];
    }
    
    self.transferRes.index++;

}

- (void)pkgResArray {
    
    for (NSInteger i = self.transferRes.cursor; i < self.resGroupArray.count; i++) {
        BSResGroupModel *resGroup = self.resGroupArray[i];
        
        if (resGroup.isSelected) {
            
            self.transferRes.cursor = i;
            
            switch (resGroup.resType) {
                case BSResTypePhoto:
                {
                    //照片
                    self.transferRes.fileType = BSFileTypePhoto;
                    
                    for (TZAssetModel *model in resGroup.resArray) {
                        if (model.isSelected) {
                            [self.transferRes.resArray addObject:model];
                        }
                    }
                    
                }
                    break;
                case BSResTypeVideo:
                {
                    //视频
                    self.transferRes.fileType = BSFileTypeVideo;
                    
                    for (TZAssetModel *model in resGroup.resArray) {
                        if (model.isSelected) {
                            [self.transferRes.resArray addObject:model];
                        }
                    }
                    
                }
                    break;
                case BSResTypeContact:
                {
                    //联系人
                    self.transferRes.fileType = BSFileTypeContact;
                    
                    for (BSContact *model in resGroup.resArray) {
                        if (model.isSelected) {
                            
                            NSMutableArray *phones = @[].mutableCopy;
                            
                            for (CNLabeledValue *labelValue in model.contact.phoneNumbers) {
                                CNPhoneNumber *phoneNumber = labelValue.value;
                                [phones addObject:phoneNumber.stringValue];
                            }
                            NSString *nameStr = [NSString stringWithFormat:@"%@%@",model.contact.familyName,model.contact.givenName];
                            NSDictionary *infoDict = @{
                                                       @"ContactName":nameStr,
                                                       @"ContactPhotoNumberArray":phones
                                                       };
                            [self.transferRes.resArray addObject:infoDict];
                        }
                    }
                }
                    break;
                case BSResTypeMusic:
                {
                    //音乐
                    self.transferRes.fileType = BSFileTypeMusic;
                    
                    for (BSMusic *model in resGroup.resArray) {
                           if (model.isSelected) {
                               [self.transferRes.resArray addObject:model];
                           }
                       }
                }
                    break;
                case BSResTypeCalendar:
                {
                    //日历
                    self.transferRes.fileType = BSFileTypeCalendar;
                    
                    for (BSCalender *model in resGroup.resArray) {
                        if (model.isSelected) {
                            [self.transferRes.resArray addObject:model.infoDict];
                        }
                    }
                }
                    break;
                    
                default:
                    break;
            }
            
            break;
            
        }
        
    }
    
    self.transferRes.cursor += 1;
}

- (void)sendPhotoData {
    
    TZAssetModel *model = [self.transferRes.resArray lastObject];
    
    kWeakSelf
    [[TZImageManager manager] getOriginalPhotoDataWithAsset:model.asset completion:^(NSData *data, NSDictionary *info, BOOL isDegraded) {
      
        dispatch_sync(dispatch_get_global_queue(0, 0), ^{
           
            [weakSelf.session sendData:data toPeers:@[weakSelf.peerModel.peerID] withMode:MCSessionSendDataReliable error:nil];
            
        });
    }];
  
}

- (void)sendVideoData {
    
    TZAssetModel *model = [self.transferRes.resArray lastObject];
    
    kWeakSelf
    [[TZImageManager manager] getVideoOutputPathWithAsset:model.asset success:^(NSString *outputPath) {
        
        dispatch_sync(dispatch_get_global_queue(0, 0), ^{
            
            NSData *data = [[NSData alloc] initWithContentsOfURL:[NSURL fileURLWithPath:outputPath]];
            
            [weakSelf.session sendData:data toPeers:@[weakSelf.peerModel.peerID] withMode:MCSessionSendDataReliable error:nil];
            
        });
        
    } failure:^(NSString *errorMessage, NSError *error) {
        
    }];
    
}

- (void)sendContactData {
    
    kWeakSelf
    dispatch_sync(dispatch_get_global_queue(0, 0), ^{
        
        NSDictionary *infoDict = [weakSelf.transferRes.resArray lastObject];
        [weakSelf.session sendData:infoDict.mj_JSONData toPeers:@[weakSelf.peerModel.peerID] withMode:MCSessionSendDataReliable error:nil];
        
    });
    
}

- (void)sendCalendartData {
    
    kWeakSelf
    dispatch_sync(dispatch_get_global_queue(0, 0), ^{
        
        NSDictionary *infoDict = [weakSelf.transferRes.resArray lastObject];
        [weakSelf.session sendData:infoDict.mj_JSONData toPeers:@[weakSelf.peerModel.peerID] withMode:MCSessionSendDataReliable error:nil];
        
    });
    
}

- (void)sendMusicData {
    
    BSMusic *music = [self.transferRes.resArray lastObject];
    
    NSString *musicTitle = music.infoDict[@"title"];
    NSString *musicSinger = music.infoDict[@"singer"];
    NSURL *musicUrl = music.infoDict[@"fileURL"];
    
    NSString *cachesPath = [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES) lastObject];
    
    BOOL hasImage = NO;
    //Image
    NSString *imageName = [NSString stringWithFormat:@"%@.jpg", [musicTitle jk_md5String]];
    NSString *imagePath = [cachesPath stringByAppendingPathComponent:imageName];
    
    if (music.image) {
        hasImage = YES;
        
        NSData *imageData = UIImagePNGRepresentation(music.image);
        if (!imageData) {
            imageData = UIImageJPEGRepresentation(music.image, 1.0);
        }
        
        [imageData writeToFile:imagePath atomically:YES];
        
    }
    
    NSString *musicFileName = [NSString stringWithFormat:@"%@.m4a", musicTitle];
    
    //Info
    NSDictionary *infoDict = @{
                               @"title":musicTitle,
                               @"singer":musicSinger,
                               @"hasImage":@(hasImage),
                               @"imageFileName":imageName,
                               @"configFileName":@"config.plist",
                               @"musicName":musicFileName
                               };
    
    NSString *musicInfoPath = [cachesPath stringByAppendingPathComponent:@"config.plist"];
    [infoDict writeToFile:musicInfoPath atomically:YES];
    
    //Music
    AVURLAsset *songAsset = [AVURLAsset URLAssetWithURL:musicUrl options:nil];
    [AVAssetExportSession exportPresetsCompatibleWithAsset:songAsset];
    
    AVAssetExportSession *session = [[AVAssetExportSession alloc] initWithAsset:songAsset presetName:AVAssetExportPresetAppleM4A];
    session.outputFileType=@"com.apple.m4a-audio";
    
    NSString *musicPath = [cachesPath stringByAppendingPathComponent:musicFileName];
    if ([[NSFileManager defaultManager] fileExistsAtPath:musicPath]) {
        [[NSFileManager defaultManager] removeItemAtPath:musicPath error:nil];
    }
    
    session.outputURL = [NSURL fileURLWithPath:musicPath];
    
    kWeakSelf
    [session exportAsynchronouslyWithCompletionHandler:^{
        
        NSArray *filesPath = @[imagePath, musicInfoPath, musicPath];
        
        NSString *zipPath = [cachesPath stringByAppendingPathComponent:@"SSZipArchive.zip"];
        [SSZipArchive createZipFileAtPath:zipPath withFilesAtPaths:filesPath];
        
        dispatch_sync(dispatch_get_global_queue(0, 0), ^{
            
            [weakSelf.session sendData:[NSData dataWithContentsOfURL:[NSURL fileURLWithPath:zipPath]] toPeers:@[weakSelf.peerModel.peerID] withMode:MCSessionSendDataReliable error:nil];
            
        });
        
    }];
    
}


#pragma mark - Getter
- (NSMutableArray<BSPeerModel *> *)peerArray {
    if (!_peerArray) {
        _peerArray = @[].mutableCopy;
    }
    return _peerArray;
}

@end
