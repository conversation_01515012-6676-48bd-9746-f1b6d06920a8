//
//  BSUserDefaultManager.m
//  PhoneClone
//
//  Created by macbookair on 14/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSUserDefaultManager.h"
@interface BSUserDefaultManager ()
@end

#define BSUserDefaults [NSUserDefaults standardUserDefaults]

// 第一个打开app的标识字段(空或者YES表示是第一次打开app,否则则表示不是第一次打开app)
#define Open_App_State_Key @"open_app_state"
//===================================================================

// 登录用户信息的key
#define Login_User_Info_Key @"login_user_info"
// 登录用户信息中[email]属性的key
#define Email_Of_Login_User_Info_Key @"email"
// 登录用户信息中[路径]属性的key
#define Path_Of_Login_User_Info_Key @"path"
//===================================================================

@implementation BSUserDefaultManager
/// 设置app打开状态
/// @param state 状态值(0:第一次打开app  1:已接受协议,但未进入主页 2:已进入主页)
+ (void)setOpenAppState:(OpenAppState)state{
    [BSUserDefaults setInteger:state forKey:Open_App_State_Key];
    [BSUserDefaults synchronize];
}

/// 获取app打开状态
/// @return 状态值(0:第一次打开app  1:已接受协议,但未进入主页 2:已进入主页)
+ (OpenAppState)getOpenAppState{
    id appOpenState = [BSUserDefaults objectForKey:Open_App_State_Key];
    if (appOpenState == nil || appOpenState == First_Open_App) {
        return First_Open_App;
    }
    
    return [appOpenState integerValue];
}

/// 判断是否第一次打开app
/// @return YES:是第一次打开app  NO:不是第一次打开app
+ (BOOL)isFirstOpenApp{
    id isFirstOpenApp = [BSUserDefaults objectForKey:Open_App_State_Key];
    if(isFirstOpenApp == nil||[isFirstOpenApp boolValue]==YES){
        return YES;
    }
    return NO;
}

/// 是否存储登录用户
/// @return YES:已存在登录用户  NO:不存在登录用户
+ (BOOL)isExistLoginUser{
    if ([BSUserDefaults objectForKey:Login_User_Info_Key]) {
        return YES;
    } else {
        return NO;
    }
}

/// 获取用户登录信息(从UserDefault本地存储中)
+ (BSAccountModel*)getUserModelByUserDefault{
    if ([self isExistLoginUser]) {
        NSDictionary *userInfoDic = [BSUserDefaults objectForKey:Login_User_Info_Key];
        return [BSAccountModel createObjectWithEmail:userInfoDic[Email_Of_Login_User_Info_Key]
                                            path:userInfoDic[Path_Of_Login_User_Info_Key]];
    } else {
        return [[BSAccountModel alloc] init];
    }
}

/// 将用户登录信息保存到UserDefault本地存储
/// @param userModel 用户登录信息model
+ (void)saveLoginUserWithUserModel:(BSAccountModel*)userModel{
    NSDictionary *userInfoDic = @{
        Email_Of_Login_User_Info_Key : userModel.email,
        Path_Of_Login_User_Info_Key : userModel.path,
    };
    [BSUserDefaults setObject:userInfoDic forKey:Login_User_Info_Key];
    [BSUserDefaults synchronize];
}

/// 退出用户登录,清空UserDefault本地存储的用户登录信息
+ (void)exitLoginUser{
    [BSUserDefaults removeObjectForKey:Login_User_Info_Key];
    [BSUserDefaults synchronize];
}
@end
