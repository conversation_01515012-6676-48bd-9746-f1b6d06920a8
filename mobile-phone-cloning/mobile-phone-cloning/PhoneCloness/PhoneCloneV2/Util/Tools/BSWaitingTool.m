//
//  BSWaitingTool.m
//  PhoneClone
//
//  Created by macbookair on 18/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSWaitingTool.h"
@interface BSWaitingTool ()
@property (nonatomic, strong) UIActivityIndicatorView *mainWaitingIndicatorView;
@end

@implementation BSWaitingTool
- (instancetype)init{
    self = [super init];
    if (self) {
        self.mainWaitingIndicatorView = [[UIActivityIndicatorView alloc] init];
        self.mainWaitingIndicatorView.frame = CGRectMake(0, 0, kScreenWidth, kScreenHeight);
    }
    return self;
}

+ (instancetype)shareSingleton{
    static BSWaitingTool *singleton = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
                      singleton = [[BSWaitingTool alloc] init];
                  });
    return singleton;
}

/// [启动]等待交互,冻结界面
/// @param superview 等待图标所在的父试图
+ (void)startWaitWithSuperview:(UIView*)superview{
    BSWaitingTool *singleton = [BSWaitingTool shareSingleton];
    //容错处理,如果等待动画已经启动,先停下上一个等待动画,再启动当前等待动画
    if ([singleton.mainWaitingIndicatorView isAnimating]) {
        [self stopWaitWithSuperview];
    }
    superview.userInteractionEnabled = NO;
    superview.alpha = 0.5f;
    [superview addSubview:singleton.mainWaitingIndicatorView];
    [singleton.mainWaitingIndicatorView startAnimating];
}

/// [关闭]等待交互
+ (void)stopWaitWithSuperview{
    BSWaitingTool *singleton = [BSWaitingTool shareSingleton];
    UIView *superview = singleton.mainWaitingIndicatorView.superview;
    if(superview == nil){
        return;
    }
    //容错处理,如果等待动画没有启动,将不用停止
    if (![singleton.mainWaitingIndicatorView isAnimating]) {
        return;
    }
    superview.userInteractionEnabled = YES;
    superview.alpha = 1.0f;
    [singleton.mainWaitingIndicatorView stopAnimating];
    [singleton.mainWaitingIndicatorView removeFromSuperview];
}

@end
