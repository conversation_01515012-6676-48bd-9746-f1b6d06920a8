//
//  BSWaitingTool.h
//  PhoneClone
//
//  Created by macbookair on 18/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/// 界面等待的工具类
@interface BSWaitingTool : NSObject

/// [启动]等待交互,冻结界面
/// @param superview 等待图标所在的父试图
+ (void)startWaitWithSuperview:(UIView*)superview;

/// [关闭]等待交互
+ (void)stopWaitWithSuperview;

@end

NS_ASSUME_NONNULL_END
