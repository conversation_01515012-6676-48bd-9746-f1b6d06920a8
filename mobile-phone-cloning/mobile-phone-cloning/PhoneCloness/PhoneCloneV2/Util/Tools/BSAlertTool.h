//
//  BSAlertTool.h
//  PhoneClone
//
//  Created by macbookair on 18/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/// 弹出框的工具类
@interface BSAlertTool : NSObject

/// 加载[信息]弹出框
/// @param message 信息内容
/// @param okText 确认文字
/// @param currentController 当前控制器(用于present到Alert控制器)
/// @param handler 点击确认后的回调
+ (void)loadAlertOfMessage:(NSString*)message okText:(NSString*)okText currentController:(UIViewController*)currentController handler:(void (^)(UIAlertAction * _Nonnull action))handler;

@end

NS_ASSUME_NONNULL_END
