//
//  BSAlertTool.m
//  PhoneClone
//
//  Created by macbookair on 18/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSAlertTool.h"
@interface BSAlertTool()

@end
@implementation BSAlertTool
/// 加载[信息]弹出框
/// @param message 信息内容
/// @param okText 确认文字
/// @param currentController 当前控制器(用于present到Alert控制器)
/// @param handler 点击确认后的回调
+ (void)loadAlertOfMessage:(NSString*)message okText:(NSString*)okText currentController:(UIViewController*)currentController handler:(void (^)(UIAlertAction * _Nonnull action))handler{
    UIAlertController *mainAlertCtl = [UIAlertController alertControllerWithTitle:message message:nil preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *action = [UIAlertAction actionWithTitle:okText style:UIAlertActionStyleDefault handler:handler];
    [mainAlertCtl addAction:action];
    [currentController presentViewController:mainAlertCtl animated:YES completion:nil];
}

@end
