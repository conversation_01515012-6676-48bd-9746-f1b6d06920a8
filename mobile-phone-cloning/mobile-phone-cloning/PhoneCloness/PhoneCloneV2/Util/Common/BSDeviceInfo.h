//
//  BSDeviceInfo.h
//  PhoneClone
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <UIKit/UIKit.h>

@interface BSDeviceInfo : NSObject

#pragma mark - 基础信息
+ (NSString *)deviceModel;
+ (NSString *)deviceName;
+ (NSString *)deviceSystemVersion;
+ (NSString *)deviceSystemName;

#pragma mark - 电池信息
+ (NSString *)batteryState;
+ (NSString *)batteryQuantity;

#pragma mark - 内存信息
+ (long long)totalMemorySize;
+ (long long)availableMemorySize;
+ (long long)usedMemorySize;

#pragma mark - 存储容量信息
+ (long long)totalDiskSpaceSize;
+ (long long)availableDiskSpaceSize;
+ (long long)usedDiskSpaceSize;

+ (NSString *)fileSizeToString:(long long)fileSize;

@end
