//
//  NSString+Predicate.m
//  PhoneClone
//
//  Created by macbookair on 22/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "NSString+Predicate.h"

@implementation NSString (Predicate)
/// 邮箱格式的正则校验
/// @param email 待校验的字符串
+ (BOOL) validateEmail:(NSString *)email{
    NSString *emailRegex = @"[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,4}";
    NSPredicate *emailTest = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", emailRegex];
    return [emailTest evaluateWithObject:email];
}
@end
