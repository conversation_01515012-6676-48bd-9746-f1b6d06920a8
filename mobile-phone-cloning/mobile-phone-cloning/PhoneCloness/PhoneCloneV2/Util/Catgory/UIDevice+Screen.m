//
//  UIDevice+Screen.m
//  PhoneClone
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "UIDevice+Screen.h"

@implementation UIDevice (Screen)

#pragma mark - 设备分辨率
+ (NSString *)currentResolution{
    CGSize size = [[UIScreen mainScreen] bounds].size;
    if ([[UIScreen mainScreen] respondsToSelector:@selector(scale)]) {
        
        return [NSString stringWithFormat:@"%.0fx%.0f", size.width * [UIScreen mainScreen].scale, size.height * [UIScreen mainScreen].scale];
    }
    
    return [NSString stringWithFormat:@"%.0fx%.0f", size.width, size.height];
}

#pragma mark - 设备尺寸
+ (NSString *)currentSize {
    
    return [NSString stringWithFormat:@"%.0fx%.0f", [UIScreen mainScreen].bounds.size.width, [UIScreen mainScreen].bounds.size.height];
}


#pragma mark - 获取设备是否是Retina屏幕
+ (BOOL)isRetina {
    
    return [[UIScreen mainScreen] scale] > 1.0f ? YES : NO;
}

#pragma mark - 获取设备是否是Retina HD屏幕
+ (BOOL)isRetinaHD {
    return [[UIScreen mainScreen] respondsToSelector:@selector(scale)] && [[UIScreen mainScreen] scale] == 3.0 ? YES : NO;
}

#pragma mark - 获取设备比例
+ (NSString *)currentRatio {
    return @"16:9";
}

#pragma mark - 获取屏幕ppi
+ (NSString *)currentPPI {
    return [NSString stringWithFormat:@"%.0fppi", [UIScreen mainScreen].scale * 163];
}


@end
