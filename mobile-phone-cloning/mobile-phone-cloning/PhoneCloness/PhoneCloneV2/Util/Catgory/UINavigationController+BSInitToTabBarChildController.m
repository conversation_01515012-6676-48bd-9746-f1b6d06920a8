//
//  UINavigationController+BSInitToTabBarChildController.m
//  PhoneClone
//
//  Created by macbookair on 17/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "UINavigationController+BSInitToTabBarChildController.h"
#import "BSNavigationController.h"
@implementation UINavigationController (BSInitToTabBarChildController)
+ (instancetype)initWithRootControllerName:(NSString*)rootControllerName title:(NSString*)title normalImageName:(NSString*)normalImageName selectedImageName:(NSString*)selectedImageName navControllerType:(NSInteger)navControllerType{
    NSString *name = rootControllerName;
    Class cls = NSClassFromString(name);
    UIViewController *rootViewCtl = [[cls alloc] init];
    rootViewCtl.title = title;
    
    UINavigationController *mainNavCtl;
    if (navControllerType == 1) {
        mainNavCtl = [[BSNavigationController alloc] initWithRootViewController:rootViewCtl];
    } else {
        mainNavCtl = [[UINavigationController alloc] initWithRootViewController:rootViewCtl];
    }
    mainNavCtl.tabBarItem.image = [UIImage loadOriginalImage:normalImageName];
    mainNavCtl.tabBarItem.selectedImage = [UIImage loadOriginalImage:selectedImageName];
    return mainNavCtl;
}
@end
