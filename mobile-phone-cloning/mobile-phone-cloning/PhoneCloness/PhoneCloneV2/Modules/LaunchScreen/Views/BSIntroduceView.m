//
//  BSIntroduceView.m
//  PhoneClone
//
//  Created by macbookair on 12/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSIntroduceView.h"
#import "Reachability.h"
@interface BSIntroduceView()
/// 主体背景试图
@property(nonatomic, strong) UIImageView *mainBackground;
/// 背景图片的集合数组
@property(nonatomic, strong) NSMutableArray <UIImage*> *bgImgMarr;
/// 核心滑动试图
@property (nonatomic, strong) UIScrollView *mainScrollView;

/// 滑动位标识点的控件
@property (nonatomic, strong) UIPageControl *pageControl;

//  [下一步]按钮控件
@property (nonatomic, strong) UIButton *nextBtn;

/// 滑动试图总共页面的数量
@property (nonatomic, assign) NSInteger sumPage;
/// 当前页码
@property (nonatomic, assign) NSInteger currentPage;

@end
@implementation BSIntroduceView
/// 加载介绍引导滑动试图
/// @param backgrounds 背景图片的集合
- (void)loadIntroduceScrollViewWithBackgrounds:(NSArray<NSString*>*)backgrounds titleArray:(NSArray<NSString*>*)titleArray detailsArray:(NSArray<NSString*>*)detailsArray{
    [self bk_eachSubview:^(UIView * _Nonnull subview) {
        [subview removeFromSuperview];
    }];
    CGFloat width = self.frame.size.width;
    CGFloat height = self.frame.size.height;
    self.sumPage = backgrounds.count;
    
    self.bgImgMarr = [NSMutableArray array];
    for (NSString *imgName in backgrounds) {
        [self.bgImgMarr addObject:[UIImage imageWithContentsOfFile:[[NSBundle mainBundle] pathForResource:imgName ofType:@"jpg"]]];
    }
    //加载主体背景试图
    self.mainBackground = [[UIImageView alloc] init];
    self.mainBackground.frame = CGRectMake(0, 0, 1.2*self.jk_width, self.jk_height);
    [self.mainBackground setImage:[self.bgImgMarr firstObject]];
    [self addSubview:self.mainBackground];
    
    //核心滑动试图
    self.mainScrollView = [[UIScrollView alloc] initWithFrame:self.frame];
    self.mainScrollView.contentSize = CGSizeMake(self.sumPage*width, height);
    self.mainScrollView.showsHorizontalScrollIndicator = NO;
    self.mainScrollView.showsVerticalScrollIndicator = NO;
    self.mainScrollView.pagingEnabled = YES;
    for (int i=0; i<self.sumPage; i++) {
        UIImageView *backgroundImgView = [[UIImageView alloc] init];
//        backgroundImgView.backgroundColor = [UIColor lightGrayColor];
        backgroundImgView.frame = CGRectMake(width*i, 0, width, height);
        backgroundImgView.tag = i;
        [self.mainScrollView addSubview:backgroundImgView];
        
        UILabel *titleLab = [[UILabel alloc] init];
        titleLab.text = titleArray[i];
        titleLab.font = [UIFont boldSystemFontOfSize:20];
        titleLab.textAlignment = NSTextAlignmentCenter;
        titleLab.textColor = [UIColor whiteColor];
        titleLab.numberOfLines = 0;
        if(AppDelegate.getAppDelegate.showGuide&&i==self.sumPage-1)
        {
            titleLab.font = [UIFont systemFontOfSize:15];
        }
//        titleLab.adjustsFontSizeToFitWidth = YES;
        [backgroundImgView addSubview:titleLab];
        [titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(@0);
            make.width.equalTo(backgroundImgView.mas_width).multipliedBy(0.7);
            make.bottom.equalTo(backgroundImgView.mas_bottom).offset(-1.0f/4*kScreenHeight);
        }];
        if(AppDelegate.getAppDelegate.showGuide&&i==self.sumPage-1)
        {
            titleLab.font = [UIFont systemFontOfSize:15];
            UIView* buttonView = [UIView new];
            backgroundImgView.userInteractionEnabled = YES;
            [backgroundImgView addSubview:buttonView];
            [buttonView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(titleLab.mas_bottom);
                make.centerX.equalTo(@0);
                make.width.equalTo(backgroundImgView.mas_width);
                make.height.mas_equalTo(60);
            }];
            
            CGFloat buttonW = 80;
            CGFloat buttonLinw = (kDeviceWidth  - buttonW*3)/6;
            
            NSArray *buttonTitleArray = @[NSLocalizedString(@"Privacy Policy",nil),NSLocalizedString(@"Restore Purchase",nil),NSLocalizedString(@"Terms Of Use",nil)];
            for(int j = 0;j<3;j++){
                UIButton *restoreButton =[[UIButton alloc]initWithFrame:CGRectMake(buttonLinw*2+(buttonLinw+buttonW)*j,0,buttonW, 40)];
                //restoreButton.backgroundColor = [UIColor whiteColor];
                [restoreButton setTitle:buttonTitleArray[j] forState:UIControlStateNormal];
                [restoreButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
                restoreButton.titleLabel.font = [UIFont boldSystemFontOfSize:12];
                restoreButton.titleLabel.adjustsFontSizeToFitWidth = YES;
                restoreButton.tag = 500+j;
//                [restoreButton bk_whenTapped:^{
//                    NSLog(@"dianle");
//                }];
                [restoreButton addTarget:self action:@selector(restoreStyleActon:) forControlEvents:UIControlEventTouchUpInside];
                [buttonView addSubview:restoreButton];
                
                UILabel *lineButLabel = [[UILabel alloc]initWithFrame:CGRectMake((restoreButton.jk_width-60)/2,restoreButton.jk_height-10,60,1)];
                lineButLabel.backgroundColor = [UIColor whiteColor];
                [restoreButton addSubview:lineButLabel];
            }
        }
        else
        {
            UILabel *detailsLab = [[UILabel alloc] init];
            detailsLab.text = detailsArray[i];
            detailsLab.textAlignment = NSTextAlignmentCenter;
            detailsLab.textColor = [UIColor whiteColor];
            detailsLab.numberOfLines = 0;
            [backgroundImgView addSubview:detailsLab];
            [detailsLab mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(titleLab.mas_bottom);
                make.centerX.equalTo(@0);
                make.width.equalTo(backgroundImgView.mas_width).multipliedBy(0.7);
            }];
        }
        
        
        
    }
    self.mainScrollView.scrollEnabled = NO;
    [self addSubview:self.mainScrollView];
    self.currentPage = 0;
    
    //滑动位标识点的控件
    self.pageControl = [[UIPageControl alloc] initWithFrame:CGRectMake(0, height - 140, width, 20)];
    self.pageControl.backgroundColor = [UIColor clearColor];
    self.pageControl.numberOfPages = self.sumPage;
    self.pageControl.pageIndicatorTintColor = [UIColor lightGrayColor];
    self.pageControl.currentPageIndicatorTintColor = [UIColor whiteColor];
    [self addSubview:self.pageControl];
    
    //[下一步]按钮控件
    CGFloat nextBtnWidth = 3.5f/5 * width;
    CGFloat nextBtnHeigt = 50;
    self.nextBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    self.nextBtn.frame = CGRectMake(width/2 - nextBtnWidth/2, kScreenHeight - 60 - nextBtnHeigt, nextBtnWidth, nextBtnHeigt);
    [self.nextBtn setTitle:NSLocalizedString(@"继续", nil) forState:UIControlStateNormal];
    [self.nextBtn setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    self.nextBtn.titleLabel.font = [UIFont boldSystemFontOfSize:20];
    self.nextBtn.backgroundColor = BKpickColorFrom(whiteColor);
    self.nextBtn.layer.cornerRadius = 25;
    self.nextBtn.layer.masksToBounds = YES;
    self.nextBtn.titleLabel.numberOfLines = 0;
    self.nextBtn.titleLabel.minimumScaleFactor = 0.2;
    self.nextBtn.titleLabel.textAlignment = NSTextAlignmentCenter;
    self.nextBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 10, 0, 10);
    [self addSubview:self.nextBtn];
    [self.nextBtn addTarget:self action:@selector(nextBtnAction:) forControlEvents:UIControlEventTouchUpInside];
}

/// [下一步]按钮的点击事件
/// @param sender 按钮对象
- (void)nextBtnAction:(UIButton*)sender{
    CGFloat pointX = self.mainScrollView.contentOffset.x;
    CGFloat width = self.mainScrollView.frame.size.width;
    /// 介绍引导页面结束后,切换到TabBar主控制器
    ///
    if (self.currentPage == self.sumPage-2&&AppDelegate.getAppDelegate.showGuide)
    {
        UIButton* close = [UIButton createButtonWithImageName:@"关闭"];
        close.jk_top = kDeviceHeight-80-KDNavH-40-100;
        [self.superview addSubview:close];
        close.frame = CGRectMake(kDeviceWidth-20-40, KDNavH+40+20, 28, 28);
        [close bk_whenTapped:^{
            [self.delegate enterTabbar];
        }];
        
        NSString *weekPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureWId"];

        if (!weekPrice) {
            weekPrice = @"18.00";
        }
        [self.nextBtn setTitle:[NSString stringWithFormat:local(@"立即订阅\n3天免费之后%@/周"),weekPrice] forState:0];
    }
    
    if (self.currentPage+1 >= self.sumPage) {
        [self.delegate gotoEnterViewController];
        return;
    }
    
    //动画开始前禁用按钮,以免造成动画混乱
    [self.nextBtn setEnabled:NO];
    // 翻页过程先将下一页控件内容隐藏,为控件滑动出现做准备
    UIView *backgroundImgView = self.mainScrollView.subviews[self.currentPage+1];//翻页后的背景试图
    for (UIView *currentView in backgroundImgView.subviews) {
        currentView.alpha = 0.0f;
    }
    self.currentPage = self.currentPage + 1;//将当前页码+1,进行翻页操作
    //播放下一页展示动画
    [UIView animateWithDuration:0.2 animations:^{
        self.mainScrollView.contentOffset = CGPointMake(pointX + width, 0);
        self.pageControl.currentPage = self.currentPage;
    } completion:^(BOOL finished) {
        UIView *backgroundImgView = self.mainScrollView.subviews[self.currentPage];//翻页后的背景试图
        CGAffineTransform startTransform = CGAffineTransformMakeTranslation(self.frame.size.width, 0);//动画开始的镜像参数
        CGAffineTransform endTransform = CGAffineTransformMakeTranslation(0, 0);//动画结束的镜像参数
        NSTimeInterval duration = 0.5;//每个试图动画时间
        // 翻页结束后,先显示控件内容,再让控件滑动展示出来
        for (UIView *currentView in backgroundImgView.subviews) {
            currentView.alpha = 1.0f;
        }
        [UIView startTransformCollectAnimationWithViewsArray:backgroundImgView.subviews duration:duration startTransform:startTransform endTransform:endTransform completion:^(BOOL finished) {
            //动画结束后,启用按钮功能,恢复界面交互
            [self.nextBtn setEnabled:YES];
        }];
    }];
    
    // 背景试图的波动动画
    [self waveAnimationWithIndex:self.currentPage];
}


/// 波动动画持续时间
NSTimeInterval const wareDuration = 7.0;
/// 第一次进入界面的波动动画
- (void)waveAnimationOfFirst{
    if (self.mainBackground != nil) {
        [self.mainBackground removeFromSuperview];
        self.mainBackground = nil;
    }
    self.mainBackground = [[UIImageView alloc] init];
    self.mainBackground.frame = CGRectMake(0, 0, 1.2*self.jk_width, self.jk_height);
    [self.mainBackground setImage:[self.bgImgMarr firstObject]];
    [self addSubview:self.mainBackground];
    [self sendSubviewToBack:self.mainBackground];
    [UIView animateWithDuration:0.5 animations:^{
        self.mainBackground.alpha = 1.0;
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:wareDuration animations:^{
            self.mainBackground.transform = CGAffineTransformMakeTranslation(-0.2*self.jk_width, 0);
        } completion:nil];
    }];
}

/// 翻页后的波动动画
/// @param index 动画试图下标
- (void)waveAnimationWithIndex:(NSInteger)index{
    
    AFHTTPSessionManager *_sessionManager = [AFHTTPSessionManager manager];;
    _sessionManager.requestSerializer = [AFJSONRequestSerializer serializer];
    _sessionManager.requestSerializer.cachePolicy = NSURLRequestReloadIgnoringLocalCacheData;
    [_sessionManager.requestSerializer setValue:@"application/json" forHTTPHeaderField:@"Accept"];
    [_sessionManager GET:updateJsonUrl parameters:nil headers:nil progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        NSDictionary* dic = responseObject;
        if([dic isKindOfClass:[NSDictionary class]])
        {

            BOOL showguid = [dic[@"showGuide"] boolValue];
            if(showguid == YES && !AppDelegate.getAppDelegate.showGuide)
            {
                NSString *weekPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureWId"];

                if (!weekPrice) {
                    weekPrice = @"18.00";
                }
                
                [self loadIntroduceScrollViewWithBackgrounds:@[@"IntroduceBackground01", @"IntroduceBackground02",@"IntroduceBackground01"] titleArray:@[NSLocalizedString(@"一键换机", nil),NSLocalizedString(@"快速安全", nil),[NSString stringWithFormat:local(@"免费试用3天体验一键换机全部功能权限，您可以在试用期间无限制的使用所有功能，如果体验不满意可以随时取消，请在当前订阅周期到期前24小时以前前三天免费后按%@/周，自动更新，随时取消"),weekPrice]] detailsArray:@[NSLocalizedString(@"一键同步手机资料", nil),NSLocalizedString(@"一键选择一键发送", nil),@""]];
            }
            AppDelegate.getAppDelegate.showGuide = showguid;
            BOOL showWebLink = [dic[@"showWebLink"] boolValue];
            AppDelegate.getAppDelegate.showWebLink = showWebLink;
//            self.isShowGuide = YES;

//            self.isAnyOK =  YES;
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
//        if(!AppDelegate.getAppDelegate.showGuide)
//        {
//            [self loadIntroduceScrollViewWithBackgrounds:@[@"IntroduceBackground01", @"IntroduceBackground02",@"IntroduceBackground01"] titleArray:@[NSLocalizedString(@"一键换机", nil),NSLocalizedString(@"快速安全", nil),[NSString stringWithFormat:local(@"免费试用3天体验一键换机全部功能权限，您可以在试用期间无限制的使用所有功能，如果体验不满意可以随时取消，请在当前订阅周期到期前24小时以前前三天免费后按%@/周，自动更新，随时取消"),@"18.00"]] detailsArray:@[NSLocalizedString(@"一键同步手机资料", nil),NSLocalizedString(@"一键选择一键发送", nil),@""]];
//            BOOL showguid = YES;
//            AppDelegate.getAppDelegate.showGuide = showguid;
//        }

    }];
    
    
    if (self.mainBackground != nil) {
        [self.mainBackground removeFromSuperview];
        self.mainBackground = nil;
    }
    self.mainBackground = [[UIImageView alloc] init];
    self.mainBackground.frame = CGRectMake(0, 0, 1.2*self.jk_width, self.jk_height);
    [self.mainBackground setImage:self.bgImgMarr[index]];
    [self addSubview:self.mainBackground];
    [self sendSubviewToBack:self.mainBackground];
    
    [UIView animateWithDuration:0.5 animations:^{
        self.mainBackground.alpha = 0.8;
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:0.5 animations:^{
            self.mainBackground.alpha = 1.0;
        } completion:^(BOOL finished) {
            [UIView animateWithDuration:wareDuration animations:^{
                self.mainBackground.transform = CGAffineTransformMakeTranslation(-0.2*self.jk_width, 0);
            } completion:nil];
        }];
    }];
}



- (void)restoreStyleActon:(UIButton *)sender{
    if(sender.tag == 500){
        //隐私政策
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:@"https://www.freeprivacypolicy.com/privacy/view/2e6556241582cf0a67fbb9b06cd98b0d"]];
    }else if (sender.tag ==501){
        //恢复购买
        [SVProgressHUD show];
        if ([self checkNetworkConnection])
        {
            [[StoreIAPManager sharedManager] restoreButClick:^{
                
            }];
            [SVProgressHUD performSelector:@selector(dismiss) withObject:self afterDelay:5];
        }
    }else{
        //服务条款
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:@"https://www.freeprivacypolicy.com/privacy/view/2e6556241582cf0a67fbb9b06cd98b0d"]];
    }
}

-(BOOL)checkNetworkConnection
{
    Reachability *reach = [Reachability reachabilityWithHostName:@"www.apple.com"];
    NetworkStatus status = [reach currentReachabilityStatus];
    if (status == 0) {
        [SVProgressHUD showInfoWithStatus:NSLocalizedString(@"No available network. Please open your network!",nil)];
        return NO;
    }
    return YES;
}
@end
