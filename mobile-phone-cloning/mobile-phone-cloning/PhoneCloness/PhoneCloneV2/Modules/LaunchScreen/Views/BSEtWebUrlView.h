//
//  BSEtWebUrlView.h
//  PhoneClone
//
//  Created by macbookair on 14/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
@protocol BSEtWebUrlViewProtocol <NSObject>
/// 订阅选项控件的点击事件
/// @param tag 控件的标识码
- (void)buttonClickActionWithTag:(NSInteger)tag;
@end
@interface BSEtWebUrlView : UIView
/// 网页链接按钮的集合数组
@property (nonatomic, strong) NSMutableArray <UIButton*> *webUrlBtnMarr;
@property(nonatomic, assign)id<BSEtWebUrlViewProtocol>mainDelegate;
/// 加载网页链接按钮的集合试图
/// @param titleArray 按钮名称的集合数组
/// @param tagArray 按钮标识码的集合数组
- (void)loadCustomWithTitleArray:(NSArray<NSString*>*)titleArray tagArray:(NSArray<NSNumber*>*)tagArray;
@end

NS_ASSUME_NONNULL_END
