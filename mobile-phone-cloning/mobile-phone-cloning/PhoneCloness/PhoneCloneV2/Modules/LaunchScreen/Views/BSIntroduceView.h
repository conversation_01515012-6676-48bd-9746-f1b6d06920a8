//
//  BSIntroduceView.h
//  PhoneClone
//
//  Created by macbookair on 12/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
@protocol BSIntroduceViewProtocol <NSObject>
/// 介绍引导页面结束后,切换到入口控制器
- (void)gotoEnterViewController;
- (void)enterTabbar;
@end

/// 介绍引导页面的试图
@interface BSIntroduceView : UIView
/// 加载介绍引导滑动试图
/// @param backgrounds 背景图片的集合
- (void)loadIntroduceScrollViewWithBackgrounds:(NSArray<NSString*>*)backgrounds titleArray:(NSArray<NSString*>*)titleArray detailsArray:(NSArray<NSString*>*)detailsArray;

/// 第一次进入界面的波动动画
- (void)waveAnimationOfFirst;

/// 翻页后的波动动画
/// @param index 动画试图下标
- (void)waveAnimationWithIndex:(NSInteger)index;

/// 介绍引导页面的代理
@property(nonatomic, assign)id<BSIntroduceViewProtocol>delegate;

@end

NS_ASSUME_NONNULL_END
