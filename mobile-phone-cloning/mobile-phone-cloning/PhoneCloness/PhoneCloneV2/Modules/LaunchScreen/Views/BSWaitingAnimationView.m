//
//  BSWaitingAnimationView.m
//  PhoneClone
//
//  Created by macbookair on 12/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSWaitingAnimationView.h"
@interface BSWaitingAnimationView()
/// 等待文本控件
@property (nonatomic, strong) UILabel *titleLab;

/// 子视图的集合
@property (nonatomic, strong) NSMutableArray<UIView*> *childrenViewMarr;

@property (nonatomic, assign) NSInteger currentIndex;

/// 动画时间计时器
@property (nonatomic, strong) NSTimer *animationTimer;

@end
@implementation BSWaitingAnimationView
/// 实例化自定义等待试图
/// @param radius 小点半径
/// @param y 所在父试图的Y轴坐标
/// @param superView 所在父试图
/// @param text 文本内容
+ (instancetype)loadCustomWithRadius:(CGFloat)radius superView:(UIView*)superView y:(CGFloat)y text:(NSString*)text{
    CGFloat space = 10;
    NSInteger pointCount = 3;
    CGFloat labHeight = 30.0f;
    CGFloat height = 2*radius + labHeight;
    CGFloat pointHeight = 2*radius;
    CGFloat width = pointCount * pointHeight + (pointCount+1)*space;
    
    BSWaitingAnimationView *mainView = [[BSWaitingAnimationView alloc] init];
    CGFloat superViewWidth = superView.frame.size.width;
    mainView.frame = CGRectMake(superViewWidth/2 - width/2, y, width, height);
    
    mainView.titleLab = [[UILabel alloc] init];
    mainView.titleLab.text = text;
    mainView.titleLab.textColor = [UIColor whiteColor];
    mainView.titleLab.numberOfLines = YES;
    mainView.titleLab.adjustsFontSizeToFitWidth = YES;
    mainView.titleLab.textAlignment = NSTextAlignmentCenter;
    mainView.titleLab.frame = CGRectMake(0, 0, width, labHeight);
    [mainView addSubview:mainView.titleLab];
    
    CGFloat pointWidth = 1.0f*(width - space*(pointCount+1))/pointCount;
    mainView.childrenViewMarr = [NSMutableArray array];
    for (int i=0; i<pointCount; i++) {
        CGFloat pointX = space + i*(space+pointWidth);
        CGFloat pointY = labHeight;
        UIView *point = [[UIView alloc] initWithFrame:CGRectMake(pointX, pointY, pointWidth, pointHeight)];
        point.backgroundColor = [UIColor whiteColor];
        point.layer.cornerRadius = radius;
        point.layer.masksToBounds = YES;
        [mainView addSubview:point];
        [mainView.childrenViewMarr addObject:point];
    }
    
    [superView addSubview:mainView];
    return mainView;
}

/// 开始动画
- (void)startAnimation{
    if(self.animationTimer == nil){
        self.animationTimer = [NSTimer scheduledTimerWithTimeInterval:0.5
                                                               target:self
                                                             selector:@selector(animationAction)
                                                             userInfo:nil
                                                              repeats:YES];
    }
}

/// 停止动画
- (void)stopAnimation{
    if (self.animationTimer != nil) {
        [self.animationTimer invalidate];
        self.animationTimer = nil;
    }
}

/// 动画执行内容
- (void)animationAction{
    NSInteger pointCount = 3;
    if (self.currentIndex >= pointCount) {
        self.currentIndex = 0;
    }
    [UIView animateWithDuration:0.4 animations:^{
        UIView *lastPoint;
        if (self.currentIndex == 0) {
            lastPoint = self.childrenViewMarr[2];
        } else {
            lastPoint = self.childrenViewMarr[self.currentIndex - 1];
        }
        lastPoint.transform = CGAffineTransformMakeScale(1.0, 1.0);
        
        UIView *point = self.childrenViewMarr[self.currentIndex];
        point.transform = CGAffineTransformMakeScale(1.2, 1.2);
        self.currentIndex = self.currentIndex + 1;
    } completion:nil];
}

@end
