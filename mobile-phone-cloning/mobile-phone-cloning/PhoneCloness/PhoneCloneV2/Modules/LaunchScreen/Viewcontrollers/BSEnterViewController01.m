//
//  BSEnterViewController.m
//  PhoneClone
//
//  Created by macbookair on 12/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSEnterViewController01.h"
#import "BSEtDetailsView.h"
#import "BSSubscribeRadio.h"
#import "BSEtWebUrlView.h"
#import "BSTabBarController.h"
#import "StoreIAPManager.h"
#import "ProgressViewUserInfo.h"
#import "Reachability.h"
#import "BSAlertTool.h"

@interface BSEnterViewController01 ()<BSSubscribeRadioProtocol,MKStoreKitDelegate,BSEtWebUrlViewProtocol>
/// 头部logo图标
@property (nonatomic, strong) UIImageView *headLogoIcon;
/// 头部标题的文本控件
@property (nonatomic, strong) UILabel *headTitleLab;

/// 订阅选项01(12个月使用期限 ¥148/年)
@property (nonatomic, strong) BSSubscribeRadio *subscribeRadio01;
/// 订阅选项02(1个月使用期限 ¥22/月)
@property (nonatomic, strong) BSSubscribeRadio *subscribeRadio02;
/// 订阅选项控件的标识码枚举
typedef NS_OPTIONS(NSUInteger, RechargeRadioTag){
    /// 第一个订阅选项控件的标识码
    RechargeRadioTag01 = 10001,
    /// 第二个订阅选项控件的标识码
    RechargeRadioTag02 = 10002,
};
/// 订阅选项的集合数组
@property (nonatomic, strong) NSArray <BSSubscribeRadio*> *subscribeRadioArr;
/// 选中的订阅选项的下标
@property (nonatomic, assign) NSInteger selectIndex;

/// 订阅解锁功能的详情说明试图
@property (nonatomic, strong) BSEtDetailsView *etDetailsView;

/// 进入主页核心按钮
@property (nonatomic, strong) UIButton *mainEnterBtn;

/// 订阅描述的文本控件
@property (nonatomic, strong) UILabel *subscribeDescribeLab;

/// 底部网页链接按钮的集合试图
@property (nonatomic, strong) BSEtWebUrlView *etWebUrlView;
@end

@implementation BSEnterViewController01
- (void)viewDidLoad {
    [super viewDidLoad];
    /// 核心背景试图
    [self loadHeadView];//加载头部试图(包括logo图标以及标题文字)
    [self loadSubscribeRadioView];//加载订阅选项试图(包括2个订阅选项框)
    [self loadDetailsView];//订阅解锁功能的详情说明试图
    [self loadMainEnterBtnView];//加载进入主页的核心按钮试图
    [self loadWebUrlView];//加载底部网页链接按钮的集合试图
    [self loadCloseBtnView];//加载[关闭]按钮控件的试图
    [self showWithAnimation];//动画展示所有控件
}
/// 加载头部试图(包括logo图标以及标题文字)
- (void)loadHeadView{
    /// 头部logo图标
    self.headLogoIcon = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"huiyuanjifen"]];
    self.headLogoIcon.contentMode = UIViewContentModeCenter;
    [self.mainBackground addSubview:self.headLogoIcon];
    [self.headLogoIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@0).offset(kStatusBarHeight);
        make.centerX.equalTo(self.mainBackground.mas_centerX);
        make.width.equalTo(@80);
        make.height.equalTo(@80);
    }];
    
    /// 头部标题的文本控件
    self.headTitleLab = [[UILabel alloc] init];
    self.headTitleLab.text = NSLocalizedString(@"无限制访问", nil);
    self.headTitleLab.textColor = [UIColor whiteColor];
    self.headTitleLab.textAlignment = NSTextAlignmentCenter;
    self.headTitleLab.font = [UIFont boldSystemFontOfSize:30];
    [self.mainBackground addSubview:self.headTitleLab];
    [self.headTitleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.headLogoIcon.mas_bottom);
        make.centerX.equalTo(self.mainBackground.mas_centerX);
        make.width.equalTo(@(1.0f/2 * kScreenWidth));
        make.height.equalTo(@60);
    }];
}

/// 加载订阅选项试图(包括2个订阅选项框)
- (void)loadSubscribeRadioView{
    // 订阅选项01(12个月使用期限 ¥148/年)
    NSString *monthPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureAId"];
    if (!monthPrice) {
        monthPrice = @"$2.99";
    }
    NSString *yeraPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureBId"];
    if (!yeraPrice) {
        yeraPrice = @"$19.99";
    }
    self.subscribeRadio01 = [[BSSubscribeRadio alloc] init];
    [self.mainBackground addSubview:self.subscribeRadio01];
    [self.subscribeRadio01 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.headTitleLab.mas_bottom);
        make.centerX.equalTo(self.mainBackground.mas_centerX);
        make.width.equalTo(@(0.8f * kScreenWidth));
        make.height.equalTo(@60);
    }];
    [self.subscribeRadio01 loadCustomContent:[NSString stringWithFormat:NSLocalizedString(@"免费试用3天，然后%@/每年",nil),yeraPrice] tag:RechargeRadioTag01];
    [self.subscribeRadio01 setSelectRadioWithState:YES];
    self.subscribeRadio01.mainDelegate = self;
    
    /// 订阅选项02(1个月使用期限 ¥22/月)
    self.subscribeRadio02 = [[BSSubscribeRadio alloc] init];
    [self.mainBackground addSubview:self.subscribeRadio02];
    [self.subscribeRadio02 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.subscribeRadio01.mas_bottom).offset(20);
        make.left.equalTo(self.subscribeRadio01.mas_left);
        make.right.equalTo(self.subscribeRadio01.mas_right);
        make.height.equalTo(self.subscribeRadio01.mas_height);
    }];
    [self.subscribeRadio02 loadCustomContent:[NSString stringWithFormat:NSLocalizedString(@"%@/每月",nil),monthPrice] tag:RechargeRadioTag02];
    
    self.subscribeRadio02.mainDelegate = self;
    
    /// 订阅选项的集合数组
    self.subscribeRadioArr = @[self.subscribeRadio01, self.subscribeRadio02];
}

/// 加载订阅解锁功能的详情说明试图
- (void)loadDetailsView{
    NSArray *detailsArr = @[
        NSLocalizedString(@"● 解锁一键传输功能", nil),
        NSLocalizedString(@"● 解锁网络测速功能", nil),
        NSLocalizedString(@"● 解锁云存储的功能", nil),
        NSLocalizedString(@"● 无广告体验", nil)
    ];
    self.etDetailsView = [[BSEtDetailsView alloc] init];
    [self.mainBackground addSubview:self.etDetailsView];
    [self.etDetailsView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.subscribeRadio02.mas_bottom).offset(0);
        make.centerX.equalTo(self.mainBackground.mas_centerX);
        make.width.equalTo(self.mainBackground.mas_width).multipliedBy(0.65f);
        make.height.equalTo(self.mainBackground.mas_height).multipliedBy(0.8f/4);
    }];
    [self.etDetailsView loadCustomWithDetailsArray:detailsArr];
}

/// 加载进入主页的核心按钮试图
- (void)loadMainEnterBtnView{
    self.mainEnterBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.mainEnterBtn setTitle:NSLocalizedString(@"立即体验", nil) forState:UIControlStateNormal];
    [self.mainEnterBtn setTintColor:[UIColor blackColor]];
    self.mainEnterBtn.titleLabel.font = [UIFont boldSystemFontOfSize:20.0f];
    self.mainEnterBtn.backgroundColor = [UIColor whiteColor];
    self.mainEnterBtn.layer.cornerRadius = 30.0f;
    self.mainEnterBtn.layer.masksToBounds = YES;
    [self.mainBackground addSubview:self.mainEnterBtn];
    [self.mainEnterBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.mainBackground.mas_bottom).offset(-60);
        make.centerX.equalTo(self.mainBackground.mas_centerX);
        make.width.equalTo(self.mainBackground.mas_width).multipliedBy(0.8f);
        make.height.equalTo(@60);
    }];
    [self.mainEnterBtn addTarget:self action:@selector(continueActon:) forControlEvents:UIControlEventTouchUpInside];
    
    self.subscribeDescribeLab = [[UILabel alloc] init];
    self.subscribeDescribeLab.text = NSLocalizedString(@"SubscriptionInstruction", nil);
    self.subscribeDescribeLab.numberOfLines = 0;
    self.subscribeDescribeLab.adjustsFontSizeToFitWidth = YES;
    self.subscribeDescribeLab.textColor = [UIColor whiteColor];
    [self.mainBackground addSubview:self.subscribeDescribeLab];
    [self.subscribeDescribeLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.mainEnterBtn.mas_top).offset(-10);
        make.centerX.equalTo(@0);
        make.width.equalTo(self.mainBackground.mas_width).multipliedBy(0.9);
        make.height.equalTo(@100);
    }];
}
- (void)continueActon:(UIButton *)sender{
    
    
    [BSWaitingTool startWaitWithSuperview:self.view];
     Reachability *reach = [Reachability reachabilityWithHostName:@"www.apple.com"];
     NetworkStatus status = [reach currentReachabilityStatus];
     switch (status) {
         case NotReachable:{
             [BSAlertTool loadAlertOfMessage:NSLocalizedString(@"No available network!","") okText:NSLocalizedString(@"OK","") currentController:self handler:nil];
         }
             break;
         default:
             break;
     }
    [StoreIAPManager sharedManager].delegate = self;
    if (self.selectIndex == 0) {
        [[StoreIAPManager sharedManager] buyFeatureB];
    }else if (self.selectIndex == 1)
    {
        [[StoreIAPManager sharedManager] buyFeatureA];
    }
     
}
- (void)failed
{
    [BSWaitingTool stopWaitWithSuperview];
}
-(void)buySuccessBack{
    [BSWaitingTool stopWaitWithSuperview];
}
- (void)productAPurchased{
    [BSWaitingTool stopWaitWithSuperview];
    [super mainEnterBtnAction];
    [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"fromscreenshow"];
}
- (void)productBPurchased{
    [BSWaitingTool stopWaitWithSuperview];
    [super mainEnterBtnAction];
    [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"fromscreenshow"];
}
/// 加载底部网页链接按钮的集合试图
- (void)loadWebUrlView{
    NSArray *titleArr = @[
        NSLocalizedString(@"隐私政策", nil),
        NSLocalizedString(@"恢复", nil),
        NSLocalizedString(@"使用条款", nil)
    ];
    NSArray *tagArr = @[
        @20001,
        @20002,
        @20003
    ];
    self.etWebUrlView = [[BSEtWebUrlView alloc] init];
    [self.mainBackground addSubview:self.etWebUrlView];
    [self.etWebUrlView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.mainBackground.mas_centerX);
        make.width.equalTo(self.mainBackground.mas_width).multipliedBy(0.8);
        make.bottom.equalTo(@0).offset(-30.0f);
        make.height.equalTo(@20);
    }];
    [self.etWebUrlView loadCustomWithTitleArray:titleArr tagArray:tagArr];
    self.etWebUrlView.mainDelegate = self;
}
- (void)buttonClickActionWithTag:(NSInteger)tag
{
    if (tag == 20001) {
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:@"https://www.freeprivacypolicy.com/privacy/view/2e6556241582cf0a67fbb9b06cd98b0d"]];
    }else if (tag == 20002) {
        [BSWaitingTool startWaitWithSuperview:self.view];
        [[StoreIAPManager sharedManager] restoreButClick:^{
                
            }];
    }else if (tag == 20003) {
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:@"https://www.freeprivacypolicy.com/terms/view/3dc6f270bb7d73a3e81e3dccdd374a8d"]];
    }
}
/// 动画展示所有控件
- (void)showWithAnimation{
    CGAffineTransform startTransform = CGAffineTransformMakeTranslation(kScreenWidth, 0);
    CGAffineTransform endTransform = CGAffineTransformMakeTranslation(0, 0);
    NSTimeInterval duration = 0.2;
    NSArray *viewArray = @[
        self.headLogoIcon,
        self.headTitleLab,
        self.subscribeRadio01,
        self.subscribeRadio02,
        self.etDetailsView,
    ];
    
    self.mainBackground.userInteractionEnabled = NO;
    [UIView startTransformCollectAnimationWithViewsArray:viewArray duration:duration startTransform:startTransform endTransform:endTransform completion:^(BOOL finished) {
        self.mainBackground.userInteractionEnabled = YES;
    }];
}


/// 订阅选项控件的点击事件
/// @param tag 控件的标识码
- (void)subscribeRadioClickActionWithTag:(NSInteger)tag{
    for (BSSubscribeRadio *radio in self.subscribeRadioArr) {
        [radio setSelectRadioWithState:NO];
    }
    self.selectIndex = tag - 10001;
    [self.subscribeRadioArr[self.selectIndex] setSelectRadioWithState:YES];
}

/// 试图出现前的调用方法
/// @param animated 是否动画
- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    self.navigationController.navigationBarHidden = YES;
}

@end
