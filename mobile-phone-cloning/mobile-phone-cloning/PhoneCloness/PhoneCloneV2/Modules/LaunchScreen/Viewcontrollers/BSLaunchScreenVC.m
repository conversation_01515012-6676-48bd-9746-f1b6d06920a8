//
//  BSLaunchScreenVC.m
//  PhoneClone
//
//  Created by macbookair on 12/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSLaunchScreenVC.h"
#import "BSIntroduceView.h"
#import "BSTabBarController.h"
#import "BSWaitingAnimationView.h"
#import "BSAgreementView.h"
#import "BSEnterViewController01.h"
#import "StoreIAPManager.h"
@interface BSLaunchScreenVC ()<BSAgreementViewProtocol, BSIntroduceViewProtocol,MKStoreKitDelegate>

@property (nonatomic, strong) BSAgreementView *agreementView;

@property (nonatomic, strong) BSIntroduceView *introduceView;

/// 用于记录是否是第一次打开app(YES:第一次打开app  NO:不是第一次打开app)
@property (nonatomic, assign) BOOL isFirstOpenApp;

@property UIButton* closeBtn;
@end

@implementation BSLaunchScreenVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.automaticallyAdjustsScrollViewInsets = NO;
    
    self.introduceView = [[BSIntroduceView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight)];
    if(!AppDelegate.getAppDelegate.showGuide)
    {
        [self.introduceView loadIntroduceScrollViewWithBackgrounds:@[@"IntroduceBackground01", @"IntroduceBackground02"] titleArray:@[NSLocalizedString(@"一键换机", nil),NSLocalizedString(@"快速安全", nil)] detailsArray:@[NSLocalizedString(@"一键同步手机资料", nil),NSLocalizedString(@"一键选择一键发送", nil)]];
    }else
    {
        
        
        
        NSString *weekPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureWId"];

        if (!weekPrice) {
            weekPrice = @"18.00";
        }
       
        
        [self.introduceView loadIntroduceScrollViewWithBackgrounds:@[@"IntroduceBackground01", @"IntroduceBackground02",@"IntroduceBackground01"] titleArray:@[NSLocalizedString(@"一键换机", nil),NSLocalizedString(@"快速安全", nil),[NSString stringWithFormat:local(@"免费试用3天体验一键换机全部功能权限，您可以在试用期间无限制的使用所有功能，如果体验不满意可以随时取消，请在当前订阅周期到期前24小时以前前三天免费后按%@/周，自动更新，随时取消"),weekPrice]] detailsArray:@[NSLocalizedString(@"一键同步手机资料", nil),NSLocalizedString(@"一键选择一键发送", nil),@""]];
        
        
    }
    
    [self.view addSubview:self.introduceView];
    self.introduceView.delegate = self;
    
    self.agreementView = [[BSAgreementView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight)];
    [self.view addSubview:self.agreementView];
    self.agreementView.delegate = self;
    
    //加载启动页等待试图
    [self loadWaitingLaunchScreenView];
    [StoreIAPManager sharedManager].delegate =  self;
    
//    if(AppDelegate.getAppDelegate.showGuide==YES)
//    {
        
//    self.closeBtn = close;
//    self.closeBtn.hidden = YES;
//    }
}

/// 试图出现前的调用方法
/// @param animated 是否动画
- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    self.navigationController.navigationBarHidden = YES;
}

/// 加载启动页等待试图
- (void)loadWaitingLaunchScreenView{
    UIImageView *welcome = [[UIImageView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight)];
    [welcome setImage:[UIImage imageWithContentsOfFile:[[NSBundle mainBundle] pathForResource:@"LaunchScreen" ofType:@"jpg"]]];
    welcome.contentMode = UIViewContentModeScaleAspectFill;
    [self.view addSubview:welcome];
    [self.view bringSubviewToFront:welcome];

    BSWaitingAnimationView  *waitingView = [BSWaitingAnimationView loadCustomWithRadius:2.5 superView:welcome y:kScreenHeight-100 text:NSLocalizedString(@"Loading", nil)];
    [waitingView  startAnimation];
    
    
//    [BSUserDefaultManager initFirstOpenApp];
    //获取app打开状态,用于判断是否打开[接受协议页面]
    if ([BSUserDefaultManager getOpenAppState] == First_Open_App) {//第一次打开app
        self.isFirstOpenApp = YES;
    } else {//不是第一次打开app
        self.isFirstOpenApp = NO;
        [self.agreementView removeFromSuperview];
    }
    //===============================================================================
    //启动页动画 -> Duration结束动画时间  delay:启动页停滞时间
    welcome.alpha = 1.0;
    [UIView animateWithDuration:1.0f delay:8.0f options:UIViewAnimationOptionTransitionNone animations:^{
        welcome.alpha = 0.8;
    } completion:^(BOOL finished) {
        [waitingView stopAnimation];
        [welcome removeFromSuperview];
        if (!self.isFirstOpenApp) {
            //如果不是第一次打开app,就要马上进入引导介绍页面,并且开启引导介绍页面的动画
            [self.introduceView waveAnimationOfFirst];
        }
    }];
}

/// 接受协议页面结束后,切换到介绍引导试图页面
- (void)gotoIntroduceView{
    if (self.agreementView.superview != nil) {
        [self.agreementView startAlphaAnimationWithDuration:0.5 startAlpha:1.0 endAlpha:0.8 completion:^(BOOL finished) {
            [self.agreementView removeFromSuperview];
        }];
    }
    
    //介绍引导页的出现动画
    [self.introduceView startTransformAnimationWithDuration:0.5 startTransform:CGAffineTransformMakeScale(1.5, 1.0) endTransform:CGAffineTransformMakeScale(1.0, 1.0) completion:nil];
    //引导页出现时,展示第一个背景试图的波动动画
    [self.introduceView waveAnimationWithIndex:0];
    //[打开app状态值]标识为[已接受协议,但未进入主页]
    [BSUserDefaultManager setOpenAppState:Accept_Agreement];
}

/// 介绍引导页面结束后,切换到TabBar主控制器
- (void)gotoEnterViewController{
    if(AppDelegate.getAppDelegate.showGuide)
    {
        [[StoreIAPManager sharedManager]buyFeatureW];
        [SVProgressHUD show];
    }
    else
    {
        [self enterTabbar];
    }
    
}

- (void)productWPurchased
{
    [SVProgressHUD dismiss];
    [self enterTabbar];
}

- (void)cancelVipViewction{
    [SVProgressHUD dismiss];
    [self enterTabbar];
    BKdislog(@"已经取消订阅!");
}


- (void)failed{
    [SVProgressHUD dismiss];
}

- (void)enterTabbar
{
    if ([[NSUserDefaults standardUserDefaults] boolForKey:@"checkKeyifOn1"]) {
        UIViewController *enterViewCtl = [[BSEnterViewController01 alloc] init];
        
        [self.navigationController pushViewController:enterViewCtl animated:YES];
    }else
    {
        [self gotoMainViewController];
        [BSUserDefaultManager setOpenAppState:Goto_Main_Interface];
    }
}

-(void)gotoMainViewController{
    CGFloat transitAlpha = 0.75f;
    BSTabBarController *mainTabBarCtl = [[BSTabBarController alloc] init];
    mainTabBarCtl.childViewControllers.firstObject.view.alpha = transitAlpha;
    mainTabBarCtl.childViewControllers.firstObject.view.transform = CGAffineTransformMakeScale(1.5, 1.0);
    [UIView animateWithDuration:0.5f animations:^{
        self.view.alpha = transitAlpha;
    } completion:^(BOOL finished) {
        [AppDelegate getAppDelegate].window.rootViewController = mainTabBarCtl;
        [UIView animateWithDuration:0.5f animations:^{
            mainTabBarCtl.childViewControllers.firstObject.view.alpha = 1.0f;
            mainTabBarCtl.childViewControllers.firstObject.view.transform = CGAffineTransformMakeScale(1.0, 1.0);
        } completion:nil];
    }];
}

@end
