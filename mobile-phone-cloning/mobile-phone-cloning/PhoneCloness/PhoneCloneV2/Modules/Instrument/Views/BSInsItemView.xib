<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="BSInsItemView">
            <rect key="frame" x="0.0" y="0.0" width="100" height="120"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="kT5-fF-tQP">
                    <rect key="frame" x="22" y="20" width="56" height="56"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="56" id="MzE-Rx-Q9Z"/>
                        <constraint firstAttribute="width" constant="56" id="voA-bh-92W"/>
                    </constraints>
                </imageView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nnp-oE-Lu3">
                    <rect key="frame" x="32" y="86" width="36" height="17"/>
                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="kT5-fF-tQP" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="Ahs-KD-ymp"/>
                <constraint firstItem="kT5-fF-tQP" firstAttribute="top" secondItem="vUN-kp-3ea" secondAttribute="top" constant="20" id="Zhc-b8-vMM"/>
                <constraint firstItem="nnp-oE-Lu3" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="a8P-Nk-rmd"/>
                <constraint firstItem="nnp-oE-Lu3" firstAttribute="top" secondItem="kT5-fF-tQP" secondAttribute="bottom" constant="10" id="laY-6v-EIb"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <connections>
                <outlet property="iconImageView" destination="kT5-fF-tQP" id="rG1-aH-Qu4"/>
                <outlet property="titleLbl" destination="nnp-oE-Lu3" id="ALe-nD-qt1"/>
            </connections>
            <point key="canvasLocation" x="-360" y="206.89655172413794"/>
        </view>
    </objects>
</document>
