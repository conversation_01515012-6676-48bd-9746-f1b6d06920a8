//
//  BSInstDetailCell.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSInstDetailCell.h"

@interface BSInstDetailCell ()

@property (weak, nonatomic) IBOutlet UILabel *titleLbl;
@property (weak, nonatomic) IBOutlet UILabel *valueLbl;

@end

@implementation BSInstDetailCell

- (void)awakeFromNib {
    [super awakeFromNib];
  
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

}

- (void)setInfoDict:(NSDictionary *)infoDict {
    _infoDict = infoDict;
    
    self.titleLbl.text = infoDict[@"title"];
    self.valueLbl.text = infoDict[@"value"];
    
}

@end
