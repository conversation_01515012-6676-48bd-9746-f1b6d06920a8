//
//  BSClockDialView.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSClockDialView.h"

#define KAngleToradian(angle) (M_PI / 180.0 * (angle))

#define Calculate_radius ((self.bounds.size.height>self.bounds.size.width)?(self.bounds.size.width*0.5-self.lineWidth):(self.bounds.size.height*0.5-self.lineWidth))

#define kPCenter CGPointMake(self.center.x-self.frame.origin.x, self.center.y-self.frame.origin.y)


@interface BSClockDialView ()

@property(nonatomic,assign) CGFloat startAngle;
@property(nonatomic,assign) CGFloat endAngle;
@property(nonatomic,assign) CGFloat arcAngle;
@property(nonatomic,assign) CGFloat lineWidth;
@property(nonatomic,assign) CGFloat arcRadius;
@property(nonatomic,assign) CGFloat scaleValueRadius;

@property (nonatomic, strong) CAShapeLayer *progressLayer;
@property (nonatomic, strong) UIView *pointerView;

@property (nonatomic, strong) NSMutableArray *textArray;

@property (nonatomic, assign) NSInteger divide;

@end

@implementation BSClockDialView

- (void)drawArcWithStartAngle:(CGFloat)startAngle endAngle:(CGFloat)endAngle lineWidth:(CGFloat)lineWitdth fillColor:(UIColor*)filleColor strokeColor:(UIColor*)strokeColor {
    
    self.lineWidth = lineWitdth;
    self.startAngle = startAngle;
    self.endAngle = endAngle;
    self.arcAngle = endAngle - startAngle;
    self.arcRadius = Calculate_radius;
    self.scaleValueRadius = self.arcRadius - self.lineWidth;
    
    [self insertSubview:self.pointerView atIndex:0];
    
    UIBezierPath *arc = [UIBezierPath bezierPathWithArcCenter:kPCenter radius:self.arcRadius startAngle:startAngle endAngle:endAngle clockwise:YES];
    CAShapeLayer *shapeLayer = [CAShapeLayer layer];
    shapeLayer.lineWidth = lineWitdth;
    shapeLayer.fillColor = filleColor.CGColor;
    shapeLayer.strokeColor = strokeColor.CGColor;
    shapeLayer.path = arc.CGPath;
    shapeLayer.lineCap = kCALineCapButt;
    [self.layer insertSublayer:shapeLayer atIndex:0];
    
}

- (void)drawScaleValueWithDivide:(NSInteger)divide {
    
    CGFloat textAngel = self.arcAngle / divide;
    
    for (NSUInteger i = 0; i <= divide; i++) {
        
        CGPoint point = [self calculateTextPositonWithArcCenter:kPCenter Angle:-(self.endAngle - textAngel * i)];
        
        NSString *tickText = [NSString stringWithFormat:@"%ld", (divide - i) * 100 / divide];

        UILabel *text = [[UILabel alloc] initWithFrame:CGRectMake(point.x - 8, point.y - 7, 30, 14)];
        text.text = tickText;
        text.font = [UIFont systemFontOfSize:14.f weight:UIFontWeightMedium];
        text.textColor = [[UIColor blackColor] colorWithAlphaComponent:0.26];
        text.textAlignment = NSTextAlignmentLeft;
        [self.textArray addObject:text];
        [self addSubview:text];
    }
}

- (CGPoint)calculateTextPositonWithArcCenter:(CGPoint)center Angle:(CGFloat)angel {
    CGFloat x = (self.scaleValueRadius - 14) * cosf(angel);
    CGFloat y = (self.scaleValueRadius - 14) * sinf(angel);
    return CGPointMake(center.x + x, center.y - y);
}

- (void)refreshDashboard:(CGFloat)currentValue {

    if (currentValue > self.maxValue) {
        currentValue = self.maxValue;
    }
    if (currentValue <= self.minValue) {
        currentValue = self.minValue;
    }
    
    if (self.progressLayer) {
        [self.progressLayer removeFromSuperlayer];
    }
    
    CGFloat percent = (currentValue - self.minValue) / (self.maxValue - self.minValue);
    CGFloat currentAngle = self.startAngle + (fabs(self.endAngle - self.startAngle) * percent);
    
    [self dashboardDrawPercent:percent startAngle:self.startAngle endAngle:currentAngle currentValue:currentValue];
    
    if (currentValue <= 0) {
        self.pointerView.backgroundColor = [UIColor jk_colorWithHexString:@"#EBECEB"];
    }else {
        self.pointerView.backgroundColor = [UIColor jk_colorWithHexString:@"#FFBD73"];
    }
    
    self.pointerView.transform = CGAffineTransformMakeRotation(M_PI_4 +(M_PI*3/2 * percent));
}

- (void)dashboardDrawPercent:(CGFloat)percent startAngle:(CGFloat)startAngle endAngle:(CGFloat)endAngle  currentValue:(CGFloat)currentValue {
    
    UIBezierPath *progressPath  = [UIBezierPath bezierPathWithArcCenter:kPCenter radius:self.arcRadius startAngle:startAngle endAngle:endAngle clockwise:YES];
    CAShapeLayer *progressLayer = [CAShapeLayer layer];
    self.progressLayer = progressLayer;
    progressLayer.lineWidth = self.lineWidth;
    progressLayer.fillColor = [UIColor clearColor].CGColor;
    progressLayer.strokeColor = [UIColor jk_colorWithHexString:@"#6FB5F4"].CGColor;
    progressLayer.path = progressPath.CGPath;
    progressLayer.lineCap=kCALineCapButt;
    [self.layer addSublayer:progressLayer];
    
}

#pragma mark - Getter
- (NSMutableArray *)textArray {
    if (!_textArray) {
        _textArray = [NSMutableArray array];
    }
    return _textArray;
}

- (UIView *)pointerView {
    if (!_pointerView) {
        _pointerView = [[UIView alloc] initWithFrame:CGRectMake((self.bounds.size.width - 6) / 2.0, self.bounds.size.height / 2.0 - 70, 6, self.bounds.size.height / 2.0 - 6)];
        _pointerView.backgroundColor = [UIColor jk_colorWithHexString:@"#EBECEB"];
        _pointerView.layer.anchorPoint = CGPointMake(0.5, 0.0);
        _pointerView.transform = CGAffineTransformMakeRotation(M_PI_4);

    }
    return _pointerView;
}

@end
