<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="BSInstDetailCell" id="KGk-i7-Jjw" customClass="BSInstDetailCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="43.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="aqf-8E-jkF">
                        <rect key="frame" x="15" y="13" width="37.5" height="18"/>
                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7xI-T7-On0">
                        <rect key="frame" x="268" y="14" width="33" height="16"/>
                        <fontDescription key="fontDescription" type="system" pointSize="13"/>
                        <color key="textColor" red="0.53725490196078429" green="0.53333333333333333" blue="0.53333333333333333" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="UqW-0I-fbE">
                        <rect key="frame" x="15" y="42.5" width="305" height="1"/>
                        <color key="backgroundColor" red="0.96078431372549022" green="0.96078431372549022" blue="0.96078431372549022" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="tiw-X0-Qi3"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="aqf-8E-jkF" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="15" id="5dG-Ca-qxy"/>
                    <constraint firstItem="7xI-T7-On0" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="BJ8-8S-y2X"/>
                    <constraint firstAttribute="bottom" secondItem="UqW-0I-fbE" secondAttribute="bottom" id="M3P-kZ-f2Z"/>
                    <constraint firstAttribute="trailing" secondItem="UqW-0I-fbE" secondAttribute="trailing" id="Tpg-Ew-moh"/>
                    <constraint firstItem="aqf-8E-jkF" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="TuT-0Q-df1"/>
                    <constraint firstItem="UqW-0I-fbE" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="15" id="Y0g-4h-RCm"/>
                    <constraint firstAttribute="trailing" secondItem="7xI-T7-On0" secondAttribute="trailing" constant="19" id="dcR-uj-sKR"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="titleLbl" destination="aqf-8E-jkF" id="15U-cI-zWP"/>
                <outlet property="valueLbl" destination="7xI-T7-On0" id="u2x-oM-Ost"/>
            </connections>
            <point key="canvasLocation" x="34.782608695652179" y="34.821428571428569"/>
        </tableViewCell>
    </objects>
</document>
