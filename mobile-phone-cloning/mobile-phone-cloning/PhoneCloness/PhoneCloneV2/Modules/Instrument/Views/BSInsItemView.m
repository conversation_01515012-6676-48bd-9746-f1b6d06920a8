//
//  BSInsItemView.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSInsItemView.h"

@interface BSInsItemView ()

@property (weak, nonatomic) IBOutlet UIImageView *iconImageView;
@property (weak, nonatomic) IBOutlet UILabel *titleLbl;

@end

@implementation BSInsItemView

- (void)awakeFromNib {
    [super awakeFromNib];
    
    self.layer.backgroundColor = [UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:1.0].CGColor;
    self.layer.cornerRadius = 7;
    self.layer.shadowColor = [UIColor colorWithRed:141/255.0 green:140/255.0 blue:140/255.0 alpha:0.5].CGColor;
    self.layer.shadowOffset = CGSizeMake(0,1);
    self.layer.shadowOpacity = 1;
    self.layer.shadowRadius = 3;
}

+ (instancetype)getInsItemView {
    return [[[NSBundle mainBundle] loadNibNamed:@"BSInsItemView" owner:nil options:nil] lastObject];
}

- (void)setInfoDict:(NSDictionary *)infoDict {
    _infoDict = infoDict;
    
    self.iconImageView.image = [UIImage imageNamed:infoDict[@"icon"]];
    self.titleLbl.text = infoDict[@"title"];
    
}

@end
