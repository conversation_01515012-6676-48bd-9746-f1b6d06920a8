//
//  BSClockDialView.h
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <UIKit/UIKit.h>

@interface BSClockDialView : UIView

@property (nonatomic, assign) CGFloat minValue;
@property (nonatomic, assign) CGFloat maxValue;
@property (nonatomic, assign) CGFloat currentValue;

-(void)drawArcWithStartAngle:(CGFloat)startAngle endAngle:(CGFloat)endAngle lineWidth:(CGFloat)lineWitdth fillColor:(UIColor*)filleColor strokeColor:(UIColor*)strokeColor;

-(void)drawScaleValueWithDivide:(NSInteger)divide;

- (void)refreshDashboard:(CGFloat)currentValue;

@end

