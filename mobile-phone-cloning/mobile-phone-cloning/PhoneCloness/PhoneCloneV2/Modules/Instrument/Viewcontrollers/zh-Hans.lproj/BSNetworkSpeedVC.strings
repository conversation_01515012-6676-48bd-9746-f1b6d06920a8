
/* Class = "UILabel"; text = "延迟"; ObjectID = "6Dz-Ty-DF3"; */
"6Dz-Ty-DF3.text" = "延迟";

/* Class = "UILabel"; text = "下载速度"; ObjectID = "Jk9-ak-Xxz"; */
"Jk9-ak-Xxz.text" = "下载速度";

/* Class = "UILabel"; text = "- -Mbps"; ObjectID = "Lqs-2e-e0g"; */
"Lqs-2e-e0g.text" = "- -Mbps";

/* Class = "UILabel"; text = "- -ms"; ObjectID = "MAN-Vq-4KB"; */
"MAN-Vq-4KB.text" = "- -ms";

/* Class = "UILabel"; text = "0"; ObjectID = "NGj-G8-XvD"; */
"NGj-G8-XvD.text" = "0";

/* Class = "UILabel"; text = "KB/S"; ObjectID = "cgK-Vf-E33"; */
"cgK-Vf-E33.text" = "KB/S";

/* Class = "UILabel"; text = "上传速度"; ObjectID = "gc0-DF-Z7c"; */
"gc0-DF-Z7c.text" = "上传速度";

/* Class = "UILabel"; text = "- -ms"; ObjectID = "gzW-G2-pyQ"; */
"gzW-G2-pyQ.text" = "- -ms";

/* Class = "UILabel"; text = "- -Mbps"; ObjectID = "oBY-rn-aw7"; */
"oBY-rn-aw7.text" = "- -Mbps";

/* Class = "UIButton"; normalTitle = "开始测速"; ObjectID = "yct-tw-so4"; */
"yct-tw-so4.normalTitle" = "开始测速";

/* Class = "UILabel"; text = "JITTER"; ObjectID = "z7R-5e-Ycm"; */
"z7R-5e-Ycm.text" = "JITTER";
