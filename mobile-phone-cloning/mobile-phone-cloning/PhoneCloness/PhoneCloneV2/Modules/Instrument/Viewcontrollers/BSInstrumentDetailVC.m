//
//  BSInstrumentDetailVC.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSInstrumentDetailVC.h"

#import "BSInstDetailCell.h"

#import "BSDeviceInfo.h"
#import "UIDevice+Screen.h"

#import <sys/sysctl.h>
#import <mach/mach.h>
#import <CoreTelephony/CTCarrier.h>
#import <CoreTelephony/CTTelephonyNetworkInfo.h>
#import <LocalAuthentication/LocalAuthentication.h>

@interface BSInstrumentDetailVC () <UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSArray *dataArray;

@end

@implementation BSInstrumentDetailVC

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self.navigationController.navigationBar setBackgroundImage:[UIImage jk_imageWithColor:[UIColor jk_gradientFromColor:[UIColor jk_colorWithHexString:@"#3B9BF0"] toColor:[UIColor jk_colorWithHexString:@"#93C8F7"] withHeight:1]] forBarMetrics:UIBarMetricsDefault];
    
    self.navigationController.navigationBar.translucent = NO;
    [self.navigationController.navigationBar setShadowImage:[UIImage jk_imageWithColor:[UIColor whiteColor]]];
}

- (void)viewDidLoad {
    [super viewDidLoad];

    [self setupUI];
    
    [self initData];
}

- (void)setupUI {
    self.view.backgroundColor = [UIColor whiteColor];
    
    UITableView *tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight - kStatusBarAndNavgationBarHeight - kBottomSpaceHeight)];
    [tableView registerNib:[UINib nibWithNibName:@"BSInstDetailCell" bundle:[NSBundle mainBundle]] forCellReuseIdentifier:@"BSInstDetailCell"];
    tableView.backgroundColor = [UIColor whiteColor];
    tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    tableView.dataSource = self;
    tableView.delegate = self;
    tableView.rowHeight = 48.0;
    tableView.estimatedSectionHeaderHeight = 0.0;
    tableView.estimatedSectionFooterHeight = 0.0;
    tableView.tableFooterView = [UIView new];
    [self.view addSubview:tableView];
    self.tableView = tableView;
    
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArray.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    BSInstDetailCell *cell = [tableView dequeueReusableCellWithIdentifier:@"BSInstDetailCell" forIndexPath:indexPath];
    cell.infoDict = self.dataArray[indexPath.row];
    return cell;
}

#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

#pragma mark - Request
- (void)initData {
    
    switch (self.type) {
        case 0:
        {
            //电池
            [self initBatteryData];
        }
            break;
        case 1:
        {
            //网络
            [self initNetworkData];
        }
            break;
        case 2:
        {
            //设备信息
            [self initDeviceInfoData];
        }
            break;
        case 3:
        {
            //屏幕
            [self initScreenData];
        }
            break;
        case 4:
        {
            //CPU
            [self initCPUData];
        }
            break;
        case 5:
        {
            //内存
            [self initMemoryData];
        }
            break;
        case 6:
        {
            //磁盘
            [self initDiskData];
        }
            break;
            
            
        default:
            break;
    }
    
    [self.tableView reloadData];
    
}

//电池
- (void)initBatteryData {
    [UIDevice currentDevice].batteryMonitoringEnabled = YES;
    
    NSMutableArray *infoArray = [NSMutableArray array];
    //电池状态
    [infoArray addObject:@{@"title": NSLocalizedString(@"batteryStatus", nil), @"value": [BSDeviceInfo batteryState]}];
    //电池电量
    [infoArray addObject:@{@"title": NSLocalizedString(@"batteryLevel", nil), @"value": [BSDeviceInfo batteryQuantity]}];
    
    self.dataArray = infoArray.copy;
}

//网络
- (void)initNetworkData {
    
    NSString *operator = NSLocalizedString(@"networkNotRecognized", nil);
    
    NSString *code;
    
    CTTelephonyNetworkInfo *info = [[CTTelephonyNetworkInfo alloc] init];
    CTCarrier *carrier = [info subscriberCellularProvider];
    if (carrier != nil) {
        code = [carrier mobileNetworkCode];
        
        if ([code isEqualToString:@"00"] || [code isEqualToString:@"02"] || [code isEqualToString:@"07"]) {
            operator = NSLocalizedString(@"mobileOperators", nil);
        } else if ([code isEqualToString:@"01"] || [code isEqualToString:@"06"]) {
            operator = NSLocalizedString(@"unicomOperators", nil);
        } else if ([code isEqualToString:@"03"] || [code isEqualToString:@"05"]) {
            operator = NSLocalizedString(@"telecomOperators", nil);
        } else if ([code isEqualToString:@"20"]) {
            operator = NSLocalizedString(@"tietongOperator", nil);
        }
        
    }
    
    if (code.length == 0) {
        code = NSLocalizedString(@"networkNotRecognized", nil);
    }
    
    NSMutableArray *infoArray = [NSMutableArray array];
    //运营商
    [infoArray addObject:@{@"title": NSLocalizedString(@"networkOperator", nil), @"value": operator}];
    //移动国家码（MCC）
    [infoArray addObject:@{@"title": NSLocalizedString(@"mobileCountryCode", nil), @"value": code}];
    
    self.dataArray = infoArray.copy;
   
    
}

//设备信息
- (void)initDeviceInfoData {
    NSMutableArray *infoArray = [NSMutableArray array];
    //设备名称
    [infoArray addObject:@{@"title": NSLocalizedString(@"screenSize", nil), @"value": [BSDeviceInfo deviceName]}];
    //设备型号
    [infoArray addObject:@{@"title": NSLocalizedString(@"screenAspectRatio", nil), @"value": [BSDeviceInfo deviceModel]}];
    //系统版本号
    [infoArray addObject:@{@"title": NSLocalizedString(@"screenResolution", nil), @"value": [BSDeviceInfo deviceSystemVersion]}];
    
    BOOL isSupportTouchID = NO;
    BOOL isSupporFaceID = NO;
    
    NSError *error = nil;
    LAContext *context = [[LAContext alloc] init];
    [context canEvaluatePolicy:LAPolicyDeviceOwnerAuthenticationWithBiometrics error:&error];
    
    if (@available(iOS 11.0, *)) {
        if (context.biometryType == LABiometryTypeTouchID) {
            // 指纹
            if (error) {
                isSupportTouchID = YES;
            } else {
               isSupportTouchID = YES;
            }
        }else if (context.biometryType == LABiometryTypeFaceID) {
            // 面容
            if (error) {
                isSupporFaceID = YES;
            } else {
                isSupporFaceID = YES;
            }
        }
        
    }else {
        if (error) {
            if (error.code == LAErrorTouchIDNotEnrolled) {
                // 支持指纹但没有设置
                isSupportTouchID = YES;
            }
        } else {
           isSupportTouchID = YES;
        }
    }
    
    
    //TouchID
    [infoArray addObject:@{@"title": @"TouchID", @"value": isSupportTouchID ? NSLocalizedString(@"support", nil) : NSLocalizedString(@"unsupported", nil)}];
    
    //FaceID
    [infoArray addObject:@{@"title": @"FaceID", @"value": isSupporFaceID ? NSLocalizedString(@"support", nil) : NSLocalizedString(@"unsupported", nil)}];
    
    //3DTouch
    BOOL isSupport3DTouch = self.traitCollection.forceTouchCapability == UIForceTouchCapabilityAvailable;
    [infoArray addObject:@{@"title": @"3DTouch", @"value": isSupport3DTouch ? NSLocalizedString(@"support", nil) : NSLocalizedString(@"unsupported", nil)}];
    
    self.dataArray = infoArray.copy;
}

//屏幕
- (void)initScreenData {
    NSMutableArray *infoArray = [NSMutableArray array];
    //屏幕尺寸
    [infoArray addObject:@{@"title": NSLocalizedString(@"screenSize", nil), @"value": [UIDevice currentSize]}];
    //屏幕比例
    [infoArray addObject:@{@"title": NSLocalizedString(@"screenAspectRatio", nil), @"value": [UIDevice currentRatio]}];
    //分辨率
    [infoArray addObject:@{@"title": NSLocalizedString(@"screenResolution", nil), @"value": [UIDevice currentResolution]}];
    //屏幕亮度
    [infoArray addObject:@{@"title": NSLocalizedString(@"screenBrightness", nil), @"value": [NSString stringWithFormat:@"%.0f%%", [UIScreen mainScreen].brightness * 100]}];
    
    self.dataArray = infoArray.copy;
}

//CPU
- (void)initCPUData {
    NSMutableArray *infoArray = [NSMutableArray array];
    
    unsigned int ncup;
    size_t len = sizeof(ncup);
    sysctlbyname("hw.ncpu", &ncup, &len, NULL, 0);
    
    //CPU数目
    [infoArray addObject:@{@"title": NSLocalizedString(@"CPU总数目", nil), @"value": [NSString stringWithFormat:@"%d", ncup]}];
    //CPU频率
    [infoArray addObject:@{@"title": NSLocalizedString(@"CPU频率", nil), @"value": @"0"}];
    
    self.dataArray = infoArray.copy;
}

//内存
- (void)initMemoryData {
    NSMutableArray *infoArray = [NSMutableArray array];
    
    long long usedMemorySize = [BSDeviceInfo usedMemorySize];
    long long availableMemorySize = [BSDeviceInfo totalMemorySize] - usedMemorySize;
    
    //总容量
    [infoArray addObject:@{@"title": NSLocalizedString(@"totalCapacity", nil), @"value": [BSDeviceInfo fileSizeToString: usedMemorySize + availableMemorySize]}];
    //可用容量
    [infoArray addObject:@{@"title": NSLocalizedString(@"availableCapacity", nil), @"value": [BSDeviceInfo fileSizeToString: availableMemorySize]}];
    //已用容量
    [infoArray addObject:@{@"title": NSLocalizedString(@"usedCapacity", nil), @"value": [BSDeviceInfo fileSizeToString: usedMemorySize]}];
    
    self.dataArray = infoArray.copy;
}

//磁盘
- (void)initDiskData {
    
    NSMutableArray *infoArray = [NSMutableArray array];
    //总容量
    [infoArray addObject:@{@"title": NSLocalizedString(@"totalCapacity", nil), @"value": [BSDeviceInfo fileSizeToString: [BSDeviceInfo totalDiskSpaceSize]]}];
    //可用容量
    [infoArray addObject:@{@"title":NSLocalizedString(@"availableCapacity", nil), @"value": [BSDeviceInfo fileSizeToString: [BSDeviceInfo availableDiskSpaceSize]]}];
    //已用容量
    [infoArray addObject:@{@"title": NSLocalizedString(@"usedCapacity", nil), @"value": [BSDeviceInfo fileSizeToString: [BSDeviceInfo usedDiskSpaceSize]]}];
    
    self.dataArray = infoArray.copy;
    
}

@end
