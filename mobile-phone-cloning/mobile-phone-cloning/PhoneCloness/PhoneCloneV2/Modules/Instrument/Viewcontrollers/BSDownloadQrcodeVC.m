//
//  BSDownloadQrcodeVC.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSDownloadQrcodeVC.h"
#import "SGQRCodeGenerateManager.h"

@interface BSDownloadQrcodeVC ()

@property (weak, nonatomic) IBOutlet UIImageView *qrcodeImageView;

@end

@implementation BSDownloadQrcodeVC

- (void)viewDidLoad {
    [super viewDidLoad];
   
    [self setupUI];
}

- (void)setupUI {
    
    self.navigationItem.title = NSLocalizedString(@"下载一键换机", nil);
    
    UIImage *qrImage = [SGQRCodeGenerateManager generateWithDefaultQRCodeData:kDownloadAppUrl imageViewWidth:282];
    self.qrcodeImageView.image = qrImage;
    
}

@end
