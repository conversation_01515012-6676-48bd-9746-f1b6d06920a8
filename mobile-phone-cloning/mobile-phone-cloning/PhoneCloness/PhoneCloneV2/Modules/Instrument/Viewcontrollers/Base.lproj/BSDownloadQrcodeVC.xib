<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22154" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22130"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="BSDownloadQrcodeVC">
            <connections>
                <outlet property="qrcodeImageView" destination="OPT-od-tgL" id="icQ-X6-nbe"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="UM5-fm-xu2">
                    <rect key="frame" x="56.5" y="102" width="262" height="262"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="OPT-od-tgL">
                            <rect key="frame" x="0.0" y="0.0" width="262" height="262"/>
                        </imageView>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="logo" translatesAutoresizingMaskIntoConstraints="NO" id="uo7-rG-71p">
                            <rect key="frame" x="100.5" y="100.5" width="61" height="61"/>
                            <color key="backgroundColor" red="0.43529411759999997" green="0.70980392160000005" blue="0.95686274510000002" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="61" id="Bxa-5c-Lq2"/>
                                <constraint firstAttribute="height" constant="61" id="UiT-n0-VoC"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="7"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                            </userDefinedRuntimeAttributes>
                        </imageView>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstItem="uo7-rG-71p" firstAttribute="centerX" secondItem="UM5-fm-xu2" secondAttribute="centerX" id="DtH-FQ-g1w"/>
                        <constraint firstAttribute="height" constant="262" id="Gz1-Nn-lEk"/>
                        <constraint firstItem="uo7-rG-71p" firstAttribute="centerY" secondItem="UM5-fm-xu2" secondAttribute="centerY" id="L2u-Dc-sfH"/>
                        <constraint firstAttribute="bottom" secondItem="OPT-od-tgL" secondAttribute="bottom" id="QWk-sn-fD6"/>
                        <constraint firstAttribute="trailing" secondItem="OPT-od-tgL" secondAttribute="trailing" id="Stg-Aw-Joq"/>
                        <constraint firstAttribute="width" constant="262" id="Tcb-zW-zVa"/>
                        <constraint firstItem="OPT-od-tgL" firstAttribute="top" secondItem="UM5-fm-xu2" secondAttribute="top" id="Vdj-t5-566"/>
                        <constraint firstItem="OPT-od-tgL" firstAttribute="leading" secondItem="UM5-fm-xu2" secondAttribute="leading" id="cMC-t4-OZC"/>
                    </constraints>
                </view>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="扫描二维码安装一键换机" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="qdu-Vx-fqo">
                    <rect key="frame" x="103.5" y="394" width="168.5" height="18"/>
                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="或在应用商店搜索" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rga-JG-6ni">
                    <rect key="frame" x="126.5" y="420" width="122.5" height="18"/>
                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <viewLayoutGuide key="safeArea" id="Q5M-cg-NOt"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="UM5-fm-xu2" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="64X-MS-l55"/>
                <constraint firstItem="qdu-Vx-fqo" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="LcW-nq-Xzo"/>
                <constraint firstItem="rga-JG-6ni" firstAttribute="top" secondItem="qdu-Vx-fqo" secondAttribute="bottom" constant="8" id="aHT-JT-H4H"/>
                <constraint firstItem="qdu-Vx-fqo" firstAttribute="top" secondItem="UM5-fm-xu2" secondAttribute="bottom" constant="30" id="b8Y-XN-2VX"/>
                <constraint firstItem="rga-JG-6ni" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="dgM-io-Z8W"/>
                <constraint firstItem="UM5-fm-xu2" firstAttribute="top" relation="greaterThanOrEqual" secondItem="i5M-Pr-FkT" secondAttribute="top" constant="102" id="kHp-fg-Eu9"/>
                <constraint firstItem="UM5-fm-xu2" firstAttribute="top" secondItem="Q5M-cg-NOt" secondAttribute="top" priority="750" constant="58" id="xdd-HB-f1c"/>
            </constraints>
            <simulatedNavigationBarMetrics key="simulatedTopBarMetrics"/>
            <point key="canvasLocation" x="-111" y="103"/>
        </view>
    </objects>
    <resources>
        <image name="logo" width="90" height="90"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
