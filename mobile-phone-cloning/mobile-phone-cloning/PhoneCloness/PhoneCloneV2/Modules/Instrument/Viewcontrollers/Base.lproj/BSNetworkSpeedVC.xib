<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="16096" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="BSNetworkSpeedVC">
            <connections>
                <outlet property="clockDialView" destination="41b-B6-4kQ" id="8b1-Yk-Bvg"/>
                <outlet property="delayLbl" destination="MAN-Vq-4KB" id="NwT-Dq-pji"/>
                <outlet property="downloadLbl" destination="oBY-rn-aw7" id="NHv-f4-9H3"/>
                <outlet property="jitterLbl" destination="gzW-G2-pyQ" id="TEt-ql-lEP"/>
                <outlet property="startBtn" destination="yct-tw-so4" id="CSU-Na-6T4"/>
                <outlet property="unitLbl" destination="cgK-Vf-E33" id="M3B-56-Ayy"/>
                <outlet property="uploadLbl" destination="Lqs-2e-e0g" id="U0i-Nh-8zf"/>
                <outlet property="valueLbl" destination="NGj-G8-XvD" id="R9l-m3-Kwf"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="CRw-6M-XiU">
                    <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="41b-B6-4kQ" customClass="BSClockDialView">
                            <rect key="frame" x="47.5" y="84" width="280" height="280"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="aQG-aL-Uns">
                                    <rect key="frame" x="66" y="66" width="148" height="148"/>
                                    <subviews>
                                        <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ns_c1" translatesAutoresizingMaskIntoConstraints="NO" id="9Zq-kE-ZKh">
                                            <rect key="frame" x="0.0" y="0.0" width="148" height="148"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </imageView>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NGj-G8-XvD">
                                            <rect key="frame" x="64.5" y="48.5" width="19" height="31.5"/>
                                            <fontDescription key="fontDescription" type="system" weight="heavy" pointSize="26"/>
                                            <nil key="textColor"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="KB/S" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cgK-Vf-E33">
                                            <rect key="frame" x="56" y="88" width="36.5" height="19.5"/>
                                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                                            <nil key="textColor"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="148" id="923-o8-Rm6"/>
                                        <constraint firstItem="NGj-G8-XvD" firstAttribute="centerY" secondItem="aQG-aL-Uns" secondAttribute="centerY" constant="-10" id="EiX-C5-Wvc"/>
                                        <constraint firstItem="9Zq-kE-ZKh" firstAttribute="leading" secondItem="aQG-aL-Uns" secondAttribute="leading" id="Jdh-eb-qK8"/>
                                        <constraint firstAttribute="trailing" secondItem="9Zq-kE-ZKh" secondAttribute="trailing" id="MhW-lt-LYw"/>
                                        <constraint firstAttribute="height" constant="148" id="ZQ7-0a-Gcb"/>
                                        <constraint firstItem="NGj-G8-XvD" firstAttribute="centerX" secondItem="aQG-aL-Uns" secondAttribute="centerX" id="aVT-dC-s7U"/>
                                        <constraint firstAttribute="bottom" secondItem="9Zq-kE-ZKh" secondAttribute="bottom" id="arx-lN-VUe"/>
                                        <constraint firstItem="cgK-Vf-E33" firstAttribute="centerX" secondItem="aQG-aL-Uns" secondAttribute="centerX" id="tPH-u7-Dts"/>
                                        <constraint firstItem="cgK-Vf-E33" firstAttribute="top" secondItem="NGj-G8-XvD" secondAttribute="bottom" constant="8" id="ut3-q9-AHc"/>
                                        <constraint firstItem="9Zq-kE-ZKh" firstAttribute="top" secondItem="aQG-aL-Uns" secondAttribute="top" id="vqT-nr-cBY"/>
                                    </constraints>
                                </view>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="280" id="1oc-JV-JaL"/>
                                <constraint firstItem="aQG-aL-Uns" firstAttribute="centerY" secondItem="41b-B6-4kQ" secondAttribute="centerY" id="H1c-Za-cia"/>
                                <constraint firstAttribute="height" constant="280" id="U4f-QC-Ya1"/>
                                <constraint firstItem="aQG-aL-Uns" firstAttribute="centerX" secondItem="41b-B6-4kQ" secondAttribute="centerX" id="vfS-3X-s8A"/>
                            </constraints>
                        </view>
                        <label opaque="NO" userInteractionEnabled="NO" alpha="0.26000000000000001" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="- -ms" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="MAN-Vq-4KB">
                            <rect key="frame" x="47.5" y="389" width="49.5" height="24"/>
                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="20"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="延迟" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6Dz-Ty-DF3">
                            <rect key="frame" x="47.5" y="425" width="29" height="17"/>
                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                            <color key="textColor" red="0.53725490196078429" green="0.53333333333333333" blue="0.53333333333333333" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="下载速度" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Jk9-ak-Xxz">
                            <rect key="frame" x="215" y="425" width="57.5" height="17"/>
                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                            <color key="textColor" red="0.53725490200000003" green="0.53333333329999999" blue="0.53333333329999999" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" alpha="0.25999999046325684" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="- -Mbps" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="oBY-rn-aw7">
                            <rect key="frame" x="215" y="389" width="73.5" height="24"/>
                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="20"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" alpha="0.25999999046325684" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="- -ms" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gzW-G2-pyQ">
                            <rect key="frame" x="47.5" y="466" width="49.5" height="24"/>
                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="20"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="JITTER" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="z7R-5e-Ycm">
                            <rect key="frame" x="47.5" y="502" width="47" height="17"/>
                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                            <color key="textColor" red="0.53725490200000003" green="0.53333333329999999" blue="0.53333333329999999" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="yct-tw-so4">
                            <rect key="frame" x="110.5" y="593" width="154" height="36"/>
                            <color key="backgroundColor" red="0.43529411764705883" green="0.70980392156862748" blue="0.95686274509803915" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="154" id="FAw-8f-Jem"/>
                                <constraint firstAttribute="height" constant="36" id="lAI-jR-QZV"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                            <state key="normal" title="开始测速">
                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </state>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="18"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                            </userDefinedRuntimeAttributes>
                            <connections>
                                <action selector="startTestSpeed:" destination="-1" eventType="touchUpInside" id="Aqg-wk-etX"/>
                            </connections>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="上传速度" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gc0-DF-Z7c">
                            <rect key="frame" x="215" y="502" width="57.5" height="17"/>
                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                            <color key="textColor" red="0.53725490200000003" green="0.53333333329999999" blue="0.53333333329999999" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" alpha="0.25999999046325684" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="- -Mbps" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Lqs-2e-e0g">
                            <rect key="frame" x="215" y="466" width="73.5" height="24"/>
                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="20"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor" cocoaTouchSystemColor="whiteColor"/>
                    <constraints>
                        <constraint firstItem="gc0-DF-Z7c" firstAttribute="centerY" secondItem="z7R-5e-Ycm" secondAttribute="centerY" id="0uO-5F-21q"/>
                        <constraint firstAttribute="bottom" secondItem="yct-tw-so4" secondAttribute="bottom" constant="38" id="Cs6-Dc-9eN"/>
                        <constraint firstItem="Jk9-ak-Xxz" firstAttribute="centerY" secondItem="6Dz-Ty-DF3" secondAttribute="centerY" id="D8h-OV-K7E"/>
                        <constraint firstItem="MAN-Vq-4KB" firstAttribute="top" secondItem="41b-B6-4kQ" secondAttribute="bottom" constant="25" id="Diy-zP-aqy"/>
                        <constraint firstItem="gzW-G2-pyQ" firstAttribute="top" secondItem="6Dz-Ty-DF3" secondAttribute="bottom" constant="24" id="EPQ-L8-dQl"/>
                        <constraint firstItem="z7R-5e-Ycm" firstAttribute="top" secondItem="gzW-G2-pyQ" secondAttribute="bottom" constant="12" id="FBt-rp-czb"/>
                        <constraint firstItem="oBY-rn-aw7" firstAttribute="centerY" secondItem="MAN-Vq-4KB" secondAttribute="centerY" id="FX4-xa-PKG"/>
                        <constraint firstItem="oBY-rn-aw7" firstAttribute="leading" secondItem="MAN-Vq-4KB" secondAttribute="trailing" constant="118" id="KsA-l8-ZdH"/>
                        <constraint firstItem="6Dz-Ty-DF3" firstAttribute="top" secondItem="MAN-Vq-4KB" secondAttribute="bottom" constant="12" id="L8i-SV-Zeu"/>
                        <constraint firstItem="41b-B6-4kQ" firstAttribute="top" secondItem="CRw-6M-XiU" secondAttribute="top" constant="84" id="QcT-rW-c9s"/>
                        <constraint firstItem="gc0-DF-Z7c" firstAttribute="leading" secondItem="Lqs-2e-e0g" secondAttribute="leading" id="Vp3-AJ-cVn"/>
                        <constraint firstItem="gzW-G2-pyQ" firstAttribute="leading" secondItem="MAN-Vq-4KB" secondAttribute="leading" id="Xdu-7b-5cf"/>
                        <constraint firstItem="41b-B6-4kQ" firstAttribute="centerX" secondItem="CRw-6M-XiU" secondAttribute="centerX" id="bak-Wd-Hga"/>
                        <constraint firstItem="Lqs-2e-e0g" firstAttribute="centerY" secondItem="gzW-G2-pyQ" secondAttribute="centerY" id="hDL-Bz-eUm"/>
                        <constraint firstItem="Jk9-ak-Xxz" firstAttribute="leading" secondItem="oBY-rn-aw7" secondAttribute="leading" id="iLQ-5W-1bQ"/>
                        <constraint firstItem="MAN-Vq-4KB" firstAttribute="leading" secondItem="41b-B6-4kQ" secondAttribute="leading" id="iyL-Vv-E89"/>
                        <constraint firstItem="z7R-5e-Ycm" firstAttribute="leading" secondItem="gzW-G2-pyQ" secondAttribute="leading" id="sLi-Qc-nWF"/>
                        <constraint firstItem="6Dz-Ty-DF3" firstAttribute="leading" secondItem="MAN-Vq-4KB" secondAttribute="leading" id="uId-Ks-srl"/>
                        <constraint firstItem="yct-tw-so4" firstAttribute="centerX" secondItem="CRw-6M-XiU" secondAttribute="centerX" id="uot-Sx-20h"/>
                        <constraint firstItem="Lqs-2e-e0g" firstAttribute="leading" secondItem="oBY-rn-aw7" secondAttribute="leading" id="y13-mb-X1S"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" systemColor="systemBackgroundColor" cocoaTouchSystemColor="whiteColor"/>
            <constraints>
                <constraint firstItem="Q5M-cg-NOt" firstAttribute="trailing" secondItem="CRw-6M-XiU" secondAttribute="trailing" id="9YP-UH-Lmg"/>
                <constraint firstItem="CRw-6M-XiU" firstAttribute="leading" secondItem="Q5M-cg-NOt" secondAttribute="leading" id="VcL-RN-e4q"/>
                <constraint firstItem="Q5M-cg-NOt" firstAttribute="bottom" secondItem="CRw-6M-XiU" secondAttribute="bottom" id="eK3-lY-Ybx"/>
                <constraint firstItem="CRw-6M-XiU" firstAttribute="top" secondItem="Q5M-cg-NOt" secondAttribute="top" id="mKW-Lc-efV"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="Q5M-cg-NOt"/>
            <point key="canvasLocation" x="82" y="97"/>
        </view>
    </objects>
    <resources>
        <image name="ns_c1" width="459" height="459"/>
    </resources>
</document>
