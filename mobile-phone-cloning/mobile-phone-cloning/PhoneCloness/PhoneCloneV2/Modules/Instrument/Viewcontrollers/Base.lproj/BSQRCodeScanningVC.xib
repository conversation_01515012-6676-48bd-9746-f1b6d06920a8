<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22154" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="ipad12_9rounded" orientation="portrait" layout="fullscreen" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22130"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="BSQRCodeScanningVC">
            <connections>
                <outlet property="funcView" destination="BJe-ML-PL7" id="Sbr-eQ-2XM"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="1024" height="1366"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="请在安卓手机的【一键换机】" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="C4M-1J-WE4">
                    <rect key="frame" x="418" y="979" width="188" height="18"/>
                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="点击接收生成" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="chs-eL-n9S">
                    <rect key="frame" x="466" y="1003" width="92" height="18"/>
                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                </label>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BJe-ML-PL7" customClass="BSView">
                    <rect key="frame" x="387.5" y="1074" width="249" height="46"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="另一台手机未安装一键换机" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="JlU-ap-QKO">
                            <rect key="frame" x="27" y="14.5" width="171" height="17"/>
                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="more_btn_icon1" translatesAutoresizingMaskIntoConstraints="NO" id="JRz-qx-ouw">
                            <rect key="frame" x="206" y="13" width="20" height="20"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="20" id="7nL-ZT-N2f"/>
                                <constraint firstAttribute="height" constant="20" id="d51-ED-xyv"/>
                            </constraints>
                        </imageView>
                    </subviews>
                    <color key="backgroundColor" red="0.43529411764705883" green="0.70980392156862748" blue="0.95686274509803915" alpha="1" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstItem="JlU-ap-QKO" firstAttribute="centerX" secondItem="BJe-ML-PL7" secondAttribute="centerX" constant="-12" id="4DC-kY-rh4"/>
                        <constraint firstItem="JlU-ap-QKO" firstAttribute="centerY" secondItem="BJe-ML-PL7" secondAttribute="centerY" id="SKB-Hz-ewH"/>
                        <constraint firstAttribute="height" constant="46" id="VT8-bF-Zde"/>
                        <constraint firstAttribute="width" constant="249" id="dTQ-3W-yVM"/>
                        <constraint firstItem="JRz-qx-ouw" firstAttribute="centerY" secondItem="BJe-ML-PL7" secondAttribute="centerY" id="eQX-y7-AeR"/>
                        <constraint firstItem="JRz-qx-ouw" firstAttribute="leading" secondItem="JlU-ap-QKO" secondAttribute="trailing" constant="8" id="nqV-Ud-wCJ"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="23"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                    </userDefinedRuntimeAttributes>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="blm-Q4-T6t">
                    <rect key="frame" x="0.0" y="24" width="1024" height="44"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="B37-De-o85">
                            <rect key="frame" x="10" y="12" width="20" height="20"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="20" id="dXQ-bs-sG7"/>
                                <constraint firstAttribute="width" constant="20" id="mhp-4x-Enj"/>
                            </constraints>
                            <state key="normal" image="back_btn"/>
                            <connections>
                                <action selector="backAction:" destination="-1" eventType="touchUpInside" id="Utd-8V-USJ"/>
                            </connections>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="连接安卓手机" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="E8s-Ze-2RH">
                            <rect key="frame" x="466" y="13" width="92" height="18"/>
                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Ah2-jY-Hkk" customClass="BSButton">
                            <rect key="frame" x="987" y="12" width="20" height="20"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="20" id="Dsr-Qm-und"/>
                                <constraint firstAttribute="height" constant="20" id="QDr-k1-PGP"/>
                            </constraints>
                            <state key="normal" image="qrcode_w_icon"/>
                            <connections>
                                <action selector="qrcodeBtnAction:" destination="-1" eventType="touchUpInside" id="cd4-e2-joT"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="B37-De-o85" firstAttribute="centerY" secondItem="blm-Q4-T6t" secondAttribute="centerY" id="2B8-qV-LXQ"/>
                        <constraint firstItem="Ah2-jY-Hkk" firstAttribute="centerY" secondItem="blm-Q4-T6t" secondAttribute="centerY" id="2lN-fv-3nQ"/>
                        <constraint firstItem="E8s-Ze-2RH" firstAttribute="centerY" secondItem="blm-Q4-T6t" secondAttribute="centerY" id="Ep3-ft-wdA"/>
                        <constraint firstItem="B37-De-o85" firstAttribute="leading" secondItem="blm-Q4-T6t" secondAttribute="leading" constant="10" id="Ia9-xd-W8h"/>
                        <constraint firstAttribute="trailing" secondItem="Ah2-jY-Hkk" secondAttribute="trailing" constant="17" id="Vhj-LA-jLz"/>
                        <constraint firstItem="E8s-Ze-2RH" firstAttribute="centerX" secondItem="blm-Q4-T6t" secondAttribute="centerX" id="giO-jz-Iv9"/>
                        <constraint firstAttribute="height" constant="44" id="xbZ-GY-7XD"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="Q5M-cg-NOt"/>
            <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="Q5M-cg-NOt" firstAttribute="trailing" secondItem="blm-Q4-T6t" secondAttribute="trailing" id="5L3-cW-hIf"/>
                <constraint firstItem="chs-eL-n9S" firstAttribute="top" secondItem="C4M-1J-WE4" secondAttribute="bottom" constant="6" id="FVT-db-sSi"/>
                <constraint firstItem="chs-eL-n9S" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="IpO-io-Jc1"/>
                <constraint firstItem="blm-Q4-T6t" firstAttribute="top" relation="greaterThanOrEqual" secondItem="i5M-Pr-FkT" secondAttribute="top" constant="20" id="Oga-Fh-pEb"/>
                <constraint firstItem="BJe-ML-PL7" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="UaD-tX-4AK"/>
                <constraint firstItem="blm-Q4-T6t" firstAttribute="top" secondItem="Q5M-cg-NOt" secondAttribute="top" priority="750" id="Zrj-b9-Y2V"/>
                <constraint firstItem="C4M-1J-WE4" firstAttribute="bottom" secondItem="i5M-Pr-FkT" secondAttribute="bottom" multiplier="0.73" id="bU6-dk-WHh"/>
                <constraint firstItem="BJe-ML-PL7" firstAttribute="top" secondItem="chs-eL-n9S" secondAttribute="bottom" constant="53" id="gh2-Ew-K96"/>
                <constraint firstItem="C4M-1J-WE4" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="kV0-tD-cH6"/>
                <constraint firstItem="blm-Q4-T6t" firstAttribute="leading" secondItem="Q5M-cg-NOt" secondAttribute="leading" id="pg5-hG-3a6"/>
            </constraints>
            <point key="canvasLocation" x="-482" y="173"/>
        </view>
    </objects>
    <resources>
        <image name="back_btn" width="20" height="20"/>
        <image name="more_btn_icon1" width="20" height="20"/>
        <image name="qrcode_w_icon" width="60" height="60"/>
    </resources>
</document>
