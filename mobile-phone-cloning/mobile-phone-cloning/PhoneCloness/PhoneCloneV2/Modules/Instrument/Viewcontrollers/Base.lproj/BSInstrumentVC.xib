<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="16096" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="BSInstrumentVC">
            <connections>
                <outlet property="contentView" destination="hL6-83-JVP" id="uF7-qI-fXv"/>
                <outlet property="view" destination="iN0-l3-epB" id="Usd-xx-KW1"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="手机设备信息" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2wu-UP-xVp">
                    <rect key="frame" x="18" y="45" width="110.5" height="21"/>
                    <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="18"/>
                    <nil key="highlightedColor"/>
                </label>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hL6-83-JVP">
                    <rect key="frame" x="0.0" y="91" width="375" height="576"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor" cocoaTouchSystemColor="whiteColor"/>
                </view>
            </subviews>
            <color key="backgroundColor" systemColor="systemBackgroundColor" cocoaTouchSystemColor="whiteColor"/>
            <constraints>
                <constraint firstItem="hL6-83-JVP" firstAttribute="leading" secondItem="vUN-kp-3ea" secondAttribute="leading" id="7Yw-G7-FUi"/>
                <constraint firstItem="vUN-kp-3ea" firstAttribute="bottom" secondItem="hL6-83-JVP" secondAttribute="bottom" id="MVp-S2-9Yn"/>
                <constraint firstItem="2wu-UP-xVp" firstAttribute="top" relation="greaterThanOrEqual" secondItem="iN0-l3-epB" secondAttribute="top" constant="45" id="PXS-dc-uWb"/>
                <constraint firstItem="hL6-83-JVP" firstAttribute="top" secondItem="2wu-UP-xVp" secondAttribute="bottom" constant="25" id="R9G-dh-UM8"/>
                <constraint firstItem="2wu-UP-xVp" firstAttribute="leading" secondItem="vUN-kp-3ea" secondAttribute="leading" constant="18" id="W42-me-lUE"/>
                <constraint firstItem="vUN-kp-3ea" firstAttribute="trailing" secondItem="hL6-83-JVP" secondAttribute="trailing" id="dYs-Pn-oQ1"/>
                <constraint firstItem="vUN-kp-3ea" firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="2wu-UP-xVp" secondAttribute="trailing" symbolic="YES" id="lwx-U9-meA"/>
                <constraint firstItem="2wu-UP-xVp" firstAttribute="top" secondItem="vUN-kp-3ea" secondAttribute="top" priority="750" constant="25" id="mrL-IK-uAm"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <point key="canvasLocation" x="132" y="117"/>
        </view>
    </objects>
</document>
