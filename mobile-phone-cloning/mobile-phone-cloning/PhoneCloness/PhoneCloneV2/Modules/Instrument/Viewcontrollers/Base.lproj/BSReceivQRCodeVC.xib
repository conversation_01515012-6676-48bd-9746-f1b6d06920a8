<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22154" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22130"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="BSReceivQRCodeVC">
            <connections>
                <outlet property="qrcodeImageView" destination="kQM-bx-h4K" id="aye-0G-xYr"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="b5w-CH-1kx">
                    <rect key="frame" x="56.5" y="122" width="262" height="262"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="kQM-bx-h4K">
                            <rect key="frame" x="0.0" y="0.0" width="262" height="262"/>
                        </imageView>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="logo" translatesAutoresizingMaskIntoConstraints="NO" id="vHO-ca-Na9">
                            <rect key="frame" x="100.5" y="100.5" width="61" height="61"/>
                            <color key="backgroundColor" red="0.43529411764705883" green="0.70980392156862748" blue="0.95686274509803915" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="61" id="401-xe-fuX"/>
                                <constraint firstAttribute="width" constant="61" id="74t-xH-mqF"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="7"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                            </userDefinedRuntimeAttributes>
                        </imageView>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="262" id="Aye-YM-YMV"/>
                        <constraint firstItem="vHO-ca-Na9" firstAttribute="centerX" secondItem="b5w-CH-1kx" secondAttribute="centerX" id="Byd-8N-nt9"/>
                        <constraint firstAttribute="bottom" secondItem="kQM-bx-h4K" secondAttribute="bottom" id="Qmn-jU-skg"/>
                        <constraint firstItem="kQM-bx-h4K" firstAttribute="top" secondItem="b5w-CH-1kx" secondAttribute="top" id="X0m-BC-7Hf"/>
                        <constraint firstItem="kQM-bx-h4K" firstAttribute="leading" secondItem="b5w-CH-1kx" secondAttribute="leading" id="hdC-bh-lte"/>
                        <constraint firstAttribute="trailing" secondItem="kQM-bx-h4K" secondAttribute="trailing" id="jGo-k4-wvx"/>
                        <constraint firstItem="vHO-ca-Na9" firstAttribute="centerY" secondItem="b5w-CH-1kx" secondAttribute="centerY" id="tiv-U9-L4a"/>
                        <constraint firstAttribute="width" constant="262" id="wUh-d8-yJU"/>
                    </constraints>
                </view>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="请在另一台安卓手机打开一键换机" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="CKQ-c6-jow">
                    <rect key="frame" x="72.5" y="414" width="230" height="18"/>
                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="扫描二维码" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="VXl-SB-Uw5">
                    <rect key="frame" x="149.5" y="440" width="76.5" height="18"/>
                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <viewLayoutGuide key="safeArea" id="Q5M-cg-NOt"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="b5w-CH-1kx" firstAttribute="top" relation="greaterThanOrEqual" secondItem="i5M-Pr-FkT" secondAttribute="top" constant="102" id="6Ek-kN-mfc"/>
                <constraint firstItem="CKQ-c6-jow" firstAttribute="top" secondItem="b5w-CH-1kx" secondAttribute="bottom" constant="30" id="Tym-1l-ESO"/>
                <constraint firstItem="VXl-SB-Uw5" firstAttribute="top" secondItem="CKQ-c6-jow" secondAttribute="bottom" constant="8" id="U9O-B4-Lms"/>
                <constraint firstItem="b5w-CH-1kx" firstAttribute="top" secondItem="Q5M-cg-NOt" secondAttribute="top" priority="750" constant="58" id="aTV-2y-umA"/>
                <constraint firstItem="b5w-CH-1kx" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="c3l-zK-DnF"/>
                <constraint firstItem="CKQ-c6-jow" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="d5g-HI-0g5"/>
                <constraint firstItem="VXl-SB-Uw5" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="zKE-yc-efj"/>
            </constraints>
            <simulatedNavigationBarMetrics key="simulatedTopBarMetrics"/>
            <point key="canvasLocation" x="-191" y="66"/>
        </view>
    </objects>
    <resources>
        <image name="logo" width="90" height="90"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
