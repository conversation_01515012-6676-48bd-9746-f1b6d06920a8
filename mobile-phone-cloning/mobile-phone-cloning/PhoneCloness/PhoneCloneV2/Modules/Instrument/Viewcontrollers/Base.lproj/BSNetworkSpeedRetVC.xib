<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="16096" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="ipad12_9" orientation="portrait" layout="fullscreen" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="BSNetworkSpeedRetVC">
            <connections>
                <outlet property="bgView" destination="buz-Zw-b9L" id="GqU-mI-K8K"/>
                <outlet property="delayLbl" destination="XfP-UF-2Dd" id="H3y-c1-hxL"/>
                <outlet property="downloadLbl" destination="4vx-Tm-qLX" id="Ydc-n4-5fW"/>
                <outlet property="jitterLbl" destination="y3G-d0-Edb" id="Wnf-mb-tuV"/>
                <outlet property="restartBtn" destination="Qlb-PZ-E68" id="ZB9-xq-hiP"/>
                <outlet property="speedLbl" destination="1jJ-M4-Vxn" id="eev-h0-vhi"/>
                <outlet property="tipsLbl" destination="TiI-We-Eiu" id="8bf-Ny-aK6"/>
                <outlet property="uploadLbl" destination="jf9-pc-5uc" id="THf-2D-NhH"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="1024" height="1366"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="buz-Zw-b9L">
                    <rect key="frame" x="0.0" y="0.0" width="1024" height="192"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor" cocoaTouchSystemColor="whiteColor"/>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="gOW-sz-9v2">
                    <rect key="frame" x="0.0" y="64" width="1024" height="128"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0.00Mbps" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1jJ-M4-Vxn">
                            <rect key="frame" x="419" y="19" width="186" height="48"/>
                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="40"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="高于100M的宽带" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TiI-We-Eiu">
                            <rect key="frame" x="454" y="75" width="116" height="18"/>
                            <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="128" id="2RX-c7-xfq"/>
                        <constraint firstAttribute="bottom" secondItem="TiI-We-Eiu" secondAttribute="bottom" constant="35" id="7I0-Ym-1st"/>
                        <constraint firstItem="1jJ-M4-Vxn" firstAttribute="centerX" secondItem="gOW-sz-9v2" secondAttribute="centerX" id="I5m-3h-8f6"/>
                        <constraint firstItem="TiI-We-Eiu" firstAttribute="top" secondItem="1jJ-M4-Vxn" secondAttribute="bottom" constant="8" id="dWB-Jf-F6K"/>
                        <constraint firstItem="TiI-We-Eiu" firstAttribute="centerX" secondItem="gOW-sz-9v2" secondAttribute="centerX" id="rhs-3h-hPG"/>
                    </constraints>
                </view>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0.00Mbps" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4vx-Tm-qLX">
                    <rect key="frame" x="538" y="232" width="94.5" height="24"/>
                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="20"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0/ms" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XfP-UF-2Dd">
                    <rect key="frame" x="360" y="232" width="47" height="24"/>
                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="20"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="下载速度" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2pi-Sy-6Va">
                    <rect key="frame" x="538" y="262" width="65.5" height="19.5"/>
                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                    <color key="textColor" red="0.53725490196078429" green="0.53333333333333333" blue="0.53333333333333333" alpha="1" colorSpace="calibratedRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="延迟" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="akp-16-hVI">
                    <rect key="frame" x="360" y="262" width="33" height="19.5"/>
                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                    <color key="textColor" red="0.53725490200000003" green="0.53333333329999999" blue="0.53333333329999999" alpha="1" colorSpace="calibratedRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0/ms" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="y3G-d0-Edb">
                    <rect key="frame" x="360" y="305.5" width="47" height="24"/>
                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="20"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="JITTER" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="o52-9Z-cUM">
                    <rect key="frame" x="360" y="335.5" width="53" height="19.5"/>
                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                    <color key="textColor" red="0.53725490200000003" green="0.53333333329999999" blue="0.53333333329999999" alpha="1" colorSpace="calibratedRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Qlb-PZ-E68">
                    <rect key="frame" x="435" y="1292" width="154" height="36"/>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="36" id="0I1-hj-66d"/>
                        <constraint firstAttribute="width" constant="154" id="T0j-5r-kxn"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                    <state key="normal" title="重新测速">
                        <color key="titleColor" red="0.43529411764705883" green="0.70980392156862748" blue="0.95686274509803915" alpha="1" colorSpace="calibratedRGB"/>
                    </state>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="18"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                    </userDefinedRuntimeAttributes>
                    <connections>
                        <action selector="restartAction:" destination="-1" eventType="touchUpInside" id="dE7-kW-Rbp"/>
                    </connections>
                </button>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0.00Mbps" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jf9-pc-5uc">
                    <rect key="frame" x="538" y="305.5" width="94.5" height="24"/>
                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="20"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="上传速度" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8Nm-aG-nqc">
                    <rect key="frame" x="538" y="335.5" width="65.5" height="19.5"/>
                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                    <color key="textColor" red="0.53725490200000003" green="0.53333333329999999" blue="0.53333333329999999" alpha="1" colorSpace="calibratedRGB"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <color key="backgroundColor" systemColor="systemBackgroundColor" cocoaTouchSystemColor="whiteColor"/>
            <constraints>
                <constraint firstItem="Q5M-cg-NOt" firstAttribute="bottom" secondItem="Qlb-PZ-E68" secondAttribute="bottom" constant="38" id="1tq-yq-GfT"/>
                <constraint firstItem="gOW-sz-9v2" firstAttribute="leading" secondItem="Q5M-cg-NOt" secondAttribute="leading" id="2Wg-j8-7cQ"/>
                <constraint firstItem="akp-16-hVI" firstAttribute="leading" secondItem="XfP-UF-2Dd" secondAttribute="leading" id="4fS-tV-exz"/>
                <constraint firstItem="Qlb-PZ-E68" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="79u-KI-VMm"/>
                <constraint firstItem="2pi-Sy-6Va" firstAttribute="leading" secondItem="4vx-Tm-qLX" secondAttribute="leading" id="7U2-fO-AfF"/>
                <constraint firstItem="2pi-Sy-6Va" firstAttribute="leading" secondItem="akp-16-hVI" secondAttribute="trailing" constant="145" id="9s2-sn-WtM"/>
                <constraint firstItem="Q5M-cg-NOt" firstAttribute="trailing" secondItem="buz-Zw-b9L" secondAttribute="trailing" id="Afn-8f-w1p"/>
                <constraint firstItem="gOW-sz-9v2" firstAttribute="bottom" secondItem="buz-Zw-b9L" secondAttribute="bottom" id="BRo-wc-0AV"/>
                <constraint firstItem="buz-Zw-b9L" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="BT7-X3-wta"/>
                <constraint firstItem="jf9-pc-5uc" firstAttribute="leading" secondItem="4vx-Tm-qLX" secondAttribute="leading" id="HR0-Ke-eCe"/>
                <constraint firstItem="8Nm-aG-nqc" firstAttribute="top" secondItem="jf9-pc-5uc" secondAttribute="bottom" constant="6" id="Iob-dg-fpW"/>
                <constraint firstItem="2pi-Sy-6Va" firstAttribute="centerY" secondItem="akp-16-hVI" secondAttribute="centerY" id="JZg-iM-jpO"/>
                <constraint firstItem="gOW-sz-9v2" firstAttribute="top" secondItem="Q5M-cg-NOt" secondAttribute="top" priority="750" id="Oeh-LU-3Mi"/>
                <constraint firstItem="o52-9Z-cUM" firstAttribute="leading" secondItem="y3G-d0-Edb" secondAttribute="leading" id="RTU-rC-g9I"/>
                <constraint firstItem="Q5M-cg-NOt" firstAttribute="trailing" secondItem="gOW-sz-9v2" secondAttribute="trailing" id="VDv-KT-4KN"/>
                <constraint firstItem="gOW-sz-9v2" firstAttribute="top" relation="greaterThanOrEqual" secondItem="i5M-Pr-FkT" secondAttribute="top" constant="64" id="WEG-F2-Dem"/>
                <constraint firstItem="2pi-Sy-6Va" firstAttribute="top" secondItem="4vx-Tm-qLX" secondAttribute="bottom" constant="6" id="YKG-rF-1P7"/>
                <constraint firstItem="buz-Zw-b9L" firstAttribute="leading" secondItem="Q5M-cg-NOt" secondAttribute="leading" id="bxb-id-nRa"/>
                <constraint firstItem="4vx-Tm-qLX" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="centerX" constant="26" id="dCj-y8-zO0"/>
                <constraint firstItem="4vx-Tm-qLX" firstAttribute="top" secondItem="buz-Zw-b9L" secondAttribute="bottom" constant="40" id="gMA-Df-YX4"/>
                <constraint firstItem="8Nm-aG-nqc" firstAttribute="leading" secondItem="jf9-pc-5uc" secondAttribute="leading" id="gb3-Jo-V1R"/>
                <constraint firstItem="y3G-d0-Edb" firstAttribute="leading" secondItem="akp-16-hVI" secondAttribute="leading" id="hTZ-xb-RTb"/>
                <constraint firstItem="4vx-Tm-qLX" firstAttribute="centerY" secondItem="XfP-UF-2Dd" secondAttribute="centerY" id="lpS-eO-gvk"/>
                <constraint firstItem="o52-9Z-cUM" firstAttribute="top" secondItem="y3G-d0-Edb" secondAttribute="bottom" constant="6" id="lri-B1-2zj"/>
                <constraint firstItem="jf9-pc-5uc" firstAttribute="centerY" secondItem="y3G-d0-Edb" secondAttribute="centerY" id="m9S-Mi-soH"/>
                <constraint firstItem="y3G-d0-Edb" firstAttribute="top" secondItem="akp-16-hVI" secondAttribute="bottom" constant="24" id="wFF-V2-VVw"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="Q5M-cg-NOt"/>
            <point key="canvasLocation" x="223" y="46"/>
        </view>
    </objects>
</document>
