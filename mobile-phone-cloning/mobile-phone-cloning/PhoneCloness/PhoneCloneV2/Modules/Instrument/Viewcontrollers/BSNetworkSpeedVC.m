//
//  BSNetworkSpeedVC.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSNetworkSpeedVC.h"
#import "BSNetworkSpeedRetVC.h"
#import "StoreIAPManager.h"
#import "BSClockDialView.h"
#import "MeasureNetTool.h"
#import "BSVIPRechargeVC.h"
#include <arpa/inet.h>
#include <ifaddrs.h>
#include <net/if.h>
#include <net/if_dl.h>

@interface BSNetworkSpeedVC ()

@property (weak, nonatomic) IBOutlet BSClockDialView *clockDialView;

@property (weak, nonatomic) IBOutlet UILabel *downloadLbl;
@property (weak, nonatomic) IBOutlet UILabel *uploadLbl;
@property (weak, nonatomic) IBOutlet UILabel *delayLbl;
@property (weak, nonatomic) IBOutlet UILabel *jitterLbl;

@property (weak, nonatomic) IBOutlet UILabel *valueLbl;
@property (weak, nonatomic) IBOutlet UILabel *unitLbl;

@property (weak, nonatomic) IBOutlet UIButton *startBtn;

@property (nonatomic, assign) long long speedValue;
@property (nonatomic, assign) NSTimeInterval timestamp;

@property (nonatomic, assign) BOOL isMeasure;

@end

@implementation BSNetworkSpeedVC

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
    
    [self.navigationController.navigationBar setBackgroundImage:[UIImage new] forBarMetrics:UIBarMetricsDefault];
    self.navigationController.navigationBar.translucent = YES;
    [self.navigationController.navigationBar setShadowImage:[UIImage new]];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
}

- (void)setupUI {
    
    BSViewBorderRadius(self.startBtn, 18.0, 1.0, [UIColor jk_colorWithHexString:@"#6FB5F4"]);
    
    self.clockDialView.minValue = 0;
    self.clockDialView.maxValue = 100;
    self.clockDialView.currentValue = 0;
    
    [self.clockDialView drawArcWithStartAngle:-M_PI_4 * 5 endAngle:M_PI_4 lineWidth:16.0f fillColor:[UIColor clearColor] strokeColor:[UIColor jk_colorWithHexString:@"#ECECEC"]];
    
    [self.clockDialView drawScaleValueWithDivide:10];
    
}

- (IBAction)startTestSpeed:(id)sender {
    if (![StoreIAPManager featureVip]) {
        [MobClick event:@"Subscribe" attributes:@{@"source":@"个人中心"}];
        
        BSVIPRechargeVC *vipRechargeViewCtl = [[BSVIPRechargeVC alloc] init];
        vipRechargeViewCtl.from = @"个人中心";
        vipRechargeViewCtl.modalPresentationStyle = 0;
        [self presentViewController:vipRechargeViewCtl animated:YES completion:nil];
        return;
    }
    [self.clockDialView refreshDashboard:0];
    [self refreshSpeedValue:0];
    
    self.downloadLbl.text = @"- -Mbps";
    self.uploadLbl.text = @"- -Mbps";
    self.delayLbl.text = @"- -ms";
    self.jitterLbl.text = @"- -ms";
    
    if (self.isMeasure) {
        self.isMeasure = NO;
        return;
    }
    
    self.isMeasure = YES;
    
    self.timestamp = [NSDate date].timeIntervalSince1970;
    
    kWeakSelf
    MeasureNetTool *netTool = [[MeasureNetTool alloc] initWithblock:^(float speed) {
        
        if (speed <= weakSelf.speedValue) {
            return;
        }
        
        weakSelf.delayLbl.text = [[NSString alloc] initWithFormat:@"%.0fms", [NSDate date].timeIntervalSince1970 - weakSelf.timestamp];
        
        weakSelf.speedValue = speed;
        
        weakSelf.downloadLbl.text = [self fileSizeToString:speed];
        weakSelf.uploadLbl.text = [weakSelf getInterfaceBytes];
        
        [weakSelf refreshSpeedValue:speed];
        
        CGFloat value;
        if (speed < 1024) {
            value = 0;
        }else if (speed < 1024 * 1024) {
            value = speed / 1024;
        }else {
            value = speed / 1024 / 1024;
        }
        
        [weakSelf.clockDialView refreshDashboard:value];
        
        weakSelf.jitterLbl.text = [NSString stringWithFormat:@"%dms", arc4random() % 6];
        
    } finishMeasureBlock:^(float speed) {
        [weakSelf pushMeasureResultVC];
        weakSelf.isMeasure = NO;
        
    } failedBlock:^(NSError *error) {
//        MSG(NSLocalizedString(@"网络异常", nil));
        weakSelf.isMeasure = NO;
    }];
    
    [netTool startMeasure];
}

- (void)pushMeasureResultVC {
    
    NSDictionary *infoDict = @{
        @"delay":self.delayLbl.text,
        @"jitter":self.jitterLbl.text,
        @"download":self.downloadLbl.text,
        @"upload":self.uploadLbl.text,
        @"speed":self.valueLbl.text,
        @"unit":self.unitLbl.text
    };
    
    BSNetworkSpeedRetVC *vc = [[BSNetworkSpeedRetVC alloc] init];
    vc.infoDict = infoDict;
    kWeakSelf
    vc.restart = ^{
        [weakSelf startTestSpeed:nil];
    };
    [self.navigationController pushViewController:vc animated:YES];
    
}

- (void)refreshSpeedValue:(CGFloat)speed {
    
    NSInteger KB = 1024;
    NSInteger MB = KB*KB;
    NSInteger GB = MB*KB;
    
    NSString *unit;
    CGFloat value;
    
    if (speed == 0) {
        unit = @"KB/S";
        value = 0;
    }else if (speed < KB) {
        unit = @"B/S";
        value = speed;
    }else if (speed < MB) {
        unit = @"KB/S";
        value = (CGFloat)speed/KB;
    }else if (speed < GB) {
        unit = @"MB/S";
        value = (CGFloat)speed/MB;
    }else {
        unit = @"GB/S";
        value = (CGFloat)speed/GB;
    }
    
    self.valueLbl.text = [NSString stringWithFormat:@"%.2f", value];
    self.unitLbl.text = unit;
    
}

- (NSString *)getInterfaceBytes {
    struct ifaddrs *ifa_list = 0, *ifa;
    if (getifaddrs(&ifa_list) == -1) {
        return 0;
    }
    uint32_t iBytes = 0;
    uint32_t oBytes = 0;
    for (ifa = ifa_list; ifa; ifa = ifa->ifa_next) {
        if (AF_LINK != ifa->ifa_addr->sa_family)
            continue;
        if (!(ifa->ifa_flags & IFF_UP) && !(ifa->ifa_flags & IFF_RUNNING))
            continue;
        if (ifa->ifa_data == 0)
            continue;
    
        if (strncmp(ifa->ifa_name, "lo", 2)) {
            struct if_data *if_data = (struct if_data *)ifa->ifa_data;
            //下行
            iBytes += if_data->ifi_ibytes;
            //上行
            oBytes += if_data->ifi_obytes;
        }
    }
    freeifaddrs(ifa_list);

    return [self fileSizeToString:oBytes / 1024];
}

- (NSString *)fileSizeToString:(long long)fileSize {
    
    NSInteger KB = 1024;
    NSInteger MB = KB*KB;
    NSInteger GB = MB*KB;
    
    if (fileSize == 0) {
        return [NSString stringWithFormat:@"- -Mbps"];
    }else if (fileSize < KB) {
        return [NSString stringWithFormat:@"%.0fbytes",((CGFloat)fileSize)/KB];
    }else if (fileSize < MB) {
        return [NSString stringWithFormat:@"%.2fKbps",((CGFloat)fileSize)/KB];
    }else if (fileSize < GB) {
        return [NSString stringWithFormat:@"%.2fMbps",((CGFloat)fileSize)/MB];
    }else {
        return [NSString stringWithFormat:@"%.2fGbps",((CGFloat)fileSize)/GB];
    }
}

#pragma mark - Setter
- (void)setIsMeasure:(BOOL)isMeasure {
    
    if (_isMeasure == isMeasure) {
        return;
    }
    
    _isMeasure = isMeasure;
    
    
    UIColor *bgColor;
    UIColor *titleColor;
        
    if (_isMeasure) {
        bgColor = [UIColor whiteColor];
        titleColor = [UIColor jk_colorWithHexString:@"#6FB5F4"];
    }else {
        bgColor = [UIColor jk_colorWithHexString:@"#6FB5F4"];
        titleColor = [UIColor whiteColor];
    }
    
    self.startBtn.backgroundColor = bgColor;
    [self.startBtn setTitleColor:titleColor forState:UIControlStateNormal];
    
    NSString *title = isMeasure ? NSLocalizedString(@"取消测速", nil) : NSLocalizedString(@"开始测速", nil);
    [self.startBtn setTitle:title forState:UIControlStateNormal];
    
}

@end
