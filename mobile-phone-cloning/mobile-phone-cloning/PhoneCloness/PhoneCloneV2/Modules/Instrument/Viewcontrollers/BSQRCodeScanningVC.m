//
//  BSQRCodeScanningVC.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSQRCodeScanningVC.h"
#import "BSReceivQRCodeVC.h"
#import "BSDownloadQrcodeVC.h"

#import "SGQRCode.h"
#import "BSView.h"

@interface BSQRCodeScanningVC () <SGQRCodeScanManagerDelegate>

@property (nonatomic, strong) SGQRCodeScanManager *manager;
@property (nonatomic, strong) SGQRCodeScanningView *scanningView;
@property (nonatomic, strong) UIView *bottomView;

@property (weak, nonatomic) IBOutlet BSView *funcView;


@end

@implementation BSQRCodeScanningVC

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [self.scanningView addTimer];
    [self.manager resetSampleBufferDelegate];
    [self.manager startRunning];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [self.scanningView removeTimer];
    [self.manager cancelSampleBufferDelegate];
}


- (void)viewDidLoad {
    [super viewDidLoad];
    self.fd_prefersNavigationBarHidden = YES;
    self.automaticallyAdjustsScrollViewInsets = NO;
    
    [self setupUI];
}

- (void)dealloc {
    [self removeScanningView];
}

- (UIStatusBarStyle)preferredStatusBarStyle {
    return UIStatusBarStyleLightContent;
}

- (void)setupUI {
    
    self.view.backgroundColor = [UIColor blackColor];
    [self.view insertSubview:self.scanningView atIndex:0];
    [self.view insertSubview:self.bottomView atIndex:0];

    kWeakSelf
    dispatch_async(dispatch_get_main_queue(), ^{
        [weakSelf setupQRCodeScanning];
        
    });
    
    self.funcView.tapAction = ^{
        [weakSelf funcViewTapAction];
    };
      
}

- (void)setupQRCodeScanning {
    self.manager = [SGQRCodeScanManager sharedManager];
    NSArray *arr = @[AVMetadataObjectTypeQRCode, AVMetadataObjectTypeEAN13Code, AVMetadataObjectTypeEAN8Code, AVMetadataObjectTypeCode128Code];
    [self.manager setupSessionPreset:AVCaptureSessionPreset1920x1080 metadataObjectTypes:arr currentController:self];
    self.manager.delegate = self;
}

- (void)removeScanningView {
    [self.scanningView removeTimer];
    [self.scanningView removeFromSuperview];
    self.scanningView = nil;
}

- (void)handlerScanResult:(NSString *)result {

    if (self.scanCallback) {
        self.scanCallback(result);
    }
    
    [self.navigationController popViewControllerAnimated:YES];
    
}

- (void)funcViewTapAction {
    BSDownloadQrcodeVC *vc = [[BSDownloadQrcodeVC alloc] init];
    [self.navigationController pushViewController:vc animated:YES];
}

- (IBAction)backAction:(id)sender {
    [self.navigationController popViewControllerAnimated:YES];
}

- (IBAction)qrcodeBtnAction:(id)sender {
    BSReceivQRCodeVC *vc = [[BSReceivQRCodeVC alloc] init];
    [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark - SGQRCodeScanManagerDelegate
- (void)QRCodeScanManager:(SGQRCodeScanManager *)scanManager didOutputMetadataObjects:(NSArray *)metadataObjects {
    if (metadataObjects != nil && metadataObjects.count > 0) {
        [scanManager playSoundName:@"SGQRCode.bundle/sound.caf"];
        [scanManager stopRunning];
        
        AVMetadataMachineReadableCodeObject *obj = metadataObjects[0];
        [self handlerScanResult:[obj stringValue]];
        
    } else {
        [self.navigationController popViewControllerAnimated:YES];
//        MSG(@"未识别出扫描的二维码");
    }
}

#pragma mark - Getter
- (SGQRCodeScanningView *)scanningView {
    if (!_scanningView) {
        _scanningView = [[SGQRCodeScanningView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, 0.9 * kScreenHeight)];
        _scanningView.cornerColor = [UIColor colorWithRed:111/255.0 green:181/255.0 blue:244/255.0 alpha:1.0];
    }
    return _scanningView;
}

- (UIView *)bottomView {
    if (!_bottomView) {
        _bottomView = [[UIView alloc] initWithFrame:CGRectMake(0, CGRectGetMaxY(self.scanningView.frame), kScreenWidth, kScreenHeight - CGRectGetMaxY(self.scanningView.frame))];
        _bottomView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
    }
    return _bottomView;
}

@end
