//
//  BSInstrumentVC.m
//  PhoneClone
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSInstrumentVC.h"
#import "BSInstrumentDetailVC.h"
#import "BSNetworkSpeedVC.h"
#import <StoreKit/StoreKit.h>
#import "BSInsItemView.h"

@interface BSInstrumentVC ()

@property (weak, nonatomic) IBOutlet UIView *contentView;

@property (nonatomic, strong) NSArray *dataArray;

@property (nonatomic, assign) BOOL hasInit;

@end

@implementation BSInstrumentVC


- (void)viewDidLoad {
    [super viewDidLoad];
    self.fd_prefersNavigationBarHidden = YES;
    if([SKStoreReviewController respondsToSelector:@selector(requestReview)]) {
        [[UIApplication sharedApplication].keyWindow endEditing:YES];
        [SKStoreReviewController requestReview];
    }
    [self showBannerView:CGRectMake(0, self.view.jk_bottom-90, kDeviceWidth, 90) size:CGSizeMake(kDeviceWidth, 90)];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear: animated];
    
    if (!self.hasInit) {
        self.hasInit = YES;
        [self setupUI];
    }
}

- (void)setupUI {
    
    CGFloat itemW = 100.0;
    CGFloat itemH = 120.0;
    
    CGFloat lineSpacing = (kScreenWidth + 8 - itemW * 3) / 4.f;
    CGFloat interitemSpacing = 15.0;

    CGFloat margin = lineSpacing - 4.0;
    
    for (NSInteger i = 0; i < self.dataArray.count; i++) {
    
        CGFloat x = margin + (itemW + lineSpacing) * (i % 3);
        CGFloat y = (itemH + interitemSpacing) * (i / 3);
        
        BSInsItemView *itemView = [BSInsItemView getInsItemView];
        itemView.frame = CGRectMake(x, y, itemW, itemH);
        itemView.infoDict = self.dataArray[i];
        kWeakSelf
        itemView.tapAction = ^{
            [weakSelf viewTapAction:i];
        };
        
        [self.contentView addSubview:itemView];
        
    }
    
}

- (void)viewTapAction:(NSInteger)index {
    
    NSDictionary *infoDict = self.dataArray[index];
    NSString *title = infoDict[@"title"];
    switch (index) {
        case 0:
            [MobClick event:@"commonly_used" attributes:@{@"source":@"电池"}];
            break;
        case 1:
            [MobClick event:@"commonly_used" attributes:@{@"source":@"网络"}];
            break;
        case 2:
            [MobClick event:@"commonly_used" attributes:@{@"source":@"设备信息"}];
            break;
        case 3:
            [MobClick event:@"commonly_used" attributes:@{@"source":@"屏幕"}];
            break;
        case 4:
            [MobClick event:@"commonly_used" attributes:@{@"source":@"CPU"}];
            break;
        case 5:
            [MobClick event:@"commonly_used" attributes:@{@"source":@"内存"}];
            break;
        case 6:
            [MobClick event:@"commonly_used" attributes:@{@"source":@"磁盘"}];
            break;
        case 7:
            [MobClick event:@"commonly_used" attributes:@{@"source":@"网络测速"}];
            break;
        default:
            break;
    }
    if ([title isEqualToString:NSLocalizedString(@"rate", nil)]) {
        
        BSNetworkSpeedVC *vc = [[BSNetworkSpeedVC alloc] init];
        vc.title = title;
        [self.navigationController pushViewController:vc animated:YES];
        
    }else {
        
        BSInstrumentDetailVC *vc = [[BSInstrumentDetailVC alloc] init];
        vc.title = title;
        vc.type = index;
        if(vc.type==0||vc.type==3||vc.type==5||vc.type==6)
        {
            [self showFullscreenVideoAd];
        }
        [self.navigationController pushViewController:vc animated:YES];
        
    }

}

#pragma mark - Getter
- (NSArray *)dataArray {

    if (!_dataArray) {
        _dataArray = @[
                       @{@"icon":@"ins_battery", @"title": NSLocalizedString(@"battery", nil)},
                       @{@"icon":@"ins_network", @"title": NSLocalizedString(@"network", nil)},
                       @{@"icon":@"ins_equipment", @"title": NSLocalizedString(@"deviceInfo", nil)},
                       @{@"icon":@"ins_screen", @"title": NSLocalizedString(@"screen", nil)},
                       @{@"icon":@"ins_cpu", @"title": NSLocalizedString(@"cpu", nil)},
                       @{@"icon":@"ins_memory", @"title": NSLocalizedString(@"memory", nil)},
                       @{@"icon":@"ins_disk", @"title": NSLocalizedString(@"disk", nil)},
                       @{@"icon":@"ins_rate", @"title": NSLocalizedString(@"rate", nil)}
                       ];
    }
    return _dataArray;
}

@end
