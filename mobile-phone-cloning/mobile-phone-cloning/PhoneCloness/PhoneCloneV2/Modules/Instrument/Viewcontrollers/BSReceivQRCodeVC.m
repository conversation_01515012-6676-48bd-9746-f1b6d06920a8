//
//  BSReceivQRCodeVC.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSReceivQRCodeVC.h"
#import "SGQRCodeGenerateManager.h"

#import "BSRetReciveFileServer.h"

#import <arpa/inet.h>
#import <netinet/in.h>
#import <ifaddrs.h>

@interface BSReceivQRCodeVC ()<BSConnectManagerDelegate>

@property (weak, nonatomic) IBOutlet UIImageView *qrcodeImageView;

@property (nonatomic, strong) BSRetReciveFileServer *recServer;

@end

@implementation BSReceivQRCodeVC

- (void)viewDidLoad {
    [super viewDidLoad];
   
    [self setupUI];
    
    self.recServer = [[BSRetReciveFileServer alloc] init];
    
    [self loadIpAddr];
}

- (void)setupUI {
    self.navigationItem.title = NSLocalizedString(@"accept", @"接收");
}

- (void)loadIpAddr {
    
    NSString *localIp = @"";
    
    struct ifaddrs *interfaces = NULL;
    struct ifaddrs *temp_addr = NULL;
    int success = 0;
    
    success = getifaddrs(&interfaces);
    
    if (success == 0) {
        temp_addr = interfaces;
        while (temp_addr != NULL) {
            if (temp_addr->ifa_addr->sa_family == AF_INET) {
                if([[NSString stringWithUTF8String:temp_addr->ifa_name] isEqualToString:@"en0"]) {
                    
                    //本机地址
                    localIp = [NSString stringWithUTF8String:inet_ntoa(((struct sockaddr_in *)temp_addr->ifa_addr)->sin_addr)];
                    
                    break;
                }
            }
            temp_addr = temp_addr->ifa_next;
            
        }
    }
    
    NSString *code = [NSString stringWithFormat:@"http://%@:8888",[DHIPAdress deviceIPAdress]];

    UIImage *qrImage = [SGQRCodeGenerateManager generateWithDefaultQRCodeData:code imageViewWidth:282];
    self.qrcodeImageView.image = qrImage;
    [BSConnectManager shareManager].delegate = self;
}

- (void)connectSuccess
{
    [self.navigationController popViewControllerAnimated:YES];
}

@end
