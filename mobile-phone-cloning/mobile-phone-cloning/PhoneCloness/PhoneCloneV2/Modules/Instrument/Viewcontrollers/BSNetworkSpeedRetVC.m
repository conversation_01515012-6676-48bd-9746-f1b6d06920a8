//
//  BSNetworkSpeedRetVC.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSNetworkSpeedRetVC.h"

@interface BSNetworkSpeedRetVC ()

@property (weak, nonatomic) IBOutlet UIView *bgView;

@property (weak, nonatomic) IBOutlet UILabel *downloadLbl;
@property (weak, nonatomic) IBOutlet UILabel *uploadLbl;
@property (weak, nonatomic) IBOutlet UILabel *delayLbl;
@property (weak, nonatomic) IBOutlet UILabel *jitterLbl;

@property (weak, nonatomic) IBOutlet UILabel *speedLbl;
@property (weak, nonatomic) IBOutlet UILabel *tipsLbl;


@property (weak, nonatomic) IBOutlet UIButton *restartBtn;


@end

@implementation BSNetworkSpeedRetVC

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
}

- (void)setupUI {
    
    self.navigationItem.title = NSLocalizedString(@"rate", nil);
    
    BSViewBorderRadius(self.restartBtn, 18.0, 1.0, [UIColor jk_colorWithHexString:@"#6FB5F4"]);
    
    CAGradientLayer *gradientLayer = [CAGradientLayer layer];
    gradientLayer.frame = self.bgView.bounds;
    gradientLayer.startPoint = CGPointMake(0.29, 0.35);
    gradientLayer.endPoint = CGPointMake(0.26, 1.14);
    gradientLayer.colors = @[(__bridge id)[UIColor colorWithRed:59/255.0 green:155/255.0 blue:240/255.0 alpha:1.0].CGColor, (__bridge id)[UIColor colorWithRed:147/255.0 green:200/255.0 blue:247/255.0 alpha:1.0].CGColor];
    gradientLayer.locations = @[@(0), @(1.0f)];
    
    [self.bgView.layer addSublayer:gradientLayer];
    
    self.delayLbl.text = self.infoDict[@"delay"];
    self.jitterLbl.text = self.infoDict[@"jitter"];
    self.downloadLbl.text = self.infoDict[@"download"];
    self.uploadLbl.text = self.infoDict[@"upload"];
    
    NSString *speed = self.infoDict[@"speed"];
    
    self.speedLbl.text = [NSString stringWithFormat:@"%@%@", speed, self.infoDict[@"unit"]];
    
    if ([speed floatValue] > 100) {
        self.tipsLbl.text = NSLocalizedString(@"高于100M的宽带", nil);
    }else {
        self.tipsLbl.text = NSLocalizedString(@"低于100M的宽带", nil);
    }
    
}


- (IBAction)restartAction:(id)sender {
    
    if (self.restart) {
        self.restart();
    }
    
    [self.navigationController popViewControllerAnimated:YES];
}

@end
