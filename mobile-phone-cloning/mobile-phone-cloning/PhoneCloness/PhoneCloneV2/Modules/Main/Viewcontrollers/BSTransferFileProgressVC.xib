<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="BSTransferFileProgressVC">
            <connections>
                <outlet property="contentView" destination="6r9-rE-j6T" id="yl5-Wc-vYV"/>
                <outlet property="progressLbl" destination="9AS-wc-NOd" id="QZr-Nw-ygg"/>
                <outlet property="titleLbl" destination="N3Z-f2-Y4U" id="j3k-DQ-FMS"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="umn-rR-TnP">
                    <rect key="frame" x="72" y="252.5" width="231" height="210"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="6r9-rE-j6T">
                            <rect key="frame" x="66.5" y="32" width="98" height="98"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9AS-wc-NOd">
                                    <rect key="frame" x="30.5" y="40" width="37.5" height="18"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                    <nil key="textColor"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="98" id="0eU-hq-ejq"/>
                                <constraint firstItem="9AS-wc-NOd" firstAttribute="centerX" secondItem="6r9-rE-j6T" secondAttribute="centerX" id="N2C-FH-aly"/>
                                <constraint firstItem="9AS-wc-NOd" firstAttribute="centerY" secondItem="6r9-rE-j6T" secondAttribute="centerY" id="SP0-XL-lyH"/>
                                <constraint firstAttribute="width" constant="98" id="UVm-Kv-h9u"/>
                            </constraints>
                        </view>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="已传送：X/X" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="N3Z-f2-Y4U">
                            <rect key="frame" x="72" y="155" width="87.5" height="18"/>
                            <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="6r9-rE-j6T" firstAttribute="centerX" secondItem="umn-rR-TnP" secondAttribute="centerX" id="6sg-VN-lXt"/>
                        <constraint firstItem="6r9-rE-j6T" firstAttribute="top" secondItem="umn-rR-TnP" secondAttribute="top" constant="32" id="Mxe-Oc-dcJ"/>
                        <constraint firstItem="N3Z-f2-Y4U" firstAttribute="top" secondItem="6r9-rE-j6T" secondAttribute="bottom" constant="25" id="SOR-ul-9xR"/>
                        <constraint firstAttribute="width" constant="231" id="iJS-bd-izY"/>
                        <constraint firstItem="N3Z-f2-Y4U" firstAttribute="centerX" secondItem="umn-rR-TnP" secondAttribute="centerX" id="pph-GV-OpJ"/>
                        <constraint firstAttribute="height" constant="210" id="rVR-HV-ZHw"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="10"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                    </userDefinedRuntimeAttributes>
                </view>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="umn-rR-TnP" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="bFw-qz-zoc"/>
                <constraint firstItem="umn-rR-TnP" firstAttribute="centerY" secondItem="i5M-Pr-FkT" secondAttribute="centerY" constant="24" id="fvt-CM-fVK"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="Q5M-cg-NOt"/>
            <point key="canvasLocation" x="-266" y="86"/>
        </view>
    </objects>
</document>
