//
//  BSTransferFileProgressVC.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSTransferFileProgressVC.h"

#import "ZZCircleProgress.h"

@interface BSTransferFileProgressVC ()

@property (weak, nonatomic) IBOutlet UIView *contentView;

@property (weak, nonatomic) IBOutlet UILabel *progressLbl;
@property (weak, nonatomic) IBOutlet UILabel *titleLbl;

@property (nonatomic, strong) ZZCircleProgress *progress;

@end

@implementation BSTransferFileProgressVC

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];

}

- (void)setupUI {
    
    self.view.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.4];
    
    self.progress = [[ZZCircleProgress alloc] initWithFrame:self.contentView.bounds pathBackColor:[UIColor whiteColor] pathFillColor:[UIColor jk_colorWithHexString:@"#1E6DDB"] startAngle:0 strokeWidth:4.0];
    self.progress.showProgressText = NO;

    [self.contentView addSubview:self.progress];
    
    kWeakSelf
    self.refreshProgress = ^(NSInteger currentIndex, NSInteger totalCount) {
        
        CGFloat progress = currentIndex * 1.0 / totalCount;
        weakSelf.progress.progress1 = progress;
        weakSelf.progressLbl.text = [NSString stringWithFormat:@"%.0f%%", progress * 100];
        
        NSString *tipsStr = weakSelf.isSendFile ? NSLocalizedString(@"hasBeenSent", @"正在传送") : NSLocalizedString(@"received", @"正在接收");
        
        NSString *fileTypeStr;
        switch (weakSelf.fileType) {
            case BSFileTypePhoto:
            {
                fileTypeStr = NSLocalizedString(@"Photos", nil);
            }
                break;
            case BSFileTypeVideo:
            {
                fileTypeStr = NSLocalizedString(@"Videos", nil);
            }
                break;
            case BSFileTypeContact:
            {
                fileTypeStr = NSLocalizedString(@"contacts", nil);
            }
                break;
            case BSFileTypeMusic:
            {
                fileTypeStr = NSLocalizedString(@"music", nil);
            }
                break;
            case BSFileTypeCalendar:
            {
                fileTypeStr = NSLocalizedString(@"calendar", nil);
            }
                break;
                
            default:
                break;
        }
        
        NSArray *appLanguages = [[NSUserDefaults standardUserDefaults] objectForKey:@"AppleLanguages"];
        NSString *languageName = [appLanguages objectAtIndex:0];
        
        if ([languageName isEqualToString:@"zh-Hans-CN"]) {
            weakSelf.titleLbl.text = [NSString stringWithFormat:@"%@%@", tipsStr, fileTypeStr];
        }else {
            weakSelf.titleLbl.text = [NSString stringWithFormat:@"%@ %@", tipsStr, fileTypeStr];
        }
        
        
        
//        weakSelf.titleLbl.text = [NSString stringWithFormat:@"%@：%ld/%ld", typeStr, (long)currentIndex, (long)totalCount];
        
        
    };

}

@end
