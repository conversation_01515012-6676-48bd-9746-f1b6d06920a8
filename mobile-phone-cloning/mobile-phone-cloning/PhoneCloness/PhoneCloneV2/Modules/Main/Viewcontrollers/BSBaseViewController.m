//
//  BSBaseViewController.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSBaseViewController.h"

@interface BSBaseViewController ()<MyAdManagerDelegate>
@property (nonatomic, strong) MyAdManager *adManager;

@end

@implementation BSBaseViewController

- (void)viewDidLoad {
    [super viewDidLoad];
//    [BUAdTestMeasurementManager showTestMeasurementWithController:self.navigationController];
}
- (void)showBannerView:(CGRect)frame size:(CGSize)size
{
    if ([W_PERSISTENT_GET_OBJECT(startWithADTimes) integerValue]>=[UpdateManager shareManagerWith:updateJsonUrl].AD_count) {
        AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
        //NSLog(@"showInterstitialAdCount = %d", appDelegate.showInterstitialAdCount);
//        NSLog(@"appDelegate.network = %ld", (long)appDelegate.network);
        

            if (self.adManager == nil) {
                self.adManager = [[MyAdManager alloc] init];
                self.adManager.delegate = self;
            }
                [self.adManager showBannerAdWithViewController:self frame:frame size:size];
        
        
    }
}

- (void)showFullscreenVideoAd
{
    if ([W_PERSISTENT_GET_OBJECT(startWithADTimes) integerValue]>=[UpdateManager shareManagerWith:updateJsonUrl].AD_count) {
        AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
        //NSLog(@"showInterstitialAdCount = %d", appDelegate.showInterstitialAdCount);
//        NSLog(@"appDelegate.network = %ld", (long)appDelegate.network);

            

            if (self.adManager == nil) {
                self.adManager = [[MyAdManager alloc] init];
                self.adManager.delegate = self;
            }
                [self.adManager showFullscreenVideoAdWithViewController:self];


            
            
        
    }
}

- (void)myAdManagerBannerAdViewRenderSuccess
{
    [self.adManager.bannerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.view.mas_bottomMargin);
        make.left.right.mas_equalTo(0);
        make.height.mas_equalTo(90);
    }];
}

@end
