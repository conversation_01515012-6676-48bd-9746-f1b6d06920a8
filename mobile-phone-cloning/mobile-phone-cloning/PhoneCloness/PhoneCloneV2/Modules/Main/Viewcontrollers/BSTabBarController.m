//
//  BSTabBarController.m
//  PhoneClone
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSTabBarController.h"
#import "BSNavigationController.h"

#import "BSHomeVC.h"
#import "BSInstrumentVC.h"
#import "BSCloudStorageVC.h"
#import "BSMeVC.h"
#import "BSCloudEnterVC.h"
#import "BSTabBarItem.h"
#import "UINavigationController+BSInitToTabBarChildController.h"
#import <Photos/Photos.h>
@interface BSTabBarController () <UITabBarControllerDelegate>

@property (nonatomic, strong) NSMutableArray *itemsArray;

@end

@implementation BSTabBarController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self initChildVCs];
}

- (void)initChildVCs {
    self.view.backgroundColor = [UIColor whiteColor];
    NSArray *navCtlInfoArr = @[
        @{@"ViewController":@"BSHomeVC",
          @"title":NSLocalizedString(@"传输TabBarTitle",nil),
          @"normalImg":@"refresh_icon_noselected",
          @"selectdImg":@"refresh_icon_selected",
          @"NavCtlType":@1,
        },
        @{@"ViewController":@"BSInstrumentVC",
          @"title":NSLocalizedString(@"常用TabBarTitle",nil),
          @"normalImg":@"tool_icon_nor_noselected",
          @"selectdImg":@"tool_icon_nor_selected",
          @"NavCtlType":@1,
        },
        @{@"ViewController":@"BSCloudEnterVC",
          @"title":NSLocalizedString(@"云存储TabBarTitle",nil),
          @"normalImg":@"cloud_storage_noselected",
          @"selectdImg":@"cloud_storage_selected",
          @"NavCtlType":@0,
        },
        @{@"ViewController":@"BSMeVC",
          @"title":NSLocalizedString(@"个人TabBarTitle",nil),
          @"normalImg":@"me_icon_nor_noselected",
          @"selectdImg":@"me_icon_nor_selected",
          @"NavCtlType":@1,
        },
    ];
    
    for (NSDictionary *infoDic in navCtlInfoArr) {
        NSInteger navCtlType = [infoDic[@"NavCtlType"] integerValue];
        UINavigationController *navCtl = [UINavigationController initWithRootControllerName:infoDic[@"ViewController"] title:infoDic[@"title"] normalImageName:infoDic[@"normalImg"] selectedImageName:infoDic[@"selectdImg"] navControllerType:navCtlType];
        [self addChildViewController:navCtl];
    }
    
}


- (void)initTabBar {
    
    UITabBar *myTabBar = [[UITabBar alloc] init];
    myTabBar.barTintColor = [UIColor whiteColor];
    
    myTabBar.backgroundColor = [UIColor whiteColor];
    
    myTabBar.layer.backgroundColor = [UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:1.0].CGColor;
    myTabBar.layer.shadowColor = [UIColor colorWithRed:0/255.0 green:0/255.0 blue:0/255.0 alpha:0.5].CGColor;
    myTabBar.layer.shadowOffset = CGSizeMake(0,2);
    myTabBar.layer.shadowOpacity = 1;
    myTabBar.layer.shadowRadius = 6;
    
    [self setValue:myTabBar forKey:@"tabBar"];
    
    NSArray *titles = @[NSLocalizedString(@"transferTabTitle", nil), NSLocalizedString(@"comonTabTitle", nil), NSLocalizedString(@"cloudStorageTabTitle", nil), NSLocalizedString(@"meTabTitle", nil)];
    
    NSInteger tabBarCount = titles.count;
    NSInteger marginCount = tabBarCount - 1;
    CGFloat margin = 40.0;
    CGFloat tabbarItemW = 1.0f*(kScreenWidth - margin * marginCount) / tabBarCount;
    CGFloat tabbarItemH = 49.0;
    
    for (NSInteger i = 0; i < titles.count; i++) {
        
        CGFloat x = (tabbarItemW + margin) * i;
        BSTabBarItem *item = [BSTabBarItem getTabBarItem];
        item.frame = CGRectMake(x, 0, tabbarItemW, tabbarItemH);
        item.title = titles[i];
        item.selected = i == 0;
        item.tag = i + 1;
        [myTabBar addSubview:item];
        
        [self.itemsArray addObject:item];
    }
    
    self.delegate = self;
    
}

#pragma mark - UITabBarControllerDelegate
- (void)tabBarController:(UITabBarController *)tabBarController didSelectViewController:(UIViewController *)viewController {

    for (NSInteger i = 0; i < self.itemsArray.count; i++) {

        BSTabBarItem *item = self.itemsArray[i];
        item.selected = item.tag == tabBarController.selectedIndex + 1;

    }
}

#pragma mark - Getter
- (NSMutableArray *)itemsArray {
    if (!_itemsArray) {
        _itemsArray = @[].mutableCopy;
    }
    return _itemsArray;
}

@end
