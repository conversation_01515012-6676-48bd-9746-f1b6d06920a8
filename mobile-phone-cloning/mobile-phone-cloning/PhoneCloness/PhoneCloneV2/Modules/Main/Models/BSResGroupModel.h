//
//  BSResGroupModel.h
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef enum : NSUInteger {
    BSResTypePhoto = 0,
    BSResType<PERSON><PERSON>o,
    BSResTypeContact,
    BSResTypeMusic,
    BSResTypeCalendar
} BSResType;

@interface BSResGroupModel : NSObject

@property (nonatomic, assign) BSResType resType;

@property (nonatomic, assign, getter=isSelected) BOOL selected;

@property (nonatomic, copy) NSString *title;
@property (nonatomic, strong) NSMutableArray *resArray;

@end

