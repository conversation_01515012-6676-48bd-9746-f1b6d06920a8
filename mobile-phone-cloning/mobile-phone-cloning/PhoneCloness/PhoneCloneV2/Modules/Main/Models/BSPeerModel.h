//
//  BSPeerModel.h
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <MultipeerConnectivity/MultipeerConnectivity.h>

typedef enum : NSUInteger {
    BSFileTypePhoto = 0,
    BSFileType<PERSON>ideo,
    BSFileTypeContact,
    BSFileTypeMusic,
    BSFileTypeCalendar
} BSFileType;

@interface BSPeerModel : NSObject

@property (nonatomic, strong) MCPeerID *peerID;
@property (nonatomic, copy) NSString *ids;

@end

