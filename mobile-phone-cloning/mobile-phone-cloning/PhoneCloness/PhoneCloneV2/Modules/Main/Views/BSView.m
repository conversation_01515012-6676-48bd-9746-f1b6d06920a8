//
//  BSView.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSView.h"

@implementation BSView

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    
    for (UIView *subView in self.subviews) {
        subView.alpha = 0.4;
    }
    
}

- (void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    for (UIView *subView in self.subviews) {
        subView.alpha = 1.0;
    }
    
    
    if (self.tapAction) {
        self.tapAction();
    }
}

- (void)touchesCancelled:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    for (UIView *subView in self.subviews) {
        subView.alpha = 1.0;
    }
}

@end

