//
//  BSTabBarItem.m
//  PhoneClone
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSTabBarItem.h"

@interface BSTabBarItem ()

@property (weak, nonatomic) IBOutlet UILabel *titleLbl;
@property (weak, nonatomic) IBOutlet UIView *lineView;

@end

@implementation BSTabBarItem

+ (instancetype)getTabBarItem {
    return [[[NSBundle mainBundle] loadNibNamed:@"BSTabBarItem" owner:nil options:nil] lastObject];
}

- (void)setTitle:(NSString *)title {
    _title = title;
    self.titleLbl.text = title;
}

- (void)setSelected:(BOOL)selected {
    _selected = selected;
    
    if (selected) {
        self.titleLbl.textColor = [UIColor blackColor];
        self.titleLbl.font = [UIFont systemFontOfSize:14.0 weight:UIFontWeightMedium];
    }else {
        self.titleLbl.textColor = [UIColor jk_colorWithHexString:@"#868686"];
        self.titleLbl.font = [UIFont systemFontOfSize:14.0 weight:UIFontWeightRegular];
    }
    
    self.lineView.hidden = !selected;
    
}

@end
