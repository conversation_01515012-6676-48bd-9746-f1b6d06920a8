<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="BSTabBarItem">
            <rect key="frame" x="0.0" y="0.0" width="121" height="114"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="个人" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LRh-oh-COh">
                    <rect key="frame" x="46" y="48.5" width="29" height="17"/>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <view hidden="YES" alpha="0.72760000000000002" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="0co-J8-iJb">
                    <rect key="frame" x="44" y="61.5" width="33" height="6"/>
                    <color key="backgroundColor" red="0.25490196078431371" green="0.51372549019607838" blue="0.94901960784313721" alpha="1" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="6" id="eW0-yM-4Z2"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="4"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                    </userDefinedRuntimeAttributes>
                </view>
            </subviews>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="LRh-oh-COh" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="3v9-CB-IRK"/>
                <constraint firstItem="0co-J8-iJb" firstAttribute="top" secondItem="LRh-oh-COh" secondAttribute="bottom" constant="-4" id="WGy-na-8Bw"/>
                <constraint firstItem="0co-J8-iJb" firstAttribute="trailing" secondItem="LRh-oh-COh" secondAttribute="trailing" constant="2" id="jiV-tY-Rmo"/>
                <constraint firstItem="0co-J8-iJb" firstAttribute="leading" secondItem="LRh-oh-COh" secondAttribute="leading" constant="-2" id="pB8-tW-UpO"/>
                <constraint firstItem="LRh-oh-COh" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" id="vQs-9k-Ifv"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <connections>
                <outlet property="lineView" destination="0co-J8-iJb" id="NOi-wf-5uP"/>
                <outlet property="titleLbl" destination="LRh-oh-COh" id="Zof-Xe-NKg"/>
            </connections>
            <point key="canvasLocation" x="-141.59999999999999" y="188.00599700149925"/>
        </view>
    </objects>
</document>
