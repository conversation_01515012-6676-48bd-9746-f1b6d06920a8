//
//  BSMeVC.m
//  PhoneClone
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSMeVC.h"
#import "BSView.h"
#import "ZZCircleProgress.h"
#import "BSDeviceInfo.h"

#import "BSCloudStorageVC.h"
#import "BSCloudStorageHeadView.h"
#import "BSCsBodyCellView.h"
#import "BSAccountModel.h"
#import "BSAWSS3Manager.h"
#import "BSVIPRechargeVC.h"
#import "BSAWSDynamoDBMgr.h"
@interface BSMeVC ()

@property (strong, nonatomic) IBOutletCollection(BSView) NSArray *itemViewsArray;

@property (weak, nonatomic) IBOutlet UIView *progressBgView;
@property (nonatomic, strong) ZZCircleProgress *progress;
@property (weak, nonatomic) IBOutlet UILabel *progressLbl;

@end

@implementation BSMeVC

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.fd_prefersNavigationBarHidden = YES;
    [self setupUI];
    if (@available(iOS 10.3, *)) {
        //        [SKStoreReviewController requestReview];
    }
    [self showBannerView:CGRectMake(0, self.view.jk_bottom-90, kDeviceWidth, 90) size:CGSizeMake(kDeviceWidth, 90)];
}

- (void)setupUI {
    
    for (BSView *view in self.itemViewsArray) {
        kWeakSelf
        weakObj(view)
        view.tapAction = ^{
            [weakSelf itemViewTapAction:weak_view];
        };
    }
    
    self.progress = [[ZZCircleProgress alloc] initWithFrame:self.progressBgView.bounds pathBackColor:[UIColor whiteColor] pathFillColor:[UIColor jk_colorWithHexString:@"#23F897"] startAngle:0 strokeWidth:10.0];
    self.progress.showProgressText = NO;
    
    long long availableMemorySize = [BSDeviceInfo availableMemorySize];
    long long totalMemorySize = [BSDeviceInfo totalMemorySize];
    
    if (availableMemorySize >= totalMemorySize) {
        availableMemorySize = totalMemorySize - [BSDeviceInfo usedMemorySize];
    }
    
    CGFloat progress = availableMemorySize * 1.0 / totalMemorySize;
    self.progress.progress = 1 - progress;
    [self.progressBgView addSubview:self.progress];
    
    self.progressLbl.text = [NSString stringWithFormat:@"%.0f%%", progress * 100];
}

- (void)itemViewTapAction:(BSView *)view {
    switch (view.tag) {
        case 11:
            [self gotoVipSubscribeAction];
            break;
            
        case 12:
            [self gotoAgreementAction];
            break;
            
        case 13:
            [self gotoPrivacyPolicyAction];
            break;
            
        case 14:
            [self commentAction];
            break;
            
        case 15:
            [self shareAppAction];
            break;
            
        case 16:
            [self deletUserLoginAction];
            break;
    }
}

- (void)gotoVipSubscribeAction{
    BSVIPRechargeVC *vipRechargeViewCtl = [[BSVIPRechargeVC alloc] init];
    vipRechargeViewCtl.modalPresentationStyle = 0;
    [self presentViewController:vipRechargeViewCtl animated:YES completion:nil];
}

- (void)gotoAgreementAction{
    [[UIApplication sharedApplication] openURL:[NSURL URLWithString:@"https://www.freeprivacypolicy.com/privacy/view/2e6556241582cf0a67fbb9b06cd98b0d"] options:[NSDictionary dictionary] completionHandler:nil];
}

- (void)gotoPrivacyPolicyAction{
    [[UIApplication sharedApplication] openURL:[NSURL URLWithString:@"https://www.freeprivacypolicy.com/privacy/view/2e6556241582cf0a67fbb9b06cd98b0d"] options:[NSDictionary dictionary] completionHandler:nil];
}

- (void)commentAction{
    //评分
    if (@available(iOS 10.3, *)) {
        [SKStoreReviewController requestReview];
    }
}

- (void)shareAppAction{
    //分享
    NSString *shareTitle = @"一键换机 https://apps.apple.com/app/id1506745684";
    UIImage *shareImage = [UIImage imageNamed:@"logo"];
    NSURL *shareUrl = [NSURL URLWithString:@"https://apps.apple.com/app/id1506745684"];
    NSArray *items = @[shareTitle, shareImage];
    UIActivityViewController *activityVC = [[UIActivityViewController alloc] initWithActivityItems:items applicationActivities:nil];
    if (UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPad) {
        activityVC.popoverPresentationController.sourceView = self.view;
        [self presentViewController:activityVC animated:YES completion:nil];
    } else {
        [self presentViewController:activityVC animated:YES completion:nil];
    }
}


- (void)deletUserLoginAction{
    //预先判断用户是否登录,或者用户登录是否过期
    if (![BSUserDefaultManager isExistLoginUser]) {
        //如果没有登录用户,强制退出云存储界面
        [SVProgressHUD showInfoWithStatus:local(@"您还没有登陆请登录后重新尝试")];
        self.tabBarController.selectedIndex = 2;
        
        return;
    }
    UIAlertController* al = [UIAlertController alertControllerWithTitle:local(@"提示") message:local(@"注销账号会永久删除您的云上所有文件！请问确认要注销账户吗？") preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction* act = [UIAlertAction actionWithTitle:local(@"确定") style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
        [SVProgressHUD show];
        [BSAWSDynamoDBMgr deleteUserWithEmail:[BSUserDefaultManager getUserModelByUserDefault].email createTime:[BSUserDefaultManager getUserModelByUserDefault].createTime completionHandler:^(AWSDynamoDBDeleteItemOutput * _Nonnull response, NSError * _Nonnull error) {
            [BSUserDefaultManager exitLoginUser];
            [BSAWSS3Manager getListObjectsWithPath:[BSUserDefaultManager getUserModelByUserDefault].path completionHandler:^(AWSS3ListObjectsOutput * _Nonnull response, NSError * _Nonnull error) {
                [BSAWSS3Manager deletePathWithPaths:response];
            }];
            dispatch_async(dispatch_get_main_queue(), ^{
                [SVProgressHUD showInfoWithStatus:local(@"账户已经注销！")];
                
                UINavigationController* navi = self.tabBarController.viewControllers[2];
                
                [navi popViewControllerAnimated:NO];
                self.tabBarController.selectedIndex = 2;
            });
            
        }];
        
    }];
    
    UIAlertAction* act2 = [UIAlertAction actionWithTitle:local(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        
    }];
    
    [al addAction:act];
    [al addAction:act2];
    [self presentViewController:al animated:YES completion:^{
        
    }];
}
@end
