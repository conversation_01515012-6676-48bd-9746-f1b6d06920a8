<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="16096" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="BSAvatarView">
            <rect key="frame" x="0.0" y="0.0" width="123" height="99"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="avatar_icon" translatesAutoresizingMaskIntoConstraints="NO" id="cLU-0g-GQo">
                    <rect key="frame" x="37" y="2" width="49" height="49"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="49" id="1tG-M4-sMs"/>
                        <constraint firstAttribute="width" constant="49" id="F51-Jh-byY"/>
                    </constraints>
                </imageView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TBM-b6-rRo">
                    <rect key="frame" x="44" y="55" width="35.5" height="17"/>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="cLU-0g-GQo" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="5bz-Kj-HOo"/>
                <constraint firstItem="cLU-0g-GQo" firstAttribute="top" secondItem="vUN-kp-3ea" secondAttribute="top" constant="2" id="7WF-ju-AJ7"/>
                <constraint firstItem="TBM-b6-rRo" firstAttribute="top" secondItem="cLU-0g-GQo" secondAttribute="bottom" constant="4" id="EeY-UZ-Jm4"/>
                <constraint firstItem="TBM-b6-rRo" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="Pxr-Rr-Wu8"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <connections>
                <outlet property="iconImageView" destination="cLU-0g-GQo" id="IAa-d7-1zE"/>
                <outlet property="nameLbl" destination="TBM-b6-rRo" id="xVD-qh-jiR"/>
            </connections>
            <point key="canvasLocation" x="-695.20000000000005" y="175.86206896551727"/>
        </view>
    </objects>
    <resources>
        <image name="avatar_icon" width="128" height="128"/>
    </resources>
</document>
