//
//  BSTransferEmptyView.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSTransferEmptyView.h"

@interface BSTransferEmptyView ()

@property (weak, nonatomic) IBOutlet UILabel *titleLbl;
@property (weak, nonatomic) IBOutlet UILabel *subtitleLbl;

@end

@implementation BSTransferEmptyView

- (void)awakeFromNib {
    [super awakeFromNib];
    self.titleLbl.text = NSLocalizedString(@"没有可连接设备", nil);
    self.subtitleLbl.text = NSLocalizedString(@"请确保两台或以上设备连接", nil);
}

+ (instancetype)getTransferEmptyView {
    return [[[NSBundle mainBundle] loadNibNamed:@"BSTransferEmptyView" owner:nil options:nil] lastObject];
}

@end
