<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="BSContactHeaderView">
            <rect key="frame" x="0.0" y="0.0" width="375" height="85"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="y6d-mT-aG3">
                    <rect key="frame" x="0.0" y="0.0" width="375" height="85"/>
                    <color key="backgroundColor" red="0.9137254901960784" green="0.9137254901960784" blue="0.9137254901960784" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                </view>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="S4S-st-huJ">
                    <rect key="frame" x="23" y="33" width="40.5" height="19.5"/>
                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <color key="backgroundColor" red="0.91769343614578247" green="0.90958207845687866" blue="0.92157071828842163" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
            <constraints>
                <constraint firstItem="vUN-kp-3ea" firstAttribute="trailing" secondItem="y6d-mT-aG3" secondAttribute="trailing" id="BS7-Ev-qV0"/>
                <constraint firstItem="S4S-st-huJ" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" id="FMP-PW-3ke"/>
                <constraint firstItem="S4S-st-huJ" firstAttribute="leading" secondItem="vUN-kp-3ea" secondAttribute="leading" constant="23" id="HCo-mo-YVu"/>
                <constraint firstAttribute="bottom" secondItem="y6d-mT-aG3" secondAttribute="bottom" id="ZMS-R2-ScR"/>
                <constraint firstItem="y6d-mT-aG3" firstAttribute="leading" secondItem="vUN-kp-3ea" secondAttribute="leading" id="cy0-UG-HLf"/>
                <constraint firstItem="y6d-mT-aG3" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="exk-dD-BQr"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <connections>
                <outlet property="titleLbl" destination="S4S-st-huJ" id="A8I-qP-IQe"/>
            </connections>
            <point key="canvasLocation" x="-521" y="33"/>
        </view>
    </objects>
</document>
