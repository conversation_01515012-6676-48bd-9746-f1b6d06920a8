<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="16096" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="BSTransferPeerCell" rowHeight="57" id="KGk-i7-Jjw" customClass="BSTransferPeerCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="57"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="57"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9ZN-yF-ehU">
                        <rect key="frame" x="20" y="19" width="39.5" height="19.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="vrg-ht-1Sf">
                        <rect key="frame" x="253" y="15" width="54" height="27"/>
                        <color key="backgroundColor" red="0.43529411764705883" green="0.70980392156862748" blue="0.95686274509803915" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="54" id="Gzv-WJ-S5L"/>
                            <constraint firstAttribute="height" constant="27" id="QJ5-qJ-V9I"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="13"/>
                        <state key="normal" title="发送">
                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </state>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                <integer key="value" value="11"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                        </userDefinedRuntimeAttributes>
                        <connections>
                            <action selector="sendBtnAction:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="ioZ-4N-nUB"/>
                        </connections>
                    </button>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Cfu-aZ-sfk">
                        <rect key="frame" x="20" y="55" width="300" height="2"/>
                        <color key="backgroundColor" red="0.96078431372549022" green="0.96078431372549022" blue="0.96078431372549022" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="2" id="Fim-eM-Yqv"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="Cfu-aZ-sfk" secondAttribute="trailing" id="R7Y-T4-5if"/>
                    <constraint firstItem="9ZN-yF-ehU" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="20" id="Vmh-tC-GjL"/>
                    <constraint firstItem="9ZN-yF-ehU" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="b4X-TD-Mxi"/>
                    <constraint firstAttribute="trailing" secondItem="vrg-ht-1Sf" secondAttribute="trailing" constant="13" id="cDP-J8-SN3"/>
                    <constraint firstItem="Cfu-aZ-sfk" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="20" id="dRq-Dy-quV"/>
                    <constraint firstAttribute="bottom" secondItem="Cfu-aZ-sfk" secondAttribute="bottom" id="fK9-Rk-egi"/>
                    <constraint firstItem="vrg-ht-1Sf" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="vqY-zN-YlQ"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="nameLbl" destination="9ZN-yF-ehU" id="rmA-of-VLG"/>
                <outlet property="sendBtn" destination="vrg-ht-1Sf" id="ued-GT-gXJ"/>
            </connections>
            <point key="canvasLocation" x="-49" y="46"/>
        </tableViewCell>
    </objects>
</document>
