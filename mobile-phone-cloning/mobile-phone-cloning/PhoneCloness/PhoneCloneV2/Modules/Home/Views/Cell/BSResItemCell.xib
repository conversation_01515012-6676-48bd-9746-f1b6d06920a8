<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="16096" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="BSResItemCell" id="KGk-i7-Jjw" customClass="BSResItemCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <button opaque="NO" tag="2" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="BYJ-6M-eRY" customClass="BSButton">
                        <rect key="frame" x="20" y="12" width="20" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="hX2-9c-Tvb"/>
                            <constraint firstAttribute="width" constant="20" id="wvZ-Xd-Mrt"/>
                        </constraints>
                        <state key="normal" image="choose_icon_nor"/>
                        <state key="selected" image="choose_icon_sel"/>
                        <connections>
                            <action selector="funcBtnAction:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="Fxc-M4-Xex"/>
                        </connections>
                    </button>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="wer-ai-piB">
                        <rect key="frame" x="48" y="12.5" width="39.5" height="19.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="more_icon" translatesAutoresizingMaskIntoConstraints="NO" id="whP-Kc-Zf1">
                        <rect key="frame" x="290" y="15" width="9" height="14"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="14" id="RYu-Q4-b0A"/>
                            <constraint firstAttribute="width" constant="9" id="blg-52-s5X"/>
                        </constraints>
                    </imageView>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="tfn-zM-fDs">
                        <rect key="frame" x="20" y="43" width="298" height="1"/>
                        <color key="backgroundColor" red="0.96078431372549022" green="0.96078431372549022" blue="0.96078431372549022" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="WeT-qx-L4X"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="whP-Kc-Zf1" secondAttribute="trailing" constant="21" id="CQ6-ls-ZtG"/>
                    <constraint firstItem="wer-ai-piB" firstAttribute="leading" secondItem="BYJ-6M-eRY" secondAttribute="trailing" constant="8" id="KWn-Br-HiM"/>
                    <constraint firstAttribute="trailing" secondItem="tfn-zM-fDs" secondAttribute="trailing" constant="2" id="MCk-yf-OXl"/>
                    <constraint firstItem="BYJ-6M-eRY" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="Ss4-mu-jKz"/>
                    <constraint firstItem="wer-ai-piB" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="Voq-qF-Awr"/>
                    <constraint firstItem="tfn-zM-fDs" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="20" id="d5y-1D-Snv"/>
                    <constraint firstItem="BYJ-6M-eRY" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="20" id="mCJ-gm-dhe"/>
                    <constraint firstItem="whP-Kc-Zf1" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="rIe-b9-i7Z"/>
                    <constraint firstAttribute="bottom" secondItem="tfn-zM-fDs" secondAttribute="bottom" id="tbX-h0-Xg8"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="selectBtn" destination="BYJ-6M-eRY" id="9fV-hV-ji5"/>
                <outlet property="titleLbl" destination="wer-ai-piB" id="1w6-Dr-9iH"/>
            </connections>
            <point key="canvasLocation" x="272" y="64"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="choose_icon_nor" width="20" height="20"/>
        <image name="choose_icon_sel" width="20" height="20"/>
        <image name="more_icon" width="9" height="14"/>
    </resources>
</document>
