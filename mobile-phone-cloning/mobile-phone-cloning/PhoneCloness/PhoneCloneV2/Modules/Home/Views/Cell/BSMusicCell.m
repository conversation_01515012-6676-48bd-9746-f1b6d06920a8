//
//  BSMusicCell.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSMusicCell.h"

@interface BSMusicCell ()

@property (weak, nonatomic) IBOutlet UIImageView *coverImageView;
@property (weak, nonatomic) IBOutlet UIButton *selectBtn;
@property (weak, nonatomic) IBOutlet UILabel *titleLbl;

@end

@implementation BSMusicCell

- (void)awakeFromNib {
    [super awakeFromNib];
  
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

}

- (void)setModel:(BSMusic *)model {
    _model = model;
    self.selectBtn.selected = model.isSelected;
    
    self.titleLbl.text = model.infoDict[@"title"];
    
    self.coverImageView.image = model.image;
    
}

@end
