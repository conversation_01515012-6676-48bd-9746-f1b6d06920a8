//
//  BSResItemCell.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSResItemCell.h"

@interface BSResItemCell ()

@property (weak, nonatomic) IBOutlet UIButton *selectBtn;
@property (weak, nonatomic) IBOutlet UILabel *titleLbl;

@end

@implementation BSResItemCell

- (void)awakeFromNib {
    [super awakeFromNib];
    
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

}

- (void)setModel:(BSResGroupModel *)model {
    _model = model;
    
    self.selectBtn.selected = model.isSelected;
    self.titleLbl.text = model.title;
    
}

- (IBAction)funcBtnAction:(id)sender {
   
    if (self.delegate && [self.delegate respondsToSelector:@selector(resItemCell:indexPath:)]) {
        [self.delegate resItemCell:self indexPath:self.indexPath];
    }
    
}


@end
