//
//  BSCalendarCell.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSCalendarCell.h"

@interface BSCalendarCell ()

@property (weak, nonatomic) IBOutlet UIButton *selectBtn;
@property (weak, nonatomic) IBOutlet UILabel *titleLbl;

@end

@implementation BSCalendarCell

- (void)awakeFromNib {
    [super awakeFromNib];
  
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];
    
}

- (void)setModel:(BSCalender *)model {
    _model = model;
    self.selectBtn.selected = model.isSelected;
    
    self.titleLbl.text = model.infoDict[@"title"];
    
}

@end
