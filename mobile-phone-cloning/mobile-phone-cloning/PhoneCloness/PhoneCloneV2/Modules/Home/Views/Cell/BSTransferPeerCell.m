//
//  BSTransferPeerCell.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSTransferPeerCell.h"

@interface BSTransferPeerCell ()

@property (weak, nonatomic) IBOutlet UILabel *nameLbl;
@property (weak, nonatomic) IBOutlet UIButton *sendBtn;

@end

@implementation BSTransferPeerCell

- (void)awakeFromNib {
    [super awakeFromNib];
    
    [self.sendBtn setTitle:NSLocalizedString(@"send", nil) forState:UIControlStateNormal];
    
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

}

- (void)setPeerModel:(BSPeerModel *)peerModel {
    _peerModel = peerModel;
    
    self.nameLbl.text = peerModel.peerID.displayName;
    
}

- (IBAction)sendBtnAction:(id)sender {
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(transferPeerCell:peerModel:)]) {
        [self.delegate transferPeerCell:self peerModel:self.peerModel];
    }
    
}

@end
