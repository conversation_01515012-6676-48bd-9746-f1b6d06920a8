<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="BSCalendarCell" rowHeight="70" id="KGk-i7-Jjw" customClass="BSCalendarCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="70"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="69.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Xta-n6-HSN">
                        <rect key="frame" x="20" y="26" width="37.5" height="18"/>
                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2Vw-CL-Zy1">
                        <rect key="frame" x="19" y="68.5" width="283" height="1"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="DXc-mA-5mg"/>
                        </constraints>
                    </view>
                    <button opaque="NO" userInteractionEnabled="NO" tag="2" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="95u-TW-gNp" customClass="BSButton">
                        <rect key="frame" x="282" y="25" width="20" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="Tof-bi-nzo"/>
                            <constraint firstAttribute="width" constant="20" id="cw9-fe-fbG"/>
                        </constraints>
                        <state key="normal" image="choose_icon_nor"/>
                        <state key="selected" image="choose_icon_sel"/>
                    </button>
                </subviews>
                <constraints>
                    <constraint firstItem="Xta-n6-HSN" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="6Yw-XN-zKI"/>
                    <constraint firstItem="2Vw-CL-Zy1" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="19" id="Bgl-eG-bVy"/>
                    <constraint firstItem="95u-TW-gNp" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="D5y-5y-vRz"/>
                    <constraint firstItem="Xta-n6-HSN" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="20" id="QP3-u2-5ay"/>
                    <constraint firstAttribute="trailing" secondItem="95u-TW-gNp" secondAttribute="trailing" constant="18" id="aoc-yY-ylC"/>
                    <constraint firstAttribute="trailing" secondItem="2Vw-CL-Zy1" secondAttribute="trailing" constant="18" id="kiL-Xs-Kgh"/>
                    <constraint firstAttribute="bottom" secondItem="2Vw-CL-Zy1" secondAttribute="bottom" id="tYy-4z-TcF"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="selectBtn" destination="95u-TW-gNp" id="1FA-RS-xUn"/>
                <outlet property="titleLbl" destination="Xta-n6-HSN" id="ODe-Ma-57a"/>
            </connections>
            <point key="canvasLocation" x="131.8840579710145" y="110.49107142857143"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="choose_icon_nor" width="16" height="16"/>
        <image name="choose_icon_sel" width="16" height="16"/>
    </resources>
</document>
