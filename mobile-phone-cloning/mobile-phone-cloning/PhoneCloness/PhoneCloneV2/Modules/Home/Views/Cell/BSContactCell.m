//
//  BSContactCell.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSContactCell.h"

@interface BSContactCell ()

@property (weak, nonatomic) IBOutlet UIButton *selectBtn;
@property (weak, nonatomic) IBOutlet UILabel *titleLbl;
@property (weak, nonatomic) IBOutlet UILabel *subtitleLbl;

@end

@implementation BSContactCell

- (void)awakeFromNib {
    [super awakeFromNib];
  
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

}

- (void)setModel:(BSContact *)model {
    _model = model;

    self.selectBtn.selected = model.isSelected;
    
    NSString *phone = @"";
    if (model.contact.phoneNumbers.count > 0) {
        CNLabeledValue *labelValue = model.contact.phoneNumbers[0];
        CNPhoneNumber *phoneNumber = labelValue.value;
        phone = phoneNumber.stringValue;
    }
    self.subtitleLbl.text = phone;
    
    NSString *name = [NSString stringWithFormat:@"%@%@", model.contact.familyName, model.contact.givenName];
    if (name.length == 0 && phone.length != 0) {
        name = phone;
        
    }
    self.titleLbl.text = name;
}

@end
