<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="BSContactCell" rowHeight="83" id="KGk-i7-Jjw" customClass="BSContactCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="83"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="82.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gEp-kC-EDv">
                        <rect key="frame" x="20" y="21.5" width="37.5" height="18"/>
                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1Pk-uy-QMm">
                        <rect key="frame" x="20" y="43.5" width="31" height="14.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <color key="textColor" red="0.36470588235294116" green="0.36470588235294116" blue="0.36470588235294116" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <button opaque="NO" userInteractionEnabled="NO" tag="2" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="56c-rQ-1hR" customClass="BSButton">
                        <rect key="frame" x="282" y="31.5" width="20" height="20"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="20" id="cpm-v6-B55"/>
                            <constraint firstAttribute="height" constant="20" id="ouI-2W-XUg"/>
                        </constraints>
                        <state key="normal" image="choose_icon_nor"/>
                        <state key="selected" image="choose_icon_sel"/>
                    </button>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="b6j-BA-w9q">
                        <rect key="frame" x="19" y="81.5" width="283" height="1"/>
                        <color key="backgroundColor" red="0.96862745098039216" green="0.96862745098039216" blue="0.96862745098039216" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="OGP-uv-pJ6"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="1Pk-uy-QMm" firstAttribute="top" secondItem="gEp-kC-EDv" secondAttribute="bottom" constant="4" id="BMm-nk-jwL"/>
                    <constraint firstItem="1Pk-uy-QMm" firstAttribute="leading" secondItem="gEp-kC-EDv" secondAttribute="leading" id="Ggv-wy-SwP"/>
                    <constraint firstItem="b6j-BA-w9q" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="19" id="TtA-jW-RE2"/>
                    <constraint firstItem="gEp-kC-EDv" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="20" id="c94-vB-9fu"/>
                    <constraint firstAttribute="trailing" secondItem="56c-rQ-1hR" secondAttribute="trailing" constant="18" id="gLE-lb-qOk"/>
                    <constraint firstAttribute="trailing" secondItem="b6j-BA-w9q" secondAttribute="trailing" constant="18" id="iug-cF-Qek"/>
                    <constraint firstItem="gEp-kC-EDv" firstAttribute="bottom" secondItem="H2p-sc-9uM" secondAttribute="centerY" constant="-2" id="mEu-Wp-9Qa"/>
                    <constraint firstItem="56c-rQ-1hR" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="pLJ-mT-hPf"/>
                    <constraint firstAttribute="bottom" secondItem="b6j-BA-w9q" secondAttribute="bottom" id="pwD-u5-d0a"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="selectBtn" destination="56c-rQ-1hR" id="VYh-gR-Tjh"/>
                <outlet property="subtitleLbl" destination="1Pk-uy-QMm" id="qJu-Gg-6Gx"/>
                <outlet property="titleLbl" destination="gEp-kC-EDv" id="vhO-Ul-LYh"/>
            </connections>
            <point key="canvasLocation" x="-43.478260869565219" y="100.78125"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="choose_icon_nor" width="20" height="20"/>
        <image name="choose_icon_sel" width="20" height="20"/>
    </resources>
</document>
