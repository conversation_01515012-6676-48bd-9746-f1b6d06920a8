<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" reuseIdentifier="BSMusicCell" rowHeight="158" id="KGk-i7-Jjw" customClass="BSMusicCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="158"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="157.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="ear-9J-auK">
                        <rect key="frame" x="18" y="34" width="90" height="90"/>
                        <color key="backgroundColor" cocoaTouchSystemColor="groupTableViewBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="90" id="Jkp-bD-Wsf"/>
                            <constraint firstAttribute="width" constant="90" id="cPi-Rg-bW9"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="3" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="F8j-2d-E2v">
                        <rect key="frame" x="128" y="70" width="135" height="18"/>
                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <button opaque="NO" userInteractionEnabled="NO" tag="2" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Z1F-rh-vZN" customClass="BSButton">
                        <rect key="frame" x="281" y="69" width="20" height="20"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="20" id="9og-to-NIZ"/>
                            <constraint firstAttribute="height" constant="20" id="yKF-Yf-plE"/>
                        </constraints>
                        <state key="normal" image="choose_icon_nor"/>
                        <state key="selected" image="choose_icon_sel"/>
                    </button>
                </subviews>
                <constraints>
                    <constraint firstItem="Z1F-rh-vZN" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="1ux-BI-Yue"/>
                    <constraint firstItem="ear-9J-auK" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="18" id="49P-8q-xQu"/>
                    <constraint firstItem="F8j-2d-E2v" firstAttribute="leading" secondItem="ear-9J-auK" secondAttribute="trailing" constant="20" id="4eS-JE-h5f"/>
                    <constraint firstItem="ear-9J-auK" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="6Gx-6s-VrV"/>
                    <constraint firstAttribute="trailing" secondItem="Z1F-rh-vZN" secondAttribute="trailing" constant="19" id="Enx-uG-IkO"/>
                    <constraint firstItem="F8j-2d-E2v" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="GLb-xf-hoL"/>
                    <constraint firstItem="Z1F-rh-vZN" firstAttribute="leading" secondItem="F8j-2d-E2v" secondAttribute="trailing" constant="18" id="bEp-8Y-gsf"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="aW0-zy-SZf"/>
            <connections>
                <outlet property="coverImageView" destination="ear-9J-auK" id="dlw-IC-qsM"/>
                <outlet property="selectBtn" destination="Z1F-rh-vZN" id="3vr-eL-Beh"/>
                <outlet property="titleLbl" destination="F8j-2d-E2v" id="fsK-c3-SKo"/>
            </connections>
            <point key="canvasLocation" x="-82.608695652173921" y="96.428571428571431"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="choose_icon_nor" width="20" height="20"/>
        <image name="choose_icon_sel" width="20" height="20"/>
    </resources>
</document>
