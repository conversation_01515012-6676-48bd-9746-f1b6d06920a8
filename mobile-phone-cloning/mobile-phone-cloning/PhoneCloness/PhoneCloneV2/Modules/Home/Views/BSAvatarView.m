//
//  BSAvatarView.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSAvatarView.h"

@interface BSAvatarView ()

@property (weak, nonatomic) IBOutlet UIImageView *iconImageView;
@property (weak, nonatomic) IBOutlet UILabel *nameLbl;

@end

@implementation BSAvatarView

+ (instancetype)getAvatarView {
    return [[[NSBundle mainBundle] loadNibNamed:@"BSAvatarView" owner:nil options:nil] lastObject];
}

- (void)setPeerModel:(BSPeerModel *)peerModel {
    _peerModel = peerModel;
    
    self.nameLbl.text = peerModel.peerID;
    
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    
    for (UIView *subView in self.subviews) {
        subView.alpha = 0.4;
    }
    
}

- (void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    for (UIView *subView in self.subviews) {
        subView.alpha = 1.0;
    }
    
    
    if (self.tapAction) {
        self.tapAction();
    }
}

- (void)touchesCancelled:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    for (UIView *subView in self.subviews) {
        subView.alpha = 1.0;
    }
}


@end
