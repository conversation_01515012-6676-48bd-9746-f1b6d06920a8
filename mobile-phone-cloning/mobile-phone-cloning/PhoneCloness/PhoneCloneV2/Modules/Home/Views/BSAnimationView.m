//
//  BSAnimationView.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSAnimationView.h"

@implementation BSAnimationView

- (void)drawRect:(CGRect)rect {
    
    //半径
    CGFloat redbius =40;
    //开始角度
    CGFloat startAngle = 0;
    //中心点
    CGFloat CGfrom_x=rect.size.width;
    
    CGPoint point = CGPointMake(CGfrom_x/2, CGfrom_x/2);
    //结束角
    CGFloat endAngle = 2*M_PI;
    
    UIBezierPath *path = [UIBezierPath bezierPathWithArcCenter:point radius:redbius startAngle:startAngle endAngle:endAngle clockwise:YES];
    CAShapeLayer *layer = [[CAShapeLayer alloc] init];
    layer.path=path.CGPath;   //添加路径
    layer.strokeColor=[UIColor jk_colorWithHexString:@"#D8D8D8"].CGColor;
    layer.fillColor=[UIColor clearColor].CGColor;
    [self.layer addSublayer:layer];
    
}


@end
