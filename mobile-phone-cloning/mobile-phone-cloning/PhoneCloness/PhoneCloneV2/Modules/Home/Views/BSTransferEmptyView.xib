<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="BSTransferEmptyView">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="transfer_empty" translatesAutoresizingMaskIntoConstraints="NO" id="mOn-nT-Tit">
                    <rect key="frame" x="127.5" y="263" width="120" height="129"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="120" id="BcT-4w-AMN"/>
                        <constraint firstAttribute="height" constant="129" id="xsJ-Np-cBm"/>
                    </constraints>
                </imageView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="没有可连接设备" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="KeQ-BH-RhN">
                    <rect key="frame" x="30" y="418" width="315" height="16"/>
                    <fontDescription key="fontDescription" type="system" pointSize="13"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="请确保两台或以上设备连接" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pvC-7y-IwC">
                    <rect key="frame" x="30" y="437" width="315" height="16"/>
                    <fontDescription key="fontDescription" type="system" pointSize="13"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="pvC-7y-IwC" firstAttribute="top" secondItem="KeQ-BH-RhN" secondAttribute="bottom" constant="3" id="3ad-c9-b7p"/>
                <constraint firstItem="mOn-nT-Tit" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" constant="-6" id="3pm-Zu-GoR"/>
                <constraint firstItem="KeQ-BH-RhN" firstAttribute="leading" secondItem="vUN-kp-3ea" secondAttribute="leading" constant="30" id="7yp-6K-dzE"/>
                <constraint firstItem="vUN-kp-3ea" firstAttribute="trailing" secondItem="pvC-7y-IwC" secondAttribute="trailing" constant="30" id="A0J-Lm-Lw5"/>
                <constraint firstItem="KeQ-BH-RhN" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="I9i-sk-CV8"/>
                <constraint firstItem="mOn-nT-Tit" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="dTf-iy-m8Q"/>
                <constraint firstItem="KeQ-BH-RhN" firstAttribute="top" secondItem="mOn-nT-Tit" secondAttribute="bottom" constant="26" id="dZl-v9-O1g"/>
                <constraint firstItem="pvC-7y-IwC" firstAttribute="leading" secondItem="vUN-kp-3ea" secondAttribute="leading" constant="30" id="txC-QV-LHJ"/>
                <constraint firstItem="vUN-kp-3ea" firstAttribute="trailing" secondItem="KeQ-BH-RhN" secondAttribute="trailing" constant="30" id="xHo-hl-jIg"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <point key="canvasLocation" x="-166" y="138"/>
        </view>
    </objects>
    <resources>
        <image name="transfer_empty" width="120" height="129"/>
    </resources>
</document>
