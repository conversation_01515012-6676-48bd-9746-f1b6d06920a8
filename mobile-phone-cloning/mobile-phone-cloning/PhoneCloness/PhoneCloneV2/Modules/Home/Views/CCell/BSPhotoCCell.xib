<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="BSPhotoCCell" id="gTV-IL-0wX" customClass="BSPhotoCCell">
            <rect key="frame" x="0.0" y="0.0" width="110" height="129"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="110" height="129"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="RaN-ag-2kj">
                        <rect key="frame" x="0.0" y="0.0" width="110" height="129"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                <integer key="value" value="5"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                    <button opaque="NO" tag="2" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="a5z-ty-EGZ" customClass="BSButton">
                        <rect key="frame" x="84" y="6" width="20" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="B9L-m6-okQ"/>
                            <constraint firstAttribute="width" constant="20" id="wGC-gT-tWo"/>
                        </constraints>
                        <state key="normal" image="choose_icon_nor"/>
                        <state key="selected" image="choose_icon_sel"/>
                        <connections>
                            <action selector="funcBtnAction:" destination="gTV-IL-0wX" eventType="touchUpInside" id="lqC-M7-F4h"/>
                        </connections>
                    </button>
                </subviews>
            </view>
            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="a5z-ty-EGZ" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="6" id="2VI-om-Snc"/>
                <constraint firstItem="RaN-ag-2kj" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="4C6-vM-f2P"/>
                <constraint firstAttribute="bottom" secondItem="RaN-ag-2kj" secondAttribute="bottom" id="9rf-Bw-Uda"/>
                <constraint firstItem="RaN-ag-2kj" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="R2L-5o-K2P"/>
                <constraint firstAttribute="trailing" secondItem="a5z-ty-EGZ" secondAttribute="trailing" constant="6" id="rR7-OW-ain"/>
                <constraint firstAttribute="trailing" secondItem="RaN-ag-2kj" secondAttribute="trailing" id="tsZ-Os-RcS"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="SEy-5g-ep8"/>
            <size key="customSize" width="110" height="129"/>
            <connections>
                <outlet property="coverImageView" destination="RaN-ag-2kj" id="rdc-Md-261"/>
                <outlet property="selectBtn" destination="a5z-ty-EGZ" id="F3l-Pr-TlY"/>
            </connections>
            <point key="canvasLocation" x="34.782608695652179" y="43.191964285714285"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="choose_icon_nor" width="24" height="24"/>
        <image name="choose_icon_sel" width="24" height="24"/>
    </resources>
</document>
