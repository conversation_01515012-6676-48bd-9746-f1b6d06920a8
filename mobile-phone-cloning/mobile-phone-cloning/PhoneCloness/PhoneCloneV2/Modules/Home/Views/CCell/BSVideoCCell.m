//
//  BSVideoCCell.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSVideoCCell.h"

@interface BSVideoCCell ()

@property (weak, nonatomic) IBOutlet UIImageView *coverImageView;
@property (weak, nonatomic) IBOutlet UIButton *selectBtn;

@end

@implementation BSVideoCCell

- (void)awakeFromNib {
    [super awakeFromNib];
  
}

- (void)setModel:(TZAssetModel *)model {
    _model = model;
    
    self.selectBtn.selected = model.isSelected;
    
    kWeakSelf
    [[TZImageManager manager] getPhotoWithAsset:model.asset photoWidth:kScreenWidth / 3.0 completion:^(UIImage *photo, NSDictionary *info, BOOL isDegraded) {
        weakSelf.coverImageView.image = photo;
    }];
}

- (IBAction)funcBtnAction:(id)sender {
    if (self.delegate && [self.delegate respondsToSelector:@selector(videoCCell:didSelectedIndexPath:)]) {
        [self.delegate videoCCell:self didSelectedIndexPath:self.indexPath];
    }
}

@end
