<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="BSVideoCCell" id="gTV-IL-0wX" customClass="BSVideoCCell">
            <rect key="frame" x="0.0" y="0.0" width="110" height="112"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="110" height="112"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="9va-Mr-cIS">
                        <rect key="frame" x="0.0" y="0.0" width="110" height="112"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                <integer key="value" value="5"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                    <button opaque="NO" tag="2" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="GZv-OX-aUt" customClass="BSButton">
                        <rect key="frame" x="84" y="6" width="20" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="EVM-mH-7TL"/>
                            <constraint firstAttribute="width" constant="20" id="ezJ-PL-1g1"/>
                        </constraints>
                        <state key="normal" image="choose_icon_nor"/>
                        <state key="selected" image="choose_icon_sel"/>
                        <connections>
                            <action selector="funcBtnAction:" destination="gTV-IL-0wX" eventType="touchUpInside" id="7V5-v5-J8N"/>
                        </connections>
                    </button>
                </subviews>
            </view>
            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="9va-Mr-cIS" secondAttribute="bottom" id="EdK-CV-MOm"/>
                <constraint firstItem="9va-Mr-cIS" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="F7B-Es-AZb"/>
                <constraint firstAttribute="trailing" secondItem="9va-Mr-cIS" secondAttribute="trailing" id="QUH-fd-Dm5"/>
                <constraint firstAttribute="trailing" secondItem="GZv-OX-aUt" secondAttribute="trailing" constant="6" id="cxD-qD-7eq"/>
                <constraint firstItem="9va-Mr-cIS" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="gKi-5X-Vh4"/>
                <constraint firstItem="GZv-OX-aUt" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="6" id="mGE-zC-qVe"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="SEy-5g-ep8"/>
            <size key="customSize" width="110" height="112"/>
            <connections>
                <outlet property="coverImageView" destination="9va-Mr-cIS" id="02U-rH-TYr"/>
                <outlet property="selectBtn" destination="GZv-OX-aUt" id="NKU-2c-AMe"/>
            </connections>
            <point key="canvasLocation" x="-322" y="70"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="choose_icon_nor" width="20" height="20"/>
        <image name="choose_icon_sel" width="20" height="20"/>
    </resources>
</document>
