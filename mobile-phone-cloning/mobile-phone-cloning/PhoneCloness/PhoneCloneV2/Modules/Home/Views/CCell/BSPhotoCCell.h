//
//  BSPhotoCCell.h
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <TZImageManager.h>
@class BSPhotoCCell;

@protocol BSPhotoCCellDelegate <NSObject>

- (void)photoCCell:(BSPhotoCCell *)cell didSelectedIndexPath:(NSIndexPath *)indexPath;

@end

@interface BSPhotoCCell : UICollectionViewCell

@property (nonatomic, strong) TZAssetModel *model;

@property (nonatomic, weak) id<BSPhotoCCellDelegate> delegate;
@property (nonatomic, strong) NSIndexPath *indexPath;

@end

