//
//  BSTransferCalendarFileVC.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSTransferCalendarFileVC.h"

#import "BSCalendarCell.h"
#import "BSCalender.h"

#import <EventKit/EventKit.h>

@interface BSTransferCalendarFileVC () <UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic, strong) EKEventStore *eventStore;

@end

@implementation BSTransferCalendarFileVC

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.eventStore = [[EKEventStore alloc] init];
    
    [self setupUI];
    
    if (self.dataArray.count > 0) {
        BOOL hasNoSelect = NO;
        for (BSCalender *model in self.dataArray) {
            if (!model.isSelected) {
                hasNoSelect = YES;
                break;
            }
        }
        
        self.allSelected = !hasNoSelect;
    }else {
        EKAuthorizationStatus status = [EKEventStore authorizationStatusForEntityType:EKEntityTypeEvent];
        if (status == EKAuthorizationStatusAuthorized) {
            [self requestCalendar];
        }else if (status == EKAuthorizationStatusNotDetermined) {
            kWeakSelf
            [self.eventStore requestAccessToEntityType:EKEntityTypeEvent completion:^(BOOL granted, NSError * _Nullable error) {
                
                dispatch_async(dispatch_get_main_queue(), ^{
                    
                    if (granted) {
                        [weakSelf requestCalendar];
                    }
                    
                });
                
            }];
        }else {
            [self showAlertViewWithFileType:BSFileTypeCalendar];
        }
    }
    
}

- (void)setupUI {
    
    UITableView *tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, self.contentHeight) style:UITableViewStylePlain];
    [tableView registerNib:[UINib nibWithNibName:@"BSCalendarCell" bundle:[NSBundle mainBundle]] forCellReuseIdentifier:@"BSCalendarCell"];
    tableView.contentInset = UIEdgeInsetsMake(0, 0, 60, 0);
    tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    tableView.backgroundColor = [UIColor whiteColor];
    tableView.dataSource = self;
    tableView.delegate = self;
    tableView.rowHeight = 58.0;
    tableView.estimatedSectionHeaderHeight = 0.0;
    tableView.estimatedSectionFooterHeight = 0.0;
    tableView.tableFooterView = [UIView new];
    [self.view addSubview:tableView];
    CGFloat topInset = [UIApplication sharedApplication].keyWindow.safeAreaInsets.top;
    [tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.top.mas_equalTo(self.view.mas_topMargin);
        make.bottom.mas_equalTo(self.view.mas_bottomMargin);
    }];
    self.tableView = tableView;
    
}

- (void)editAction {
    
    self.allSelected = !self.isAllSelected;
    [MobClick event:@"Select_the_file" attributes:@{@"Calendar":@"全选"}];
    if (self.selectStatus && self.dataArray.count > 0) {
        self.selectStatus(self.isAllSelected, self.isAllSelected);
    }
    
    for (BSCalender *model in self.dataArray) {
        model.selected = self.allSelected;
    }
    [self.tableView reloadData];
    
}

- (void)senderAction {
    
    NSMutableArray *dataArray = @[].mutableCopy;
    
    for (BSCalender *model in self.dataArray) {
        if (model.isSelected) {
            [dataArray addObject:model.infoDict];
        }
    }
    
    [[BSSendFileManager manager] sendFileWithPeerModel:self.peerModel datas:dataArray sendFileType:BSFileTypeCalendar];
    
}

- (void)sendDataSuccessed {
    self.allSelected = NO;
    
    if (self.selectStatus) {
        self.selectStatus(self.isAllSelected, self.isAllSelected);
    }
    
    for (BSCalender *model in self.dataArray) {
        model.selected = self.allSelected;
    }
    [self.tableView reloadData];
}

- (void)reciveDataSuccessed:(NSInteger)reciveType {
    if (reciveType == BSFileTypeCalendar) {
        [self requestCalendar];
    }
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArray.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    BSCalendarCell *cell = [tableView dequeueReusableCellWithIdentifier:@"BSCalendarCell" forIndexPath:indexPath];
    cell.model = self.dataArray[indexPath.item];
    return cell;
}


#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [MobClick event:@"Select_the_file" attributes:@{@"Calendar":@"选择某文件"}];
    BSCalender *model = self.dataArray[indexPath.item];
    model.selected = !model.isSelected;
    [self.tableView reloadRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationNone];
    
    BOOL hasNoSelect = NO;
    for (BSCalender *model in self.dataArray) {
        if (!model.isSelected) {
            hasNoSelect = YES;
            break;
        }
    }
    
    self.allSelected = !hasNoSelect;
    
    BOOL hasSelect = NO;
    if (!self.allSelected) {
        for (BSCalender *model in self.dataArray) {
            if (model.isSelected) {
                hasSelect = YES;
                break;
            }
        }
    }else {
        hasSelect = YES;
    }
    
    
    if (self.selectStatus) {
        self.selectStatus(hasSelect, self.allSelected);
    }
    
}

#pragma mark - JXCategoryListContentViewDelegate
- (void)listDidAppear {
    [super listDidAppear];
    
    BOOL hasSelect = NO;
    if (!self.allSelected) {
        for (BSCalender *model in self.dataArray) {
            if (model.isSelected) {
                hasSelect = YES;
                break;
            }
        }
    }else {
        hasSelect = YES;
    }
    
    
    if (self.selectStatus) {
        self.selectStatus(hasSelect, self.allSelected);
    }
}

#pragma mark - Request
- (void)requestCalendar {
    
    NSDate *startDate = [[NSDate date] jk_dateByAddingYears:-1];
    NSDate *endDate = [[NSDate date] jk_dateByAddingYears:2];
    
    NSArray *calendarArray = [self.eventStore calendarsForEntityType:EKEntityTypeEvent];
    NSMutableArray *dataArray = @[].mutableCopy;
    
    for (EKCalendar *calendar in calendarArray) {
        EKCalendarType type = calendar.type;
        if (type == EKCalendarTypeLocal || type == EKCalendarTypeCalDAV) {
            [dataArray addObject:calendar];
        }
        
    }
    
    NSPredicate *predicate = [self.eventStore predicateForEventsWithStartDate:startDate endDate:endDate calendars:dataArray];
    
    NSArray *events = [self.eventStore eventsMatchingPredicate:predicate];
    events = [events sortedArrayUsingSelector:@selector(compareStartDateWithEvent:)];
    
    [self.dataArray removeAllObjects];
    
    for (EKEvent *event in events) {
        
        NSString *startDate = [event.startDate jk_stringWithFormat:@"yyyy-MM-dd HH:mm:ss"];
        NSString *endDate = [event.endDate jk_stringWithFormat:@"yyyy-MM-dd HH:mm:ss"];
        
        NSMutableDictionary *infoDict = [NSMutableDictionary dictionary];
        
        NSDateFormatter *format = [[NSDateFormatter alloc] init];
        [format setDateFormat:@"YYYY-MM-dd HH:mm:ss"];
        infoDict[@"title"] = event.title;
        infoDict[@"startDate"] =  [format stringFromDate:event.startDate];
        infoDict[@"endDate"] =  [format stringFromDate:event.endDate];
        infoDict[@"isAllDay"] = [NSNumber numberWithBool:event.allDay];
        infoDict[@"location"] = event.location;
        infoDict[@"notes"] = event.notes;
        infoDict[@"urlPath"] = event.URL.absoluteString;
        infoDict[@"lastModifiedDate"] =  [format stringFromDate:event.lastModifiedDate];
        infoDict[@"creationDate"] =  [format stringFromDate:event.creationDate];
        
        
        
        BSCalender *model = [[BSCalender alloc] init];
        model.infoDict = infoDict;
        
        [self.dataArray addObject:model];
        
    }
    
    [self.tableView reloadData];
    
}

@end
