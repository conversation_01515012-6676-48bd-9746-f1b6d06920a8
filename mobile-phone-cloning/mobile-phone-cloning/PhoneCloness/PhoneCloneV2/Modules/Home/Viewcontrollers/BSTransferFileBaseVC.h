//
//  BSTransferFileBaseVC.h
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <UIKit/UIKit.h>

#import "BSPeerModel.h"

#import "JXCategoryListContainerView.h"

@interface BSTransferFileBaseVC : UIViewController <JXCategoryListContentViewDelegate>

@property (nonatomic, assign) NSInteger type;
@property (nonatomic, assign) CGFloat contentHeight;

@property (nonatomic, strong) BSPeerModel *peerModel;

@property (nonatomic, assign, getter=isAllSelected) BOOL allSelected;

@property (nonatomic, assign, getter=isAuthDenied) BOOL authDenied;

@property (nonatomic, copy) void(^selectStatus)(BOOL hasSelect, BOOL allSelect);

@property (nonatomic, strong) NSMutableArray *dataArray;

- (void)listDidAppear;
- (void)editAction;
- (void)senderAction;

- (void)sendDataSuccessed;
- (void)reciveDataSuccessed:(NSInteger)reciveType;

- (void)showAlertViewWithFileType:(BSFileType)fileType;

@end

