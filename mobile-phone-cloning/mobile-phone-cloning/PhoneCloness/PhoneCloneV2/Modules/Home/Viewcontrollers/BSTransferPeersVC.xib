<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22154" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22130"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="BSTransferPeersVC">
            <connections>
                <outlet property="contentView" destination="BhM-pM-5VE" id="H2U-sW-myn"/>
                <outlet property="networkStatusLbl" destination="J6O-7l-PjC" id="HQN-fG-xCd"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="home_bg" translatesAutoresizingMaskIntoConstraints="NO" id="Gl9-wx-cLq">
                    <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                </imageView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BhM-pM-5VE">
                    <rect key="frame" x="30.5" y="134.5" width="314" height="314"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="home_wifi" translatesAutoresizingMaskIntoConstraints="NO" id="A6B-Ro-jju">
                            <rect key="frame" x="129.5" y="129.5" width="55" height="55"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="55" id="9Lk-Y2-EYJ"/>
                                <constraint firstAttribute="width" constant="55" id="bcq-eT-gwp"/>
                            </constraints>
                        </imageView>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="A6B-Ro-jju" firstAttribute="centerY" secondItem="BhM-pM-5VE" secondAttribute="centerY" id="2X9-54-Uhz"/>
                        <constraint firstItem="A6B-Ro-jju" firstAttribute="centerX" secondItem="BhM-pM-5VE" secondAttribute="centerX" id="IIM-UG-5O6"/>
                        <constraint firstAttribute="height" constant="314" id="nDl-hC-wex"/>
                        <constraint firstAttribute="width" constant="314" id="oeR-RC-Wck"/>
                    </constraints>
                </view>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="home_tips_w" translatesAutoresizingMaskIntoConstraints="NO" id="q06-k5-1AQ">
                    <rect key="frame" x="108" y="495" width="159" height="100"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="100" id="jzq-Zz-rqK"/>
                    </constraints>
                </imageView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="请在同一网络下进行传输" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="J6O-7l-PjC">
                    <rect key="frame" x="120" y="462.5" width="135" height="14.5"/>
                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <viewLayoutGuide key="safeArea" id="Q5M-cg-NOt"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="q06-k5-1AQ" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="2mX-FA-da8"/>
                <constraint firstItem="BhM-pM-5VE" firstAttribute="centerY" secondItem="i5M-Pr-FkT" secondAttribute="centerY" constant="-42" id="7rb-qq-JaW"/>
                <constraint firstItem="J6O-7l-PjC" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="CMv-xt-pkh"/>
                <constraint firstItem="BhM-pM-5VE" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="DT8-Xw-Cga"/>
                <constraint firstItem="Gl9-wx-cLq" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="DwQ-et-gDf"/>
                <constraint firstItem="J6O-7l-PjC" firstAttribute="top" secondItem="BhM-pM-5VE" secondAttribute="bottom" constant="14" id="Gl8-Gp-Scu"/>
                <constraint firstAttribute="trailing" secondItem="Gl9-wx-cLq" secondAttribute="trailing" id="QPZ-Ur-zS1"/>
                <constraint firstItem="q06-k5-1AQ" firstAttribute="top" secondItem="J6O-7l-PjC" secondAttribute="bottom" constant="18" id="WSc-Vt-JBI"/>
                <constraint firstItem="Gl9-wx-cLq" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="d0M-P7-l0V"/>
                <constraint firstAttribute="bottom" secondItem="Gl9-wx-cLq" secondAttribute="bottom" id="muJ-XS-suG"/>
            </constraints>
            <point key="canvasLocation" x="119" y="127"/>
        </view>
    </objects>
    <resources>
        <image name="home_bg" width="375" height="667"/>
        <image name="home_tips_w" width="159" height="100"/>
        <image name="home_wifi" width="65" height="52"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
