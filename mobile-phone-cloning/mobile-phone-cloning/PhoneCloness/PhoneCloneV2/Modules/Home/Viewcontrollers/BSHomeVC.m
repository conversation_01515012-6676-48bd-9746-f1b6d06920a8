//
//  BSHomeVC.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSHomeVC.h"
#import "BSTransferPeersVC.h"
#import "BSTransferResVC.h"
#import "BSTransferDeviceVC.h"
#import "BSQRCodeScanningVC.h"
#import "BSReceivQRCodeVC.h"

#import "BSResItemCell.h"

#import "BSMusic.h"
#import "BSContact.h"
#import "BSCalender.h"
#import "BSResGroupModel.h"
#import "BSVIPRechargeVC.h"
#import "BSRetSendFileServer.h"

#import <TZImageManager.h>
#import <EventKit/EventKit.h>
#import <Contacts/Contacts.h>
#import <MediaPlayer/MediaPlayer.h>

#import "startLinkViewController.h"



@interface BSHomeVC () <UITableViewDataSource, UITableViewDelegate, BSResItemCellDelegate>

@property (nonatomic, strong) UIButton *sendBtn;

@property (nonatomic, strong) UITableView *tabelView;
@property (nonatomic, strong) NSArray *dataArray;

@property (nonatomic, strong) EKEventStore *eventStore;
@property (nonatomic, strong) CNContactStore *contactStore;

@property (nonatomic, strong) BSRetSendFileServer *sendServer;

@property NSString* host;

@property UIButton* right;
@end

@implementation BSHomeVC

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
    
    kWeakSelf
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [weakSelf scanNearbyPeer];
    });
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(receiveNotificationHandler:) name:@"reciveDataSuccessed" object:nil];
    [self performSelector:@selector(showRequestReview) withObject:nil afterDelay:50];
    
    if (![[NSUserDefaults standardUserDefaults] boolForKey:@"fromscreenshow"]) {
        if (![StoreIAPManager featureVip]&&!AppDelegate.getAppDelegate.showGuide)
        {
            dispatch_async(dispatch_get_global_queue(0, 0), ^{
                
                [MobClick event:@"Subscribe" attributes:@{@"source":@"首页"}];
                int sec = 0;
                while (sec < 20) {
                    if ([[NSUserDefaults standardUserDefaults] objectForKey:@"featureCId"]) {
                        dispatch_async(dispatch_get_main_queue(), ^{
                            BSVIPRechargeVC * placeholder = [BSVIPRechargeVC new];
                            placeholder.from = @"首页";
                            placeholder.modalPresentationStyle = 0;
                            UIViewController *topViewcontroller = [[UIApplication sharedApplication].keyWindow rootViewController];
                            while (topViewcontroller.presentedViewController) {
                                topViewcontroller = topViewcontroller.presentedViewController;
                            }
                            [topViewcontroller presentViewController:placeholder animated:YES completion:nil];
                            
                        });
                        
                        break;
                    }
                    sec++;
                    sleep(1);
                }
                
            });
        }
    }else
    {
        [[NSUserDefaults standardUserDefaults] removeObjectForKey:@"fromscreenshow"];
    }
    
//    [[httpNework shareNetwork]openServer];
    
    [self showBannerView:CGRectMake(0, self.view.jk_bottom-90, kDeviceWidth, 90) size:CGSizeMake(kDeviceWidth, 90)];
    
    
    
}



-(void)showRequestReview
{
    if (@available(iOS 10.3, *)) {
        [[UIApplication sharedApplication].keyWindow endEditing:YES];
        [SKStoreReviewController requestReview];
    }
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    for (BSResGroupModel *resGrp in self.dataArray) {
        
        BOOL hasSelect = NO;
        
        switch (resGrp.resType) {
            case BSResTypePhoto:
            {
                for (TZAssetModel *model in resGrp.resArray) {
                    if (model.isSelected) {
                        hasSelect = YES;
                        break;
                    }
                }
            }
                break;
            case BSResTypeVideo:
            {
                for (TZAssetModel *model in resGrp.resArray) {
                    if (model.isSelected) {
                        hasSelect = YES;
                        break;
                    }
                }
            }
                break;
            case BSResTypeContact:
            {
                for (BSContact *model in resGrp.resArray) {
                    if (model.isSelected) {
                        hasSelect = YES;
                        break;
                    }
                }
            }
                break;
            case BSResTypeMusic:
            {
                for (BSMusic *model in resGrp.resArray) {
                    if (model.isSelected) {
                        hasSelect = YES;
                        break;
                    }
                }
            }
                break;
            case BSResTypeCalendar:
            {
                for (BSCalender *model in resGrp.resArray) {
                    if (model.isSelected) {
                        hasSelect = YES;
                        break;
                    }
                }
            }
                break;
                
            default:
                break;
        }
        
        resGrp.selected = hasSelect;
    }
    
    [self.tabelView reloadData];
    
    [self refreshSendBtnStatus];
    
}

- (void)setupUI {
    
    self.navigationItem.title = NSLocalizedString(@"传输", nil);
    
    UIButton *qrcodeBtn = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, 80, 35)];
    qrcodeBtn.backgroundColor = [UIColor colorWithHexString:@"#3b9af0" alpha:1];
    qrcodeBtn.layer.cornerRadius = 9;
    qrcodeBtn.layer.masksToBounds = YES;
    [qrcodeBtn setImage:[UIImage imageNamed:@"连接流"] forState:0];
    [qrcodeBtn setTitle:local(@"连接") forState:0];
    [qrcodeBtn setTitleColor:[UIColor whiteColor] forState:0];
    [qrcodeBtn addTarget:self action:@selector(qrcodeBtnAction) forControlEvents:UIControlEventTouchUpInside];
    self.right = qrcodeBtn;
    UIView *qrcodeView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 80, 35)];
    [qrcodeView addSubview:qrcodeBtn];
    
    self.navigationItem.rightBarButtonItem = [[UIBarButtonItem alloc] initWithCustomView:qrcodeView];
    
    UITableView *tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight - kStatusBarAndNavgationBarHeight - kTabbarHeight) style:UITableViewStylePlain];
    [tableView registerNib:[UINib nibWithNibName:@"BSResItemCell" bundle:[NSBundle mainBundle]] forCellReuseIdentifier:@"BSResItemCell"];
    tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    tableView.backgroundColor = [UIColor whiteColor];
    tableView.dataSource = self;
    tableView.delegate = self;
    tableView.rowHeight = 50;
    tableView.estimatedSectionHeaderHeight = 0.0;
    tableView.estimatedSectionFooterHeight = 0.0;
    tableView.tableFooterView = [UIView new];
    [self.view addSubview:tableView];
    self.tabelView = tableView;
    
    
    
    
    CGFloat sendBtnX = (kScreenWidth - 154) / 2.0;
    CGFloat sendBtnY = kScreenHeight - kStatusBarAndNavgationBarHeight - 31 - 24 - kTabbarHeight;
    self.sendBtn = [[UIButton alloc] initWithFrame:CGRectMake(sendBtnX, sendBtnY, 154, 31)];
    [self.sendBtn setTitle:NSLocalizedString(@"send", nil) forState:UIControlStateNormal];
    [self.sendBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.sendBtn.backgroundColor = [UIColor jk_colorWithHexString:@"#6FB5F4"];
    self.sendBtn.titleLabel.font = [UIFont systemFontOfSize:13.0];
    self.sendBtn.layer.cornerRadius = 15.5;
    self.sendBtn.layer.masksToBounds = YES;
    self.sendBtn.tag = 1;
    [self.sendBtn addTarget:self action:@selector(sendAction) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.sendBtn];
    self.sendBtn.hidden = YES;
    
}

- (void)refreshSendBtnStatus {
    
    BOOL hasSelect = NO;
    for (BSResGroupModel *model in self.dataArray) {
        if (model.isSelected) {
            hasSelect = YES;
            break;
        }
    }
    
    self.sendBtn.hidden = !hasSelect;
    
}

- (void)sendAction {
    AppDelegate* app = APPDELEGATE;
    
    [MobClick event:@"Select_the_file" attributes:@{@"Send":@"点击发送"}];
    
    
    if(app.currentLinkUrl)
    {
        NSString* host = [app.currentLinkUrl containsString:@"8888"]?app.currentLinkUrl:[NSString stringWithFormat:@"http://%@:8888",app.currentLinkUrl];
        //    [self.nearbyServiceBrowser invitePeer:peerModel.peerID toSession:self.session withContext:[infoDidct mj_JSONData] timeout:30];
        self.host = host;
        BSNewSendServer* send = [BSNewSendServer new];
        [send connectToHost:self.host datas:self.dataArray];
    }
    else
    {
        UITabBarController *tabBarController = (UITabBarController *)[AppDelegate getAppDelegate].window.rootViewController;
        
        BSTransferDeviceVC *transferDeviceVC = [[BSTransferDeviceVC alloc] init];
        transferDeviceVC.modalPresentationStyle = UIModalPresentationOverCurrentContext;
        kWeakSelf
        transferDeviceVC.selectIndex = ^(NSInteger index) {
            [weakSelf sendDataWithDeviceType:index];
        };
        [tabBarController presentViewController:transferDeviceVC animated:NO completion:nil];
    }
    
}

- (void)sendDataWithDeviceType:(NSInteger)type {
    AppDelegate* app = APPDELEGATE;
    
    if(!app.currentLinkUrl)
    {
        //iPhone
        startLinkViewController *vc = [[startLinkViewController alloc] init];
        
        kWeakSelf
        weakObj(vc);
        vc.from = type;
        vc.FileDateCompelete = ^(id  _Nonnull sender) {
            [self.navigationController popViewControllerAnimated:YES];
            [self loadUIWith:sender];
            
            NSString* host = [app.currentLinkUrl containsString:@"8888"]?app.currentLinkUrl:[NSString stringWithFormat:@"http://%@:8888",app.currentLinkUrl];
            //    [self.nearbyServiceBrowser invitePeer:peerModel.peerID toSession:self.session withContext:[infoDidct mj_JSONData] timeout:30];
            self.host = host;
            if([W_PERSISTENT_GET_OBJECT(@"useCount") intValue]>=app.allowUseCount&&![StoreIAPManager featureVip])
            {
                BSVIPRechargeVC *vipRechargeViewCtl = [[BSVIPRechargeVC alloc] init];
                vipRechargeViewCtl.from = @"发送文件";
                vipRechargeViewCtl.modalPresentationStyle = 0;
                [self presentViewController:vipRechargeViewCtl animated:YES completion:nil];
                return;
            }
            else
            {
                int i =  [W_PERSISTENT_GET_OBJECT(@"useCount") intValue];
                i++;
                W_PERSISTENT_SET_OBJECT(@(i), @"useCount");
                BSNewSendServer* send = [BSNewSendServer new];
                [send connectToHost:self.host datas:self.dataArray];
            }
            
        };
        vc.dataArray = [self.dataArray mutableCopy];
        //     }   vc.selPeerSuc = ^(BSPeerModel *peer) {
        //            [weakSelf sendFileWithPeerModel:peer];
        //        };
        
        [self.navigationController pushViewController:vc animated:YES];
    }
    else
    {
        
        NSString* host = [app.currentLinkUrl containsString:@"8888"]?app.currentLinkUrl:[NSString stringWithFormat:@"http://%@:8888",app.currentLinkUrl];
        //    [self.nearbyServiceBrowser invitePeer:peerModel.peerID toSession:self.session withContext:[infoDidct mj_JSONData] timeout:30];
        self.host = host;
        
        
        if([W_PERSISTENT_GET_OBJECT(@"useCount") intValue]>=app.allowUseCount&&![StoreIAPManager featureVip])
        {
            BSVIPRechargeVC *vipRechargeViewCtl = [[BSVIPRechargeVC alloc] init];
            vipRechargeViewCtl.from = @"发送文件";
            vipRechargeViewCtl.modalPresentationStyle = 0;
            [self presentViewController:vipRechargeViewCtl animated:YES completion:nil];
            return;
        }
        else
        {
            int i =  [W_PERSISTENT_GET_OBJECT(@"useCount") intValue];
            i++;
            W_PERSISTENT_SET_OBJECT(@(i), @"useCount");
            BSNewSendServer* send = [BSNewSendServer new];
            [send connectToHost:self.host datas:self.dataArray];
        }
        
    }
    
}


- (void)receiveDataWithDeviceType:(NSInteger)type
{
    AppDelegate* app = APPDELEGATE;
    
    
    //iPhone
    startLinkViewController *vc = [[startLinkViewController alloc] init];
    vc.from = type;
    kWeakSelf
    weakObj(vc);
    vc.FileDateCompelete = ^(id  _Nonnull sender) {
        [self.navigationController popViewControllerAnimated:YES];
        [self loadUIWith:sender];
        
        
        //    [self.nearbyServiceBrowser invitePeer:peerModel.peerID toSession:self.session withContext:[infoDidct mj_JSONData] timeout:30];
        
        
    };
    vc.dataArray = [self.dataArray mutableCopy];
    [self.navigationController pushViewController:vc animated:YES];
    //     }   vc.selPeerSuc = ^(BSPeerModel *peer) {
    //            [weakSelf sendFileWithPeerModel:peer];
    //        };
    
    
}

- (void)loadUIWith:(NSString*)targetName
{
    if(![self.view viewWithTag:666])
    {
        UIView *bottom = [UIView new];
        bottom.backgroundColor = [UIColor whiteColor];
        bottom.tag = 666;
        [self.view addSubview:bottom];
        [bottom mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.mas_equalTo(0);
            make.top.mas_equalTo(self.view.mas_top).offset(kStatusBarAndNavgationBarHeight);
            make.height.mas_equalTo(16*3);
        }];
        
        self.tabelView.frame = CGRectMake(0, 16*3+kStatusBarAndNavgationBarHeight, kScreenWidth, kScreenHeight - kStatusBarAndNavgationBarHeight - kTabbarHeight);
    }
    self.right.hidden = YES;
    UIView* bottom = [self.view viewWithTag:666];
    [bottom bk_eachSubview:^(UIView * _Nonnull subview) {
        [subview removeFromSuperview];
    }];
    UILabel* link = [UILabel createLabelWithTitle:[NSString stringWithFormat:local(@"与%@连接中"),targetName] textColor:[UIColor colorWithHexString:@"333333" alpha:1] textAlignment:NSTextAlignmentLeft font:[UIFont systemFontOfSize:14]];
    [bottom addSubview:link];
    [link mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(0);
        make.left.mas_equalTo(16);
    }];
    NSMutableAttributedString* atr = [[NSMutableAttributedString alloc]initWithString:link.text];
    [atr addAttributes:@{NSForegroundColorAttributeName:[UIColor colorWithHexString:@"#4DD0A7" alpha:1]} range:[link.text rangeOfString:targetName]];
    link.attributedText  =  atr;
    
    UIButton* btn = [UIButton createButtonWithTitle:local(@"断开") color:[UIColor whiteColor] font:[UIFont systemFontOfSize:14]];
    btn.backgroundColor = [UIColor colorWithHexString:@"#3b9af0" alpha:1];
    btn.layer.cornerRadius = 2;
    btn.layer.masksToBounds = YES;
    [bottom addSubview:btn];
    [btn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(0);
        make.right.mas_equalTo(-24);
        make.width.mas_equalTo(48);
        make.height.mas_equalTo(16);
    }];
    [btn bk_whenTapped:^{
        [MobClick event:@"other" attributes:@{@"source":@"断开"}];
        AppDelegate* app = APPDELEGATE;
        app.currentLinkUrl = nil;
        app.currentDeviceName = nil;
        [bottom removeFromSuperview];
        self.right.hidden = NO;
        [[httpNework shareNetwork]closeServer];
        self.tabelView.frame = CGRectMake(0, 0, kScreenWidth, kScreenHeight - kStatusBarAndNavgationBarHeight - kTabbarHeight);
    }];
    
}


- (void)sendFileWithPeerModel:(BSPeerModel *)peerModel {
    [[BSSendFileManager manager] sendFileWithPeerModel:peerModel datas:self.dataArray];
    
}

- (void)scanNearbyPeer {
    [BSSendFileManager manager];
    [BSReciveFileManager manager];
}

- (void)receiveNotificationHandler:(NSNotification *)not {
    
    if ([not.name isEqualToString:@"sendDataSuccessed"]) {
        
        for (BSResGroupModel *model in self.dataArray) {
            model.selected = NO;
            [model.resArray removeAllObjects];
        }
        
        [self.tabelView reloadData];
        
    }else if ([not.name isEqualToString:@"reciveDataSuccessed"]) {
        
        if (not.userInfo) {
            return;
        }
        
        for (BSResGroupModel *model in self.dataArray) {
            model.selected = NO;
            [model.resArray removeAllObjects];
        }
        
        [self.tabelView reloadData];
        
    }
    
}

- (void)scanResultHandler:(NSString *)result {
    
    NSString *ip = [[result componentsSeparatedByString:@"://"] lastObject];
    
    NSString *rule = @"^(1\\d{2}|2[0-4]\\d|25[0-4]|[1-9])\\.+((1\\d{2}|2[0-4]\\d|25[0-5]|\\d)\\.){2}+(1\\d{2}|2[0-4]\\d|25[0-5]|\\d)$";
    NSPredicate * predicate = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", rule];
    
    if (![predicate evaluateWithObject:ip]) {
        //        MSG(NSLocalizedString(@"访问ip地址无效", @"访问ip地址无效"));
        return;
    }
    
    if (![result hasPrefix:@"android://"]) {
        //        MSG(NSLocalizedString(@"连接的是非Android app", @"连接的是非Android app"));
        return;
    }
    
    
    //    self.sendServer = [[BSRetSendFileServer alloc] init];
    //    [self.sendServer  connectToHost:ip datas:self.dataArray];
    NSString* host = [result containsString:@"8888"]?result:[NSString stringWithFormat:@"http://%@:8888",result];
    //    [self.nearbyServiceBrowser invitePeer:peerModel.peerID toSession:self.session withContext:[infoDidct mj_JSONData] timeout:30];
    self.host = host;
    [BSConnectManager shareManager].delegate = self;
    [[BSConnectManager shareManager]joinHttpServer:host];
}
- (void)connectSuccess
{
    //    BSNewSendServer* send = [BSNewSendServer new];
    //    [send connectToHost:self.host datas:self.dataArray];
}



- (void)qrcodeBtnAction {
    
    UITabBarController *tabBarController = (UITabBarController *)[AppDelegate getAppDelegate].window.rootViewController;
    
    BSTransferDeviceVC *transferDeviceVC = [[BSTransferDeviceVC alloc] init];
    transferDeviceVC.modalPresentationStyle = UIModalPresentationOverCurrentContext;
    kWeakSelf
    transferDeviceVC.isPC = YES;
    transferDeviceVC.selectIndex = ^(NSInteger index) {
        [weakSelf receiveDataWithDeviceType:index];
    };
    [tabBarController presentViewController:transferDeviceVC animated:NO completion:nil];
    
    
    //    AppDelegate* app = APPDELEGATE;
    //
    //
    //        //iPhone
    //        startLinkViewController *vc = [[startLinkViewController alloc] init];
    //        kWeakSelf
    //        vc.FileDateCompelete = ^(id  _Nonnull sender) {
    //            [self.navigationController popViewControllerAnimated:YES];
    //            [self loadUIWith:sender];
    //        };
    //        //     }   vc.selPeerSuc = ^(BSPeerModel *peer) {
    //        //            [weakSelf sendFileWithPeerModel:peer];
    //        //        };
    //        [self.navigationController pushViewController:vc animated:YES];
    //    }
    //    else
    //    {
    //
    //        NSString* host = [app.currentLinkUrl containsString:@"8888"]?app.currentLinkUrl:[NSString stringWithFormat:@"http://%@:8888",app.currentLinkUrl];
    //    //    [self.nearbyServiceBrowser invitePeer:peerModel.peerID toSession:self.session withContext:[infoDidct mj_JSONData] timeout:30];
    //        self.host = host;
    //        BSNewSendServer* send = [BSNewSendServer new];
    //        [send connectToHost:self.host datas:self.dataArray];
    //    }
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArray.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    BSResItemCell *cell = [tableView dequeueReusableCellWithIdentifier:@"BSResItemCell" forIndexPath:indexPath];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    cell.model = self.dataArray[indexPath.row];
    cell.delegate = self;
    cell.indexPath = indexPath;
    return cell;
}

#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    BSTransferResVC *vc = [[BSTransferResVC alloc] init];
    vc.defaultSelectedIndex = indexPath.row;
    vc.resGroupArray = self.dataArray;
    switch (indexPath.row) {
        case 0:
            [MobClick event:@"Select_the_file" attributes:@{@"source":@"照片"}];
            break;
        case 1:
            [MobClick event:@"Select_the_file" attributes:@{@"source":@"视频"}];
            break;
        case 2:
            [MobClick event:@"Select_the_file" attributes:@{@"source":@"联系人"}];
            break;
        case 3:
            [MobClick event:@"Select_the_file" attributes:@{@"source":@"音乐"}];
            break;
        case 4:
            [MobClick event:@"Select_the_file" attributes:@{@"source":@"日历"}];
            break;
            
        default:
            break;
    }
    
    [self.navigationController pushViewController:vc animated:YES];
    
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

#pragma mark - BSResItemCellDelegate
- (void)resItemCell:(BSResItemCell *)cell indexPath:(NSIndexPath *)indexPath {
    
    BSResGroupModel *model = self.dataArray[indexPath.row];
    model.selected = !model.selected;
    [model.resArray removeAllObjects];
    
    if (model.isSelected) {
        [self checkResPermission:model];
    }
    
    if (model.resArray.count == 0) {
        model.selected = NO;
    }
    
    [self.tabelView reloadData];
    
    [self refreshSendBtnStatus];
}

- (void)refreshResGroup:(BSResGroupModel *)model {
    
    switch (model.resType) {
        case BSResTypePhoto:
        {
            [self requestPhotos];
        }
            break;
        case BSResTypeVideo:
        {
            [self requestVideos];
        }
            break;
        case BSResTypeContact:
        {
            [self requestContact];
        }
            break;
        case BSResTypeMusic:
        {
            [self requestMusic];
        }
            break;
        case BSResTypeCalendar:
        {
            [self requestCalendar];
        }
            break;
            
        default:
            break;
    }
    
}

#pragma mark - Permission
- (void)checkResPermission:(BSResGroupModel *)resGroup {
    
    switch (resGroup.resType) {
        case BSResTypePhoto:
        {
            //相册权限
            PHAuthorizationStatus status = [PHPhotoLibrary authorizationStatus];
            if (status == PHAuthorizationStatusAuthorized) {
                [self refreshResGroup:resGroup];
                
            }else if (status == PHAuthorizationStatusNotDetermined) {
                kWeakSelf
                [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        
                        if (status == PHAuthorizationStatusAuthorized) {
                            [weakSelf refreshResGroup:resGroup];
                        }
                        
                    });
                }];
                
            }else {
                [self showAlertViewWithResType:BSResTypePhoto];
            }
            
        }
            break;
        case BSResTypeVideo:
        {
            //视频权限
            PHAuthorizationStatus status = [PHPhotoLibrary authorizationStatus];
            if (status == PHAuthorizationStatusAuthorized) {
                [self refreshResGroup:resGroup];
                
            }else if (status == PHAuthorizationStatusNotDetermined) {
                kWeakSelf
                [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        
                        if (status == PHAuthorizationStatusAuthorized) {
                            [weakSelf refreshResGroup:resGroup];
                        }
                        
                    });
                }];
                
            }else {
                [self showAlertViewWithResType:BSResTypePhoto];
            }
        }
            break;
        case BSResTypeContact:
        {
            //联系人权限
            CNAuthorizationStatus status = [CNContactStore authorizationStatusForEntityType:CNEntityTypeContacts];
            if (status == CNAuthorizationStatusAuthorized) {
                [self refreshResGroup:resGroup];
            }else if (status == CNAuthorizationStatusNotDetermined) {
                
                kWeakSelf
                [self.contactStore requestAccessForEntityType:CNEntityTypeContacts
                                            completionHandler:^(BOOL granted, NSError * _Nullable error) {
                    
                    dispatch_async(dispatch_get_main_queue(), ^{
                        if (granted) {
                            [weakSelf refreshResGroup:resGroup];
                        }
                    });
                    
                }];
                
            }else {
                [self showAlertViewWithResType:BSResTypePhoto];
            }
            
        }
            break;
        case BSResTypeMusic:
        {
            //音乐权限
            MPMediaLibraryAuthorizationStatus status = [MPMediaLibrary authorizationStatus];
            if (status == MPMediaLibraryAuthorizationStatusAuthorized) {
                [self refreshResGroup:resGroup];
            }else if (status == MPMediaLibraryAuthorizationStatusNotDetermined) {
                
                kWeakSelf
                [MPMediaLibrary requestAuthorization:^(MPMediaLibraryAuthorizationStatus status) {
                    
                    if (status == MPMediaLibraryAuthorizationStatusAuthorized) {
                        dispatch_async(dispatch_get_main_queue(), ^{
                            [weakSelf refreshResGroup:resGroup];
                        });
                    }
                    
                }];
                
            }else {
                [self showAlertViewWithResType:BSResTypeMusic];
            }
            
        }
            break;
        case BSResTypeCalendar:
        {
            //日历权限
            EKAuthorizationStatus status = [EKEventStore authorizationStatusForEntityType:EKEntityTypeEvent];
            if (status == EKAuthorizationStatusAuthorized) {
                [self refreshResGroup:resGroup];
            }else if (status == EKAuthorizationStatusNotDetermined) {
                kWeakSelf
                [self.eventStore requestAccessToEntityType:EKEntityTypeEvent completion:^(BOOL granted, NSError * _Nullable error) {
                    
                    dispatch_async(dispatch_get_main_queue(), ^{
                        
                        if (granted) {
                            [weakSelf refreshResGroup:resGroup];
                        }
                        
                    });
                    
                }];
            }else {
                [self showAlertViewWithResType:BSResTypeMusic];
            }
        }
            break;
            
        default:
            break;
    }
    
}


- (void)showAlertViewWithResType:(BSResType)resType {
    
    NSString *title;
    NSString *message;
    
    switch (resType) {
        case BSResTypePhoto:
        {
            //相册权限
            title = NSLocalizedString(@"请授权相册权限", nil);
            message = NSLocalizedString(@"请在iPhone的\"设置-隐私-相册\"选项中,允许App访问你的相册", nil);
        }
            break;
        case BSResTypeVideo:
        {
            //视频权限
            title = NSLocalizedString(@"请授权相册权限", nil);
            message = NSLocalizedString(@"请在iPhone的\"设置-隐私-相册\"选项中,允许App访问你的相册", nil);
        }
            break;
        case BSResTypeContact:
        {
            //通讯录权限
            title = NSLocalizedString(@"请授权通讯录权限", nil);
            message = NSLocalizedString(@"请在iPhone的\"设置-隐私-通讯录\"选项中,允许App访问你的通讯录", nil);
        }
            break;
        case BSResTypeMusic:
        {
            //多媒体权限
            title = NSLocalizedString(@"请授权媒体与Apple Music权限", nil);
            message = NSLocalizedString(@"请在iPhone的\"设置-隐私-媒体与Apple Music\"选项中,允许App访问你的媒体与Apple Music", nil);
        }
            break;
        case BSResTypeCalendar:
        {
            //日历权限
            title = NSLocalizedString(@"请授权日历权限", nil);
            message = NSLocalizedString(@"请在iPhone的\"设置-隐私-日历\"选项中,允许App访问你的日历", nil);
        }
            break;
            
        default:
            break;
    }
    
    if (!title) {
        return;
    }
    
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:title message:message preferredStyle: UIAlertControllerStyleAlert];
    
    kWeakSelf
    UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"gotIt", @"知道了") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        [weakSelf.navigationController popViewControllerAnimated:YES];
    }];
    [alertController addAction:cancleAction];
    
    UIAlertAction *settingAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"toSettings", @"去设置") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        
        NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
        if ([[UIApplication sharedApplication] canOpenURL:url]) {
            [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
        }
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.25 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [weakSelf.navigationController popViewControllerAnimated:YES];
        });
        
    }];
    
    [alertController addAction:settingAction];
    
    UITabBarController *tabBarController = (UITabBarController *)[AppDelegate getAppDelegate].window.rootViewController;
    UINavigationController *navController = (UINavigationController *)tabBarController.viewControllers[tabBarController.selectedIndex];
    
    [navController presentViewController:alertController animated:YES completion:nil];
    
}

#pragma mark - Request
- (void)requestPhotos {
    
    kWeakSelf
    [[TZImageManager manager] getCameraRollAlbum:NO allowPickingImage:YES needFetchAssets:NO completion:^(TZAlbumModel *model) {
        
        [[TZImageManager manager] getAssetsFromFetchResult:model.result allowPickingVideo:NO allowPickingImage:YES completion:^(NSArray<TZAssetModel *> *models) {
            
            for (BSResGroupModel *resGroup in weakSelf.dataArray) {
                if (resGroup.resType == BSResTypePhoto) {
                    
                    for (TZAssetModel *model in models) {
                        if (model.type == TZAssetModelMediaTypePhoto) {
                            model.isSelected = YES;
                            [resGroup.resArray addObject:model];
                        }
                    }
                    
                    break;
                }
            }
            
        }];
        
    }];
    
    
}

- (void)requestVideos {
    
    kWeakSelf
    [[TZImageManager manager] getCameraRollAlbum:YES allowPickingImage:NO needFetchAssets:NO completion:^(TZAlbumModel *model) {
        
        [[TZImageManager manager] getAssetsFromFetchResult:model.result allowPickingVideo:YES allowPickingImage:NO completion:^(NSArray<TZAssetModel *> *models) {
            
            for (BSResGroupModel *resGroup in weakSelf.dataArray) {
                if (resGroup.resType == BSResTypeVideo) {
                    
                    for (TZAssetModel *model in models) {
                        if (model.type == TZAssetModelMediaTypeVideo) {
                            model.isSelected = YES;
                            [resGroup.resArray addObject:model];
                        }
                    }
                    
                    break;
                }
            }
            
        }];
        
    }];
    
    
}

- (void)requestMusic {
    
    NSMutableArray *dataArray;
    for (BSResGroupModel *resGroup in self.dataArray) {
        if (resGroup.resType == BSResTypeMusic) {
            dataArray = resGroup.resArray;
            break;
        }
    }
    
    MPMediaQuery *query = [[MPMediaQuery alloc] init];
    MPMediaPropertyPredicate *albumNamePredicate = [MPMediaPropertyPredicate predicateWithValue:[NSNumber numberWithInt:MPMediaTypeMusic] forProperty:MPMediaItemPropertyMediaType];
    [query addFilterPredicate:albumNamePredicate];
    
    NSArray *items = [query items];
    
    for (MPMediaItem *item in items) {
        
        NSString *title = [item valueForProperty:MPMediaItemPropertyTitle];
        NSURL *fileURL = [item valueForProperty:MPMediaItemPropertyAssetURL];
        NSString *singer = [item valueForProperty:MPMediaItemPropertyArtist];
        
        if (!singer) {
            singer = @"未知歌手";
        }
        
        MPMediaItemArtwork *artwork = [item valueForProperty:MPMediaItemPropertyArtwork];
        
        UIImage *image;
        if (artwork) {
            image = [artwork imageWithSize:CGSizeMake(72, 72)];
        }
        
        if (fileURL && title && singer) {
            NSDictionary *infoDict = @{
                @"title":title,
                @"fileURL":fileURL,
                @"singer":singer
            };
            BSMusic *music = [[BSMusic alloc] init];
            music.infoDict = infoDict;
            music.image = image;
            music.selected = YES;
            [dataArray addObject:music];
        }
        
    }
    
}

- (void)requestContact {
    
    NSMutableArray *dataArray;
    for (BSResGroupModel *resGroup in self.dataArray) {
        if (resGroup.resType == BSResTypeContact) {
            dataArray = resGroup.resArray;
            break;
        }
    }
    
    NSArray *keys = @[CNContactIdentifierKey, CNContactGivenNameKey, CNContactFamilyNameKey, CNContactPhoneNumbersKey, CNContactImageDataAvailableKey, CNContactThumbnailImageDataKey];
    CNContactFetchRequest *fetchRequest = [[CNContactFetchRequest alloc] initWithKeysToFetch:keys];
    
    [self.contactStore enumerateContactsWithFetchRequest:fetchRequest error:nil usingBlock:^(CNContact * _Nonnull contact, BOOL * _Nonnull stop) {
        
        BSContact *model = [[BSContact alloc] init];
        model.contact = contact;
        model.selected = YES;
        [dataArray addObject:model];
        
    }];
    
}

- (void)requestCalendar {
    
    NSMutableArray *dataArray;
    for (BSResGroupModel *resGroup in self.dataArray) {
        if (resGroup.resType == BSResTypeCalendar) {
            dataArray = resGroup.resArray;
            break;
        }
    }
    
    NSDate *startDate = [[NSDate date] jk_dateByAddingYears:-1];
    NSDate *endDate = [[NSDate date] jk_dateByAddingYears:2];
    
    NSArray *calendarArray = [self.eventStore calendarsForEntityType:EKEntityTypeEvent];
    
    NSMutableArray *result = @[].mutableCopy;
    for (EKCalendar *calendar in calendarArray) {
        EKCalendarType type = calendar.type;
        if (type == EKCalendarTypeLocal || type == EKCalendarTypeCalDAV) {
            [result addObject:calendar];
        }
        
    }
    
    NSPredicate *predicate = [self.eventStore predicateForEventsWithStartDate:startDate endDate:endDate calendars:result];
    
    NSArray *events = [self.eventStore eventsMatchingPredicate:predicate];
    events = [events sortedArrayUsingSelector:@selector(compareStartDateWithEvent:)];
    
    for (EKEvent *event in events) {
        
        NSString *startDate = [event.startDate jk_stringWithFormat:@"yyyy-MM-dd HH:mm:ss"];
        NSString *endDate = [event.endDate jk_stringWithFormat:@"yyyy-MM-dd HH:mm:ss"];
        
        NSMutableDictionary *infoDict =[NSMutableDictionary dictionary];
        
        NSDateFormatter *format = [[NSDateFormatter alloc] init];
        [format setDateFormat:@"YYYY-MM-dd HH:mm:ss"];
        infoDict[@"title"] = event.title;
        infoDict[@"startDate"] =  [format stringFromDate:event.startDate];
        infoDict[@"endDate"] =  [format stringFromDate:event.endDate];
        infoDict[@"isAllDay"] = [NSNumber numberWithBool:event.allDay];
        infoDict[@"location"] = event.location;
        infoDict[@"notes"] = event.notes;
        infoDict[@"urlPath"] = event.URL.absoluteString;
        infoDict[@"lastModifiedDate"] =  [format stringFromDate:event.lastModifiedDate];
        infoDict[@"creationDate"] =  [format stringFromDate:event.creationDate];
        
        BSCalender *model = [[BSCalender alloc] init];
        model.infoDict = infoDict;
        model.selected = YES;
        [dataArray addObject:model];
        
    }
    
}


#pragma mark - Getter
- (NSArray *)dataArray {
    if (!_dataArray) {
        
        NSMutableArray *iDataArray = @[].mutableCopy;
        
        //Photo
        BSResGroupModel *photoResGrp = [[BSResGroupModel alloc] init];
        photoResGrp.resType = BSResTypePhoto;
        photoResGrp.title = NSLocalizedString(@"Photos", nil);
        [iDataArray addObject:photoResGrp];
        
        //Video
        BSResGroupModel *videoResGrp = [[BSResGroupModel alloc] init];
        videoResGrp.resType = BSResTypeVideo;
        videoResGrp.title = NSLocalizedString(@"Videos", nil);
        [iDataArray addObject:videoResGrp];
        
        //Contact
        BSResGroupModel *contactResGrp = [[BSResGroupModel alloc] init];
        contactResGrp.resType = BSResTypeContact;
        contactResGrp.title = NSLocalizedString(@"contacts", nil);
        [iDataArray addObject:contactResGrp];
        
        //Music
        BSResGroupModel *musicResGrp = [[BSResGroupModel alloc] init];
        musicResGrp.resType = BSResTypeMusic;
        musicResGrp.title = NSLocalizedString(@"music", nil);
        [iDataArray addObject:musicResGrp];
        
        //Photo
        BSResGroupModel *calendarResGrp = [[BSResGroupModel alloc] init];
        calendarResGrp.resType = BSResTypeCalendar;
        calendarResGrp.title = NSLocalizedString(@"calendar", nil);
        [iDataArray addObject:calendarResGrp];
        
        _dataArray = iDataArray.copy;
        
    }
    return _dataArray;
}

- (EKEventStore *)eventStore {
    if (!_eventStore) {
        _eventStore = [[EKEventStore alloc] init];
    }
    return _eventStore;
}

- (CNContactStore *)contactStore {
    if (!_contactStore) {
        _contactStore = [[CNContactStore alloc] init];
    }
    return _contactStore;
}




@end
