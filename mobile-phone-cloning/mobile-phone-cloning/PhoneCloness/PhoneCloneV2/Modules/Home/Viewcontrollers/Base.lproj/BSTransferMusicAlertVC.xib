<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="BSTransferMusicAlertVC">
            <connections>
                <outlet property="view" destination="iN0-l3-epB" id="FGo-L4-Cme"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vce-gz-IUc">
                    <rect key="frame" x="62.5" y="204" width="250" height="203"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LuK-wP-46s">
                            <rect key="frame" x="18" y="22" width="214" height="109"/>
                            <string key="text">选择需要发送的音乐保存至“文件”。 在发送端的手机里选择需要发送的音乐，点击发送。
接收端收到音乐文件后将会存储在“文件”中。
通过第三方音乐App读取存储于“文件”中的音乐列表并播放。</string>
                            <fontDescription key="fontDescription" type="system" pointSize="13"/>
                            <color key="textColor" red="0.21176470589999999" green="0.21176470589999999" blue="0.21176470589999999" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="XcD-ef-k7p">
                            <rect key="frame" x="0.0" y="161" width="250" height="42"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="aze-zs-IQd">
                                    <rect key="frame" x="0.0" y="0.0" width="250" height="1"/>
                                    <color key="backgroundColor" red="0.94117647059999998" green="0.94117647059999998" blue="0.94117647059999998" alpha="1" colorSpace="calibratedRGB"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="1" id="4zH-2T-WQK"/>
                                    </constraints>
                                </view>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="rKE-zO-HuX">
                                    <rect key="frame" x="0.0" y="1" width="250" height="41"/>
                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                                    <state key="normal" title="了解">
                                        <color key="titleColor" red="0.23921568630000001" green="0.60784313729999995" blue="0.94117647059999998" alpha="1" colorSpace="calibratedRGB"/>
                                    </state>
                                    <connections>
                                        <action selector="btnAction:" destination="-1" eventType="touchUpInside" id="9Yb-pQ-FRN"/>
                                    </connections>
                                </button>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="aze-zs-IQd" secondAttribute="trailing" id="Eq3-Md-hdZ"/>
                                <constraint firstItem="rKE-zO-HuX" firstAttribute="leading" secondItem="XcD-ef-k7p" secondAttribute="leading" id="Jai-bw-Y8n"/>
                                <constraint firstAttribute="bottom" secondItem="rKE-zO-HuX" secondAttribute="bottom" id="LaW-SY-6zL"/>
                                <constraint firstAttribute="height" constant="42" id="pwA-QA-LCo"/>
                                <constraint firstItem="aze-zs-IQd" firstAttribute="leading" secondItem="XcD-ef-k7p" secondAttribute="leading" id="sSS-ma-pct"/>
                                <constraint firstItem="aze-zs-IQd" firstAttribute="top" secondItem="XcD-ef-k7p" secondAttribute="top" id="srz-fc-wiC"/>
                                <constraint firstItem="rKE-zO-HuX" firstAttribute="top" secondItem="aze-zs-IQd" secondAttribute="bottom" id="twL-NW-0E0"/>
                                <constraint firstAttribute="trailing" secondItem="rKE-zO-HuX" secondAttribute="trailing" id="ud0-BZ-Nir"/>
                            </constraints>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="250" id="6Rl-U0-gPN"/>
                        <constraint firstItem="LuK-wP-46s" firstAttribute="leading" secondItem="vce-gz-IUc" secondAttribute="leading" constant="18" id="AWF-sY-dOl"/>
                        <constraint firstAttribute="trailing" secondItem="XcD-ef-k7p" secondAttribute="trailing" id="C8n-pi-4Xc"/>
                        <constraint firstAttribute="trailing" secondItem="LuK-wP-46s" secondAttribute="trailing" constant="18" id="FWA-Rw-rp3"/>
                        <constraint firstItem="LuK-wP-46s" firstAttribute="top" secondItem="vce-gz-IUc" secondAttribute="top" constant="22" id="Fhq-8F-5ZP"/>
                        <constraint firstItem="XcD-ef-k7p" firstAttribute="leading" secondItem="vce-gz-IUc" secondAttribute="leading" id="ikj-QY-2gZ"/>
                        <constraint firstItem="XcD-ef-k7p" firstAttribute="top" secondItem="LuK-wP-46s" secondAttribute="bottom" constant="30" id="kGW-NA-TqJ"/>
                        <constraint firstAttribute="bottom" secondItem="XcD-ef-k7p" secondAttribute="bottom" id="po7-w7-fq0"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="4"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                    </userDefinedRuntimeAttributes>
                </view>
            </subviews>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="vce-gz-IUc" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="INU-1Y-TLn"/>
                <constraint firstItem="vce-gz-IUc" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" constant="-28" id="zK5-Kb-J3x"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
        </view>
    </objects>
</document>
