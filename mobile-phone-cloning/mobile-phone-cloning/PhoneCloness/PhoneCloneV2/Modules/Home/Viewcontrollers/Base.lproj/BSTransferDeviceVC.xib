<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22154" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22130"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="BSTransferDeviceVC">
            <connections>
                <outlet property="heightConstant" destination="60Q-9S-GmV" id="ugf-bo-3ug"/>
                <outlet property="pcView" destination="lsk-Nv-sFg" id="ipu-y1-2Wg"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="z8q-DB-K4U">
                    <rect key="frame" x="0.0" y="607" width="375" height="60"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="60" id="aef-LR-mYJ"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rYS-p2-5Gd">
                    <rect key="frame" x="0.0" y="455" width="375" height="212"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="LlE-3i-6nE">
                            <rect key="frame" x="63" y="136" width="249" height="46"/>
                            <subviews>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ios_icon" translatesAutoresizingMaskIntoConstraints="NO" id="r9z-wx-7L7">
                                    <rect key="frame" x="84" y="13" width="20" height="20"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="20" id="A8B-jE-EUZ"/>
                                        <constraint firstAttribute="width" constant="20" id="RWb-yG-MaF"/>
                                    </constraints>
                                </imageView>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="苹果手机" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8Ix-1t-gLt">
                                    <rect key="frame" x="112" y="14.5" width="57.5" height="17"/>
                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <button opaque="NO" tag="2" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="1hR-sY-5fz">
                                    <rect key="frame" x="0.0" y="0.0" width="249" height="46"/>
                                    <connections>
                                        <action selector="funcBtnAction:" destination="-1" eventType="touchUpInside" id="QfJ-rj-3cd"/>
                                    </connections>
                                </button>
                            </subviews>
                            <color key="backgroundColor" red="0.23137254901960785" green="0.58039215686274503" blue="0.9137254901960784" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstItem="r9z-wx-7L7" firstAttribute="centerY" secondItem="LlE-3i-6nE" secondAttribute="centerY" id="1ne-RR-9IB"/>
                                <constraint firstAttribute="height" constant="46" id="CRk-0W-YQg"/>
                                <constraint firstItem="8Ix-1t-gLt" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="r9z-wx-7L7" secondAttribute="trailing" constant="8" symbolic="YES" id="CSf-SR-Ojq"/>
                                <constraint firstItem="1hR-sY-5fz" firstAttribute="leading" secondItem="LlE-3i-6nE" secondAttribute="leading" id="DOg-lQ-XPI"/>
                                <constraint firstAttribute="width" constant="249" id="Qcc-qR-AMd"/>
                                <constraint firstAttribute="bottom" secondItem="1hR-sY-5fz" secondAttribute="bottom" id="gBg-wf-Dwf"/>
                                <constraint firstAttribute="trailing" secondItem="1hR-sY-5fz" secondAttribute="trailing" id="m3J-P7-Flx"/>
                                <constraint firstItem="r9z-wx-7L7" firstAttribute="leading" secondItem="LlE-3i-6nE" secondAttribute="leading" constant="84" id="qeF-w8-anu"/>
                                <constraint firstItem="1hR-sY-5fz" firstAttribute="top" secondItem="LlE-3i-6nE" secondAttribute="top" id="qpn-ae-kvZ"/>
                                <constraint firstItem="8Ix-1t-gLt" firstAttribute="centerY" secondItem="LlE-3i-6nE" secondAttribute="centerY" id="uMM-qf-xGc"/>
                                <constraint firstAttribute="trailing" secondItem="8Ix-1t-gLt" secondAttribute="trailing" constant="79.5" id="xRw-qU-RPP"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="23"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                            </userDefinedRuntimeAttributes>
                        </view>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2V7-S6-9e5">
                            <rect key="frame" x="63" y="66" width="249" height="46"/>
                            <subviews>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="android_icon" translatesAutoresizingMaskIntoConstraints="NO" id="suD-ci-Xfs">
                                    <rect key="frame" x="84" y="13" width="20" height="20"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="20" id="aRe-l1-Fiu"/>
                                        <constraint firstAttribute="height" constant="20" id="i0o-o6-s4I"/>
                                    </constraints>
                                </imageView>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="安卓手机" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Si4-yi-twK">
                                    <rect key="frame" x="112" y="14.5" width="57.5" height="17"/>
                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <button opaque="NO" tag="1" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qP3-8c-qZH">
                                    <rect key="frame" x="0.0" y="0.0" width="249" height="46"/>
                                    <connections>
                                        <action selector="funcBtnAction:" destination="-1" eventType="touchUpInside" id="ExG-L4-aJR"/>
                                    </connections>
                                </button>
                            </subviews>
                            <color key="backgroundColor" red="0.21176470588235294" green="0.81568627450980391" blue="0.6588235294117647" alpha="1" colorSpace="calibratedRGB"/>
                            <constraints>
                                <constraint firstAttribute="bottom" secondItem="qP3-8c-qZH" secondAttribute="bottom" id="084-EF-t3X"/>
                                <constraint firstItem="Si4-yi-twK" firstAttribute="centerY" secondItem="2V7-S6-9e5" secondAttribute="centerY" id="3Ql-GY-Cuc"/>
                                <constraint firstAttribute="height" constant="46" id="87i-TL-OHE"/>
                                <constraint firstAttribute="trailing" secondItem="qP3-8c-qZH" secondAttribute="trailing" id="NTj-Le-1Rw"/>
                                <constraint firstAttribute="trailing" secondItem="Si4-yi-twK" secondAttribute="trailing" constant="79.5" id="OCb-s2-aiM"/>
                                <constraint firstItem="Si4-yi-twK" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="suD-ci-Xfs" secondAttribute="trailing" constant="8" symbolic="YES" id="Wp3-Js-22w"/>
                                <constraint firstAttribute="width" constant="249" id="kNn-5b-q6H"/>
                                <constraint firstItem="qP3-8c-qZH" firstAttribute="top" secondItem="2V7-S6-9e5" secondAttribute="top" id="lC4-KJ-d3o"/>
                                <constraint firstItem="qP3-8c-qZH" firstAttribute="leading" secondItem="2V7-S6-9e5" secondAttribute="leading" id="m5C-uh-ctQ"/>
                                <constraint firstItem="suD-ci-Xfs" firstAttribute="centerY" secondItem="2V7-S6-9e5" secondAttribute="centerY" id="uvn-NN-D8o"/>
                                <constraint firstItem="suD-ci-Xfs" firstAttribute="leading" secondItem="2V7-S6-9e5" secondAttribute="leading" constant="84" id="yoV-S5-B6G"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="23"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                            </userDefinedRuntimeAttributes>
                        </view>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="PrF-fa-b1e">
                            <rect key="frame" x="0.0" y="0.0" width="375" height="52"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="另一台设备是？" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hUH-o7-3Xd">
                                    <rect key="frame" x="137.5" y="17" width="100" height="18"/>
                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                                    <nil key="textColor"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="PU5-v6-Z1i" customClass="BSButton">
                                    <rect key="frame" x="344" y="18.5" width="15" height="15"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="15" id="QKq-Y5-GYm"/>
                                        <constraint firstAttribute="height" constant="15" id="S3g-cc-Glo"/>
                                    </constraints>
                                    <state key="normal" image="close_icon"/>
                                    <connections>
                                        <action selector="backAction:" destination="-1" eventType="touchUpInside" id="gnz-bI-vER"/>
                                    </connections>
                                </button>
                            </subviews>
                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            <constraints>
                                <constraint firstItem="hUH-o7-3Xd" firstAttribute="centerX" secondItem="PrF-fa-b1e" secondAttribute="centerX" id="KzV-yL-Stm"/>
                                <constraint firstAttribute="trailing" secondItem="PU5-v6-Z1i" secondAttribute="trailing" constant="16" id="Me1-2i-9ZF"/>
                                <constraint firstItem="hUH-o7-3Xd" firstAttribute="centerY" secondItem="PrF-fa-b1e" secondAttribute="centerY" id="Vtn-6n-zE8"/>
                                <constraint firstAttribute="height" constant="52" id="sO5-CE-pKe"/>
                                <constraint firstItem="PU5-v6-Z1i" firstAttribute="centerY" secondItem="PrF-fa-b1e" secondAttribute="centerY" id="yFa-FR-pOE"/>
                            </constraints>
                        </view>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lsk-Nv-sFg">
                            <rect key="frame" x="63" y="-4" width="249" height="46"/>
                            <subviews>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="pc_icon" translatesAutoresizingMaskIntoConstraints="NO" id="0Kx-Fb-gNA">
                                    <rect key="frame" x="84" y="13" width="20" height="20"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="20" id="NfM-ir-gg6"/>
                                        <constraint firstAttribute="width" constant="20" id="qfZ-Oa-hg1"/>
                                    </constraints>
                                </imageView>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="电脑网页" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eB2-ce-SZG">
                                    <rect key="frame" x="112" y="14.5" width="57.5" height="17"/>
                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <button opaque="NO" tag="3" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="wCp-38-rqg">
                                    <rect key="frame" x="0.0" y="0.0" width="249" height="46"/>
                                    <connections>
                                        <action selector="backAction:" destination="-1" eventType="touchUpInside" id="ZoZ-cz-lVi"/>
                                        <action selector="funcBtnAction:" destination="-1" eventType="touchUpInside" id="9Pl-Kq-viy"/>
                                    </connections>
                                </button>
                            </subviews>
                            <color key="backgroundColor" systemColor="systemYellowColor"/>
                            <constraints>
                                <constraint firstItem="0Kx-Fb-gNA" firstAttribute="leading" secondItem="lsk-Nv-sFg" secondAttribute="leading" constant="84" id="HmZ-or-cYM"/>
                                <constraint firstItem="wCp-38-rqg" firstAttribute="top" secondItem="lsk-Nv-sFg" secondAttribute="top" id="Nax-ga-N3A"/>
                                <constraint firstItem="eB2-ce-SZG" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="0Kx-Fb-gNA" secondAttribute="trailing" constant="8" symbolic="YES" id="asb-Kq-74A"/>
                                <constraint firstItem="eB2-ce-SZG" firstAttribute="centerY" secondItem="lsk-Nv-sFg" secondAttribute="centerY" id="d0r-8S-byr"/>
                                <constraint firstItem="0Kx-Fb-gNA" firstAttribute="centerY" secondItem="lsk-Nv-sFg" secondAttribute="centerY" id="mog-5j-8Xj"/>
                                <constraint firstAttribute="width" constant="249" id="nvp-Fg-9s3"/>
                                <constraint firstAttribute="trailing" secondItem="eB2-ce-SZG" secondAttribute="trailing" constant="79.5" id="q2d-0d-Afc"/>
                                <constraint firstAttribute="bottom" secondItem="wCp-38-rqg" secondAttribute="bottom" id="qhn-V9-dP6"/>
                                <constraint firstAttribute="trailing" secondItem="wCp-38-rqg" secondAttribute="trailing" id="sZF-ND-Sm8"/>
                                <constraint firstAttribute="height" constant="46" id="xgX-GB-SgI"/>
                                <constraint firstItem="wCp-38-rqg" firstAttribute="leading" secondItem="lsk-Nv-sFg" secondAttribute="leading" id="zGw-ua-bag"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="23"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                            </userDefinedRuntimeAttributes>
                        </view>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="212" id="60Q-9S-GmV"/>
                        <constraint firstItem="PrF-fa-b1e" firstAttribute="top" secondItem="rYS-p2-5Gd" secondAttribute="top" id="Hgq-MK-9Ks"/>
                        <constraint firstItem="lsk-Nv-sFg" firstAttribute="centerX" secondItem="rYS-p2-5Gd" secondAttribute="centerX" id="Nmu-vi-AkC"/>
                        <constraint firstItem="2V7-S6-9e5" firstAttribute="centerX" secondItem="rYS-p2-5Gd" secondAttribute="centerX" id="Prd-ln-RdU"/>
                        <constraint firstItem="qP3-8c-qZH" firstAttribute="top" secondItem="lsk-Nv-sFg" secondAttribute="bottom" constant="24" id="QD2-0b-SQ7"/>
                        <constraint firstItem="LlE-3i-6nE" firstAttribute="centerX" secondItem="rYS-p2-5Gd" secondAttribute="centerX" id="Qq1-gC-7Gc"/>
                        <constraint firstAttribute="bottom" secondItem="LlE-3i-6nE" secondAttribute="bottom" constant="30" id="Xty-K2-BkF"/>
                        <constraint firstItem="LlE-3i-6nE" firstAttribute="top" secondItem="2V7-S6-9e5" secondAttribute="bottom" constant="24" id="dCE-tB-W8a"/>
                        <constraint firstItem="PrF-fa-b1e" firstAttribute="leading" secondItem="rYS-p2-5Gd" secondAttribute="leading" id="krw-vE-EQl"/>
                        <constraint firstAttribute="trailing" secondItem="PrF-fa-b1e" secondAttribute="trailing" id="nec-Q9-3G3"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="18"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                    </userDefinedRuntimeAttributes>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="Q5M-cg-NOt"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="z8q-DB-K4U" firstAttribute="leading" secondItem="Q5M-cg-NOt" secondAttribute="leading" id="10I-Hx-jPc"/>
                <constraint firstAttribute="bottom" secondItem="z8q-DB-K4U" secondAttribute="bottom" id="7yL-F9-eew"/>
                <constraint firstItem="Q5M-cg-NOt" firstAttribute="trailing" secondItem="z8q-DB-K4U" secondAttribute="trailing" id="8a1-qD-bNA"/>
                <constraint firstItem="rYS-p2-5Gd" firstAttribute="leading" secondItem="Q5M-cg-NOt" secondAttribute="leading" id="Kg7-OG-uV2"/>
                <constraint firstItem="Q5M-cg-NOt" firstAttribute="trailing" secondItem="rYS-p2-5Gd" secondAttribute="trailing" id="Wh6-NT-KOU"/>
                <constraint firstItem="Q5M-cg-NOt" firstAttribute="bottom" secondItem="rYS-p2-5Gd" secondAttribute="bottom" id="Z5i-K8-9n7"/>
            </constraints>
            <point key="canvasLocation" x="-66.400000000000006" y="106.59670164917542"/>
        </view>
    </objects>
    <resources>
        <image name="android_icon" width="17" height="21"/>
        <image name="close_icon" width="15" height="15"/>
        <image name="ios_icon" width="17" height="20"/>
        <image name="pc_icon" width="17" height="21"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemYellowColor">
            <color red="1" green="0.80000000000000004" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
