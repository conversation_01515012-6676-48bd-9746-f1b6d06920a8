<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="16096" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="BSConnTipsVC">
            <connections>
                <outlet property="confirmBtn" destination="0ir-9i-jDU" id="V4Q-dA-cEM"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="home_tips_b" translatesAutoresizingMaskIntoConstraints="NO" id="YlF-vq-UtU">
                    <rect key="frame" x="30.5" y="140.5" width="314" height="314"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="314" id="1Oh-Un-mEZ"/>
                        <constraint firstAttribute="height" constant="314" id="nfk-oH-GVe"/>
                    </constraints>
                </imageView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="请保持两台或两台以上的设备在同一网络下进行传输" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dME-NG-4Ow">
                    <rect key="frame" x="20" y="454.5" width="335" height="17"/>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <color key="textColor" red="0.84705882349999995" green="0.84705882349999995" blue="0.84705882349999995" alpha="1" colorSpace="calibratedRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="0ir-9i-jDU">
                    <rect key="frame" x="132.5" y="521.5" width="110" height="35"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="110" id="j2L-BX-kU0"/>
                        <constraint firstAttribute="height" constant="35" id="vLa-gC-xki"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <state key="normal" title="了解">
                        <color key="titleColor" red="0.84705882349999995" green="0.84705882349999995" blue="0.84705882349999995" alpha="1" colorSpace="calibratedRGB"/>
                    </state>
                    <connections>
                        <action selector="btnAction:" destination="-1" eventType="touchUpInside" id="Zia-5j-aPp"/>
                    </connections>
                </button>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="dME-NG-4Ow" secondAttribute="trailing" constant="20" id="3vN-cu-omX"/>
                <constraint firstItem="YlF-vq-UtU" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="5fj-ds-xkb"/>
                <constraint firstItem="YlF-vq-UtU" firstAttribute="centerY" secondItem="i5M-Pr-FkT" secondAttribute="centerY" constant="-36" id="TM2-KZ-bKn"/>
                <constraint firstItem="0ir-9i-jDU" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="gae-wX-3qr"/>
                <constraint firstItem="dME-NG-4Ow" firstAttribute="leading" secondItem="Q5M-cg-NOt" secondAttribute="leading" constant="20" id="i06-3e-RdM"/>
                <constraint firstItem="dME-NG-4Ow" firstAttribute="top" secondItem="YlF-vq-UtU" secondAttribute="bottom" id="lJd-Lm-4ut"/>
                <constraint firstItem="0ir-9i-jDU" firstAttribute="top" secondItem="dME-NG-4Ow" secondAttribute="bottom" constant="50" id="s4A-xz-8QW"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="Q5M-cg-NOt"/>
            <point key="canvasLocation" x="132" y="115"/>
        </view>
    </objects>
    <resources>
        <image name="home_tips_b" width="316" height="216"/>
    </resources>
</document>
