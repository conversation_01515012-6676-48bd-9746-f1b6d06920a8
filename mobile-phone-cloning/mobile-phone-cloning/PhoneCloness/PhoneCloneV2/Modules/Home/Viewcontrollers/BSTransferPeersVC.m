//
//  BSTransferPeersVC.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSTransferPeersVC.h"

#import "BSConnTipsVC.h"
#import "BSSendFileManager.h"

#import "BSAvatarView.h"
#import "BSAnimationView.h"

#import <AFNetworkReachabilityManager.h>
#import "QRcodeView.h"
#import "PopView.h"
@interface BSTransferPeersVC ()

@property (weak, nonatomic) IBOutlet UILabel *networkStatusLbl;
@property (weak, nonatomic) IBOutlet UIView *contentView;

@property (nonatomic, strong) NSMutableArray<BSAvatarView *> *avatarPool;

@property (strong, nonatomic) NSTimer *timer;



@end

@implementation BSTransferPeersVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.fd_prefersNavigationBarHidden = YES;
    
    [self setupUI];
    
    [[AFNetworkReachabilityManager sharedManager] startMonitoring];
    
    kWeakSelf
    [[AFNetworkReachabilityManager sharedManager] setReachabilityStatusChangeBlock:^(AFNetworkReachabilityStatus status) {
        
        if (status == AFNetworkReachabilityStatusReachableViaWiFi) {
            weakSelf.networkStatusLbl.text = NSLocalizedString(@"networkConnectedTips", nil);
        }else {
            weakSelf.networkStatusLbl.text = NSLocalizedString(@"networkUnconnectedTips", nil);
        }
        
    }];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [weakSelf scanNearbyPeer];
    });
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleNotification) name:@"applicationDidEnterBackground" object:nil];

}

- (void)dealloc {
    [BSSendFileManager manager].lostPeer = nil;
    [BSSendFileManager manager].foundPeer = nil;
    
}

- (void)setupUI {
    
    self.timer = [NSTimer scheduledTimerWithTimeInterval:0.5 target:self selector:@selector(clickAnimation) userInfo:nil repeats:YES];
    

    CGFloat itemW = 80.0;
    CGFloat itemH = 80.0;
    
    NSInteger col = 3;

    CGFloat lineSpacing = (self.contentView.jk_width - itemW * col) / (col + 1);
    CGFloat interItemSpacing = (self.contentView.jk_height - itemH * col) / (col + 1);
    
    CGFloat offset = 22;
    
    for (NSInteger i = 0; i < 9; i++) {
        
        if (i == 4) {
            continue;
        }
        
        CGFloat x = lineSpacing + (itemW + lineSpacing) * (i % col);
        CGFloat y = interItemSpacing + (itemH + interItemSpacing) * (i / col);
    
        if (i / col == 0) {
            if (i % col == 0) {
                x = lineSpacing + (itemW + lineSpacing) * (i % col) + offset;
                y = interItemSpacing + (itemH + interItemSpacing) * (i / col) + offset;
            }
            
            if (i % col == 2) {
                x = lineSpacing + (itemW + lineSpacing) * (i % col) - offset;
                y = interItemSpacing + (itemH + interItemSpacing) * (i / col) + offset;
            }
        }else if (i / col == 2) {
            if (i % col == 0) {
                x = lineSpacing + (itemW + lineSpacing) * (i % col) + offset;
                y = interItemSpacing + (itemH + interItemSpacing) * (i / col) - offset;
            }
            
            if (i % col == 2) {
                x = lineSpacing + (itemW + lineSpacing) * (i % col) - offset;
                y = interItemSpacing + (itemH + interItemSpacing) * (i / col) - offset;
            }
            
        }
        
        BSAvatarView *avatarView = [BSAvatarView getAvatarView];
        kWeakSelf
        weakObj(avatarView)
        avatarView.tapAction = ^{
            [weakSelf avatarViewTapAction:weak_avatarView];
        };
        avatarView.frame = CGRectMake(x, y, itemW, itemH);
        avatarView.hidden = YES;
        [self.contentView addSubview:avatarView];
        
        [self.avatarPool addObject:avatarView];
    }
    
    kWeakSelf
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [weakSelf showConnTipsVC];
    });
    
    
    for (BSPeerModel *peerModel in [BSSendFileManager manager].peerArray) {
        
        NSMutableArray *avatarViews = @[].mutableCopy;
        for (BSAvatarView *avatarView in weakSelf.avatarPool) {
            if (!avatarView.peerModel) {
                [avatarViews addObject:avatarView];
            }
        }
        
        if (avatarViews.count > 0) {
            NSInteger index = arc4random() % avatarViews.count;
            BSAvatarView *avatarView = avatarViews[index];
            [weakSelf showPeer:avatarView peerModel:peerModel];
        }
        
    }
    
    
    UILabel *tipLabel = [[UILabel alloc]initWithFrame:CGRectMake(0,20,kDeviceWidth-18*2,30)];
    tipLabel.tag = 666;
    tipLabel.textColor =  [UIColor whiteColor];
    tipLabel.font = [UIFont systemFontOfSize:14 weight:UIFontWeightBold];
    tipLabel.textAlignment = NSTextAlignmentCenter;
    if (self.type==1) {
        tipLabel.text =  local(@"点击此处，请在另一台设备上扫码下载“一键换机”，并确保在同一网络下打开即可进行连接");
    }
    else
    {
        tipLabel.text =  local(@"点击此处，请在另一台设备上扫码下载“一键换机”，并确保在同一网络下打开即可连接成功");
    }
    
    tipLabel.userInteractionEnabled = YES;
    tipLabel.numberOfLines = 0;
    tipLabel.jk_centerX = kDeviceWidth*0.5;
    [tipLabel sizeToFit];
    [self.view addSubview:tipLabel];
    [tipLabel bk_whenTapped:^{
        [MobClick event:@"other" attributes:@{@"source":@"连接页引流下载二维码点击次数"}];
        QRcodeView* codeView = [[QRcodeView alloc]initWithQrString:@"https://www.fsintech.com/mobile_transfer_download.html"];
        [codeView layoutIfNeeded];
        PopView* pop = [PopView popSideContentView:codeView direct:PopViewDirection_SlideInCenter];
        pop.backgroundColor =  [[UIColor blackColor]colorWithAlphaComponent:0.65];
    }];
    
}


- (void)setType:(NSInteger)type
{
    UILabel *tipLabel = [self.view viewWithTag:666];
    if (type==1) {
        tipLabel.text =  local(@"点击此处，请在另一台设备上扫码下载“一键换机”，并确保在同一网络下打开即可进行连接");
    }
    else
    {
        tipLabel.text =  local(@"点击此处，请在另一台设备上扫码下载“一键换机”，并确保在同一网络下打开即可连接成功");
    }
    
    tipLabel.userInteractionEnabled = YES;
    tipLabel.numberOfLines = 0;
    tipLabel.jk_centerX = kDeviceWidth*0.5;
    [tipLabel sizeToFit];
}

- (void)scanNearbyPeer {
    
    
    [[BSSendFileManager manager]startScan];
    kWeakSelf
    [BSSendFileManager manager].foundPeer = ^(BSPeerModel *model) {
        NSMutableArray *avatarViews = @[].mutableCopy;
        for (BSAvatarView *avatarView in weakSelf.avatarPool) {
            if (!avatarView.peerModel) {
                [avatarViews addObject:avatarView];
            }
        }
        
        if (avatarViews.count > 0) {
            NSInteger index = arc4random() % avatarViews.count;
            BSAvatarView *avatarView = avatarViews[index];
            [weakSelf showPeer:avatarView peerModel:model];
        }
        
    };
    
    [BSSendFileManager manager].lostPeer = ^(BSPeerModel *model) {
        
        for (BSAvatarView *avatarView in weakSelf.avatarPool) {
            if ([avatarView.peerModel.ids isEqualToString:model.ids]) {
                [weakSelf hidePeer:avatarView];
                break;
            }
        }
        
    };
    
    [BSReciveFileManager manager];
}




- (void)showPeer:(BSAvatarView *)avatarView peerModel:(BSPeerModel *)peerModel {
    avatarView.hidden = NO;
    avatarView.peerModel = peerModel;
    
    CATransition *transition = [CATransition animation];
    transition.duration = 1.5f;
    transition.type = @"rippleEffect";
    [avatarView.layer addAnimation:transition forKey:nil];
}

- (void)hidePeer:(BSAvatarView *)avatarView {
    avatarView.peerModel = nil;
    
    CATransition *transition = [CATransition animation];
    transition.duration = 1.5f;
    transition.type = @"rippleEffect";
    [avatarView.layer addAnimation:transition forKey:nil];
    
    weakObj(avatarView)
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.6 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        weak_avatarView.hidden = YES;
    });
}

- (void)showConnTipsVC {
    
    if ([[NSUserDefaults standardUserDefaults] boolForKey:@"connTipsVCShowed"]) {
        return;
    }
    
    BSConnTipsVC *vc = [[BSConnTipsVC alloc] init];
    vc.modalPresentationStyle = UIModalPresentationOverCurrentContext;
    
    UITabBarController *tabBarController = (UITabBarController *)[AppDelegate getAppDelegate].window.rootViewController;
    
    [tabBarController presentViewController:vc animated:NO completion:nil];
}

#pragma mark - Action
- (void)clickAnimation {
    
    BSAnimationView *animationView = [[BSAnimationView alloc] initWithFrame:self.contentView.bounds];
    animationView.userInteractionEnabled = NO;
    animationView.backgroundColor=[UIColor clearColor];
    [self.contentView insertSubview:animationView atIndex:0];
    
    [UIView animateWithDuration:2 animations:^{
        animationView.transform=CGAffineTransformScale(animationView.transform, 4, 4);
        animationView.alpha=0;
    } completion:^(BOOL finished) {
        [animationView removeFromSuperview];
    }];
}

- (void)avatarViewTapAction:(BSAvatarView *)avatarView {
    if (self.selPeerSuc) {
        self.selPeerSuc(avatarView.peerModel);
        
        
    }
//    [self backAction:nil];
}

- (void)handleNotification {
    for (BSAvatarView *avatarView in self.avatarPool) {
        avatarView.peerModel = nil;
        avatarView.hidden = YES;
    }
}

- (IBAction)backAction:(id)sender {
    [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark - Getter
- (NSMutableArray<BSAvatarView *> *)avatarPool {
    if (!_avatarPool) {
        _avatarPool = @[].mutableCopy;
    }
    return _avatarPool;
}

@end
