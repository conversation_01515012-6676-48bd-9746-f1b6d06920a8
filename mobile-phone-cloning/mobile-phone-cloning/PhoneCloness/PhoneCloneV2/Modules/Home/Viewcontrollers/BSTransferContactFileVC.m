//
//  BSTransferContactFileVC.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSTransferContactFileVC.h"

#import "BSContactHeaderView.h"
#import "BSContactCell.h"
#import "BSContact.h"

#import "pinyin.h"
#import <Contacts/Contacts.h>
#import <ContactsUI/ContactsUI.h>

@interface BSTransferContactFileVC () <UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic, strong) NSMutableSet *titlesSet;
@property (nonatomic, strong) NSMutableArray *titlesArray;

@property (nonatomic, strong) NSMutableDictionary *contactDict;
@property (nonatomic, strong) CNContactStore *contactStore;

@end

@implementation BSTransferContactFileVC

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.contactStore = [[CNContactStore alloc] init];
    
    [self setupUI];
    
    if (self.dataArray.count > 0) {
        
        BOOL hasNoSelect = NO;
        for (BSContact *model in self.dataArray) {
            if (!model.isSelected) {
                hasNoSelect = YES;
                break;
            }
        }
        
        self.allSelected = !hasNoSelect;
        
        [self requestContact];
        
    }else {
        CNAuthorizationStatus status = [CNContactStore authorizationStatusForEntityType:CNEntityTypeContacts];
        if (status == CNAuthorizationStatusAuthorized) {
            [self loadLocalContact];
        }else if (status == CNAuthorizationStatusNotDetermined) {
            
            kWeakSelf
            [self.contactStore requestAccessForEntityType:CNEntityTypeContacts
                                        completionHandler:^(BOOL granted, NSError * _Nullable error) {
                                            
                                            dispatch_async(dispatch_get_main_queue(), ^{
                                                if (granted) {
                                                    [weakSelf loadLocalContact];
                                                }
                                            });
                                            
                                        }];
            
        }else {
            [self showAlertViewWithFileType:BSFileTypeContact];
        }
    }
    
}

- (void)setupUI {
    
    UITableView *tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, self.contentHeight) style:UITableViewStylePlain];
    [tableView registerNib:[UINib nibWithNibName:@"BSContactCell" bundle:[NSBundle mainBundle]] forCellReuseIdentifier:@"BSContactCell"];
    [tableView registerNib:[UINib nibWithNibName:@"BSContactHeaderView" bundle:[NSBundle mainBundle]] forHeaderFooterViewReuseIdentifier:@"BSContactHeaderView"];
    tableView.contentInset = UIEdgeInsetsMake(0, 0, 61, 0);
    tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    tableView.backgroundColor = [UIColor whiteColor];
    tableView.dataSource = self;
    tableView.delegate = self;
    tableView.rowHeight = 58.0;
    tableView.estimatedSectionHeaderHeight = 0.0;
    tableView.estimatedSectionFooterHeight = 0.0;
    tableView.tableFooterView = [UIView new];
    [self.view addSubview:tableView];
    self.tableView = tableView;
    CGFloat topInset = [UIApplication sharedApplication].keyWindow.safeAreaInsets.top;
    [tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.top.mas_equalTo(self.view.mas_topMargin);
        make.bottom.mas_equalTo(self.view.mas_bottomMargin);
    }];
}

- (void)editAction {
    
    self.allSelected = !self.isAllSelected;
    [MobClick event:@"Select_the_file" attributes:@{@"Contact":@"全选"}];
    if (self.selectStatus && self.titlesArray.count > 0) {
        self.selectStatus(self.isAllSelected, self.isAllSelected);
    }
    
    for (NSArray *models in self.contactDict.allValues) {
        for (BSContact *model in models) {
             model.selected = self.allSelected;
        }
    }
    
    [self.tableView reloadData];
    
}

- (void)senderAction {
    
    NSMutableArray *dataArray = @[].mutableCopy;
    for (NSArray *models in self.contactDict.allValues) {
        for (BSContact *model in models) {
            if (model.isSelected) {
                
                NSMutableArray *phones = @[].mutableCopy;
                
                for (CNLabeledValue *labelValue in model.contact.phoneNumbers) {
                    CNPhoneNumber *phoneNumber = labelValue.value;
                    [phones addObject:phoneNumber.stringValue];
                }
          
                NSDictionary *infoDict = @{
                                           @"familyName":model.contact.familyName,
                                           @"givenName":model.contact.givenName,
                                           @"phones":phones
                                           };
                [dataArray addObject:infoDict];
            }
        }
    }
    
    [[BSSendFileManager manager] sendFileWithPeerModel:self.peerModel datas:dataArray sendFileType:BSFileTypeContact];
    
}

- (void)sendDataSuccessed {
    self.allSelected = NO;
    
    if (self.selectStatus) {
        self.selectStatus(self.isAllSelected, self.isAllSelected);
    }
    
    for (NSArray *models in self.contactDict.allValues) {
        for (BSContact *model in models) {
            model.selected = self.allSelected;
        }
    }
    
    [self.tableView reloadData];
}

- (void)reciveDataSuccessed:(NSInteger)reciveType {
    if (reciveType == BSFileTypeContact) {
        [self loadLocalContact];
    }
}

#pragma mark - UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.titlesArray.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    
    NSArray *sectionArray = [self.contactDict objectForKey:self.titlesArray[section]];
    return sectionArray.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    BSContactCell *cell = [tableView dequeueReusableCellWithIdentifier:@"BSContactCell" forIndexPath:indexPath];
    NSArray *sectionArray = [self.contactDict objectForKey:self.titlesArray[indexPath.section]];
    cell.indexPath = indexPath;
    cell.model = sectionArray[indexPath.row];
    return cell;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    
    BSContactHeaderView *headerView = [tableView dequeueReusableHeaderFooterViewWithIdentifier:@"BSContactHeaderView"];
    headerView.titleLbl.text = self.titlesArray[section];
    return headerView;
    
}

- (NSArray<NSString *> *)sectionIndexTitlesForTableView:(UITableView *)tableView {
    return self.titlesArray.copy;
}
#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath  {
    [MobClick event:@"Select_the_file" attributes:@{@"Contact":@"选择某文件"}];
    NSArray *sectionArray = [self.contactDict objectForKey:self.titlesArray[indexPath.section]];
    BSContact *contact = sectionArray[indexPath.row];
    contact.selected = !contact.isSelected;
    
    [self.tableView reloadRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationNone];
    
    BOOL hasNoSelect = NO;
    for (NSArray *models in self.contactDict.allValues) {
        for (BSContact *model in models) {
            if (!model.isSelected) {
                hasNoSelect = YES;
                break;
            }
        }
        if (hasNoSelect) {
            break;
        }
    }
    
    self.allSelected = !hasNoSelect;
    
    BOOL hasSelect = NO;
    for (NSArray *models in self.contactDict.allValues) {
        for (BSContact *model in models) {
            if (model.isSelected) {
                hasSelect = YES;
                break;
            }
        }
        if (hasSelect) {
            break;
        }
    }
    
    if (self.selectStatus) {
        self.selectStatus(hasSelect, self.allSelected);
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 30.0;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

#pragma mark - JXCategoryListContentViewDelegate
- (void)listDidAppear {
    [super listDidAppear];
    
    BOOL hasSelect = NO;
    if (!self.allSelected) {
        
        for (NSArray *models in self.contactDict.allValues) {
            for (BSContact *model in models) {
                if (model.isSelected) {
                    hasSelect = YES;
                }
                break;
            }
            
            if (hasSelect) {
                break;
            }
        }
    }else {
        hasSelect = YES;
    }
    
    
    if (self.selectStatus) {
        self.selectStatus(hasSelect, self.allSelected);
    }
}

#pragma mark - Request
- (void)loadLocalContact {
    
    [self.dataArray removeAllObjects];
    
    NSArray *keys = @[CNContactIdentifierKey, CNContactGivenNameKey, CNContactFamilyNameKey, CNContactPhoneNumbersKey, CNContactImageDataAvailableKey, CNContactThumbnailImageDataKey];
    CNContactFetchRequest *fetchRequest = [[CNContactFetchRequest alloc] initWithKeysToFetch:keys];
    
    kWeakSelf
    [self.contactStore enumerateContactsWithFetchRequest:fetchRequest error:nil usingBlock:^(CNContact * _Nonnull contact, BOOL * _Nonnull stop) {
        
        BSContact *model = [[BSContact alloc] init];
        model.contact = contact;
        [weakSelf.dataArray addObject:model];
    
    }];
    
    [self requestContact];
    
}

- (void)requestContact {
    
    [self.titlesSet removeAllObjects];
    [self.contactDict removeAllObjects];
    
    for (BSContact *model in self.dataArray) {
        
        NSString *title = @"#";
        
        NSString *name = [NSString stringWithFormat:@"%@%@", model.contact.familyName, model.contact.givenName];
        
        if (name.length > 0) {
            
            if ([self stringIsChinese: [name substringToIndex:1]]) {
                title = [NSString stringWithFormat:@"%c", pinyinFirstLetter([name characterAtIndex:0])];
            }else {
                NSString *c = [NSString stringWithFormat:@"%c", [name characterAtIndex:0]];
                if ([self stringIsLetter:c]) {
                    title = c;
                }
                
            }
            
        }
        
        title = [title uppercaseString];
        [self pickContact:model title:title];
        
    }
    
    self.titlesArray = [[self.titlesSet allObjects] sortedArrayUsingSelector:@selector(compare:)].mutableCopy;
    
    if ([self.titlesArray containsObject:@"#"]) {
        [self.titlesArray removeObject:@"#"];
        [self.titlesArray addObject:@"#"];
    }
    
    [self.tableView reloadData];
    
}

- (void)pickContact:(BSContact *)contact title:(NSString *)title{
    
    if (title.length > 0) {
        if ([self.titlesSet containsObject:title]) {
            
            NSMutableArray *contacts = [self.contactDict objectForKey:title];
            [contacts addObject:contact];
            
        }else {
            [self.titlesSet addObject:title];
            NSMutableArray *contacts = [NSMutableArray array];
            [contacts addObject:contact];
            [self.contactDict setObject:contacts forKey:title];
        }
        
    }
    
}

- (BOOL)stringIsLetter:(NSString *)string {
    if (string.length == 0) {
        return NO;
    }
    
    NSString *regex =@"[a-zA-Z]*";
    NSPredicate *pred = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",regex];
    return [pred evaluateWithObject:string];
}

- (BOOL)stringIsChinese:(NSString *)string {
    if (string.length == 0) {
        return NO;
    }
    
    NSString *regex = @"(^[\u4e00-\u9fa5]+$)";
    NSPredicate *pred = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",regex];
    return [pred evaluateWithObject:string];
}


#pragma mark - GET
- (NSMutableDictionary *)contactDict {
    if (!_contactDict) {
        _contactDict = [NSMutableDictionary dictionary];
    }
    return _contactDict;
}

- (NSMutableSet *)titlesSet {
    if (!_titlesSet) {
        _titlesSet = [NSMutableSet set];
    }
    return _titlesSet;
}

@end
