//
//  BSTransferVC.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSTransferVC.h"

#import "BSTransferPeerCell.h"
#import "BSTransferEmptyView.h"

@interface BSTransferVC () <UITableViewDataSource, UITableViewDelegate, BSTransferPeerCellDelegate>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray *dataArray;

@property (nonatomic, strong) BSTransferEmptyView *emptyView;

@end

@implementation BSTransferVC

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
    
    kWeakSelf
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [weakSelf scanNearbyPeer];
    });
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleNotification) name:@"applicationDidEnterBackground" object:nil];

    [self.dataArray addObjectsFromArray:[BSSendFileManager manager].peerArray];
    [self.tableView reloadData];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    [BSSendFileManager manager].foundPeer = nil;
    [BSSendFileManager manager].lostPeer = nil;
}

- (void)setupUI {
    
    self.navigationItem.title = NSLocalizedString(@"传输", nil);
    
    UITableView *tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth , kScreenHeight - kStatusBarAndNavgationBarHeight - kBottomSpaceHeight) style:UITableViewStylePlain];
    [tableView registerNib:[UINib nibWithNibName:@"BSTransferPeerCell" bundle:[NSBundle mainBundle]] forCellReuseIdentifier:@"BSTransferPeerCell"];
    tableView.backgroundColor = [UIColor whiteColor];
    tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    tableView.dataSource = self;
    tableView.delegate = self;
    tableView.rowHeight = 55.0;
    tableView.estimatedSectionHeaderHeight = 0.0;
    tableView.estimatedSectionFooterHeight = 0.0;
    tableView.tableFooterView = [UIView new];
    [self.view addSubview:tableView];
    self.tableView = tableView;
    CGFloat topInset = [UIApplication sharedApplication].keyWindow.safeAreaInsets.top;
    [tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.top.mas_equalTo(self.view.mas_topMargin).offset(topInset);
        make.bottom.mas_equalTo(self.view.mas_bottomMargin);
    }];
    
    self.emptyView = [BSTransferEmptyView getTransferEmptyView];
    self.emptyView.bounds = self.tableView.bounds;
}

- (void)scanNearbyPeer {
    
    kWeakSelf
    [BSSendFileManager manager].foundPeer = ^(BSPeerModel *model) {
        
        [weakSelf.dataArray insertObject:model atIndex:0];
        [weakSelf.tableView reloadData];
        
    };
    
    [BSSendFileManager manager].lostPeer = ^(BSPeerModel *model) {
        
        for (BSPeerModel *peerModel in weakSelf.dataArray) {
            if ([peerModel.ids isEqualToString:model.ids]) {
                [weakSelf.dataArray removeObject:peerModel];
                [weakSelf.tableView reloadData];
                break;
            }
        }
        
    };

}

- (void)handleNotification {
    
    [self.dataArray removeAllObjects];
    [self.tableView reloadData];
    
}

#pragma mark - UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    if (self.dataArray.count == 0) {
        self.tableView.backgroundView = self.emptyView;
    }else {
        self.tableView.backgroundView = nil;
    }
    return self.dataArray.count > 0 ? 1 : 0;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArray.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    BSTransferPeerCell *cell = [tableView dequeueReusableCellWithIdentifier:@"BSTransferPeerCell" forIndexPath:indexPath];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    cell.peerModel = self.dataArray[indexPath.row];
    cell.delegate = self;
    return cell;
}

#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    
}

#pragma mark - BSTransferPeerCellDelegate
- (void)transferPeerCell:(BSTransferPeerCell *)cell peerModel:(BSPeerModel *)peerModel {
    
    [[BSSendFileManager manager] sendFileWithPeerModel:peerModel datas:self.resGroupArray];
    
    cell.userInteractionEnabled = NO;
    
    weakObj(cell)
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        weak_cell.userInteractionEnabled = YES;
    });
}

#pragma mark - Getter
- (NSMutableArray *)dataArray {
    if (!_dataArray) {
        _dataArray = @[].mutableCopy;
    }
    return _dataArray;
}

@end
