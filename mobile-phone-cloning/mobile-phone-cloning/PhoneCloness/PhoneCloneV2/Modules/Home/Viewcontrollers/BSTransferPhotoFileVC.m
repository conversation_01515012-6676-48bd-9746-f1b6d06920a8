//
//  BSTransferPhotoFileVC.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSTransferPhotoFileVC.h"

#import "BSPhotoCCell.h"

@interface BSTransferPhotoFileVC () <UICollectionViewDataSource, UICollectionViewDelegate, BSPhotoCCellDelegate>

@property (nonatomic, strong) UICollectionView *collectionView;

@end

@implementation BSTransferPhotoFileVC

- (void)viewDidLoad {
    [super viewDidLoad];

    [self setupUI];
    
    if (self.dataArray.count > 0) {
        
        BOOL hasNoSelect = NO;
        for (TZAssetModel *model in self.dataArray) {
            if (!model.isSelected) {
                hasNoSelect = YES;
                break;
            }
        }
        
        self.allSelected = !hasNoSelect;
        
    }else {
        
        PHAuthorizationStatus status = [PHPhotoLibrary authorizationStatus];
        if (status == PHAuthorizationStatusAuthorized) {
            [self requestPhotos];
            
        }else if (status == PHAuthorizationStatusNotDetermined) {
            kWeakSelf
            [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    
                    if (status == PHAuthorizationStatusAuthorized) {
                        [weakSelf requestPhotos];
                    }
                    
                });
            }];
            
        }else {
            [self showAlertViewWithFileType:BSFileTypePhoto];
        }
        
    }
}

- (void)setupUI {
    
    UICollectionViewFlowLayout *flowLayout = [[UICollectionViewFlowLayout alloc] init];
    flowLayout.minimumInteritemSpacing = 15.0;
    flowLayout.minimumLineSpacing = 20.0;
    flowLayout.sectionInset = UIEdgeInsetsMake(15.0, 18.0, 15.0, 18.0);
    CGFloat itemW = (kScreenWidth - 2 * 18 - 20 * 2) / 3.0;
    CGFloat itemH = (152 / 100.0) * itemW;
    flowLayout.itemSize = CGSizeMake(itemW, itemH);
    
    UICollectionView *collectionView = [[UICollectionView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, self.contentHeight) collectionViewLayout:flowLayout];
    [collectionView registerNib:[UINib nibWithNibName:@"BSPhotoCCell" bundle:[NSBundle mainBundle]] forCellWithReuseIdentifier:@"BSPhotoCCell"];
    collectionView.contentInset = UIEdgeInsetsMake(0, 0, 61, 0);
    collectionView.backgroundColor = [UIColor whiteColor];
    collectionView.dataSource = self;
    collectionView.delegate = self;
    [self.view addSubview:collectionView];
    self.collectionView = collectionView;
    CGFloat topInset = [UIApplication sharedApplication].keyWindow.safeAreaInsets.top;
    [collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.top.mas_equalTo(self.view.mas_topMargin);
        make.bottom.mas_equalTo(self.view.mas_bottomMargin);
    }];
}

- (void)editAction {
    
    self.allSelected = !self.isAllSelected;
    [MobClick event:@"Select_the_file" attributes:@{@"Photo":@"全选"}];
    if (self.selectStatus && self.dataArray.count > 0) {
        self.selectStatus(self.isAllSelected, self.isAllSelected);
    }
    
    for (TZAssetModel *model in self.dataArray) {
        model.isSelected = self.allSelected;
    }
    [self.collectionView reloadData];
    
}

- (void)senderAction {
    
    NSMutableArray *dataArray = @[].mutableCopy;
    for (TZAssetModel *model in self.dataArray) {
        if (model.isSelected) {
            [dataArray addObject:model];
        }
    }

    [[BSSendFileManager manager] sendFileWithPeerModel:self.peerModel datas:dataArray sendFileType:BSFileTypePhoto];
    
}

- (void)sendDataSuccessed {
    self.allSelected = NO;
    
    if (self.selectStatus) {
        self.selectStatus(self.isAllSelected, self.isAllSelected);
    }
    
    for (TZAssetModel *model in self.dataArray) {
        model.isSelected = self.allSelected;
    }
    [self.collectionView reloadData];
}

- (void)reciveDataSuccessed:(NSInteger)reciveType {
    if (reciveType == BSFileTypePhoto) {
        [self requestPhotos];
    }
}

#pragma mark - UICollectionViewDataSource
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.dataArray.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    BSPhotoCCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"BSPhotoCCell" forIndexPath:indexPath];
    cell.model = self.dataArray[indexPath.item];
    cell.delegate = self;
    cell.indexPath = indexPath;
    return cell;
}

#pragma mark - UICollectionViewDelegate
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    
}


#pragma mark - BSPhotoCCellDelegate
- (void)photoCCell:(BSPhotoCCell *)cell didSelectedIndexPath:(NSIndexPath *)indexPath {
    [MobClick event:@"Select_the_file" attributes:@{@"Photo":@"选择某文件"}];
    TZAssetModel *model = self.dataArray[indexPath.item];
    model.isSelected = !model.isSelected;
    [self.collectionView reloadItemsAtIndexPaths:@[indexPath]];
    
    BOOL hasNoSelect = NO;
    for (TZAssetModel *model in self.dataArray) {
        if (!model.isSelected) {
            hasNoSelect = YES;
            break;
        }
    }
    
    self.allSelected = !hasNoSelect;
    
    BOOL hasSelect = NO;
    if (!self.allSelected) {
        for (TZAssetModel *model in self.dataArray) {
            if (model.isSelected) {
                hasSelect = YES;
                break;
            }
        }
    }else {
        hasSelect = YES;
    }
    
    
    if (self.selectStatus) {
        self.selectStatus(hasSelect, self.allSelected);
    }
}

#pragma mark - JXCategoryListContentViewDelegate
- (void)listDidAppear {
    [super listDidAppear];
    
    BOOL hasSelect = NO;
    if (!self.allSelected) {
        for (TZAssetModel *model in self.dataArray) {
            if (model.isSelected) {
                hasSelect = YES;
                break;
            }
        }
    }else {
        hasSelect = YES;
    }
    
    
    if (self.selectStatus) {
        self.selectStatus(hasSelect, self.allSelected);
    }
}

#pragma mark - Request
- (void)requestPhotos {
    
    kWeakSelf
    [[TZImageManager manager] getCameraRollAlbum:NO allowPickingImage:YES needFetchAssets:NO completion:^(TZAlbumModel *model) {
        
        [[TZImageManager manager] getAssetsFromFetchResult:model.result allowPickingVideo:NO allowPickingImage:YES completion:^(NSArray<TZAssetModel *> *models) {
            
            [weakSelf.dataArray removeAllObjects];
            
            for (TZAssetModel *model in models) {
                if (model.type == TZAssetModelMediaTypePhoto) {
                    [weakSelf.dataArray addObject:model];
                }
            }
            
            [weakSelf.collectionView reloadData];
            
        }];
        
    }];
    
    
}

@end
