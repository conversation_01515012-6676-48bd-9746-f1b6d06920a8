<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="BSTransferMusicAlertVC">
            <connections>
                <outlet property="view" destination="iN0-l3-epB" id="L2A-AW-Vh0"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="6vS-8c-dKk">
                    <rect key="frame" x="62.5" y="204" width="250" height="203"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8Q6-v8-suj">
                            <rect key="frame" x="18" y="22" width="214" height="109"/>
                            <string key="text">选择需要发送的音乐保存至“文件”。 在发送端的手机里选择需要发送的音乐，点击发送。
接收端收到音乐文件后将会存储在“文件”中。
通过第三方音乐App读取存储于“文件”中的音乐列表并播放。</string>
                            <fontDescription key="fontDescription" type="system" pointSize="13"/>
                            <color key="textColor" red="0.21176470589999999" green="0.21176470589999999" blue="0.21176470589999999" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kXg-ak-aGR">
                            <rect key="frame" x="0.0" y="161" width="250" height="42"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8vk-kK-8KH">
                                    <rect key="frame" x="0.0" y="0.0" width="250" height="1"/>
                                    <color key="backgroundColor" red="0.94117647059999998" green="0.94117647059999998" blue="0.94117647059999998" alpha="1" colorSpace="calibratedRGB"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="1" id="xiV-6z-cy9"/>
                                    </constraints>
                                </view>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="rWu-Ly-hYr">
                                    <rect key="frame" x="0.0" y="1" width="250" height="41"/>
                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                                    <state key="normal" title="了解">
                                        <color key="titleColor" red="0.23921568630000001" green="0.60784313729999995" blue="0.94117647059999998" alpha="1" colorSpace="calibratedRGB"/>
                                    </state>
                                    <connections>
                                        <action selector="btnAction:" destination="-1" eventType="touchUpInside" id="QFI-zS-FPh"/>
                                    </connections>
                                </button>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="bottom" secondItem="rWu-Ly-hYr" secondAttribute="bottom" id="3Az-je-oZd"/>
                                <constraint firstItem="rWu-Ly-hYr" firstAttribute="leading" secondItem="kXg-ak-aGR" secondAttribute="leading" id="Dxn-rk-tHq"/>
                                <constraint firstAttribute="height" constant="42" id="Gkc-JQ-nE3"/>
                                <constraint firstAttribute="trailing" secondItem="8vk-kK-8KH" secondAttribute="trailing" id="TaL-RL-ChG"/>
                                <constraint firstItem="8vk-kK-8KH" firstAttribute="top" secondItem="kXg-ak-aGR" secondAttribute="top" id="X5Z-Fp-ADQ"/>
                                <constraint firstAttribute="trailing" secondItem="rWu-Ly-hYr" secondAttribute="trailing" id="bms-DM-xab"/>
                                <constraint firstItem="rWu-Ly-hYr" firstAttribute="top" secondItem="8vk-kK-8KH" secondAttribute="bottom" id="lDZ-uo-czn"/>
                                <constraint firstItem="8vk-kK-8KH" firstAttribute="leading" secondItem="kXg-ak-aGR" secondAttribute="leading" id="vxk-at-nbJ"/>
                            </constraints>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="8Q6-v8-suj" secondAttribute="trailing" constant="18" id="26C-Vo-ge8"/>
                        <constraint firstAttribute="trailing" secondItem="kXg-ak-aGR" secondAttribute="trailing" id="46j-4Q-Zvq"/>
                        <constraint firstAttribute="bottom" secondItem="kXg-ak-aGR" secondAttribute="bottom" id="E9M-YJ-U3s"/>
                        <constraint firstItem="kXg-ak-aGR" firstAttribute="leading" secondItem="6vS-8c-dKk" secondAttribute="leading" id="RUm-mX-Ywe"/>
                        <constraint firstItem="kXg-ak-aGR" firstAttribute="top" secondItem="8Q6-v8-suj" secondAttribute="bottom" constant="30" id="Tpo-wb-SJb"/>
                        <constraint firstAttribute="width" constant="250" id="kog-kA-lbt"/>
                        <constraint firstItem="8Q6-v8-suj" firstAttribute="top" secondItem="6vS-8c-dKk" secondAttribute="top" constant="22" id="plc-1E-dZV"/>
                        <constraint firstItem="8Q6-v8-suj" firstAttribute="leading" secondItem="6vS-8c-dKk" secondAttribute="leading" constant="18" id="v6n-2Q-39c"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                            <integer key="value" value="4"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                    </userDefinedRuntimeAttributes>
                </view>
            </subviews>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="6vS-8c-dKk" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" constant="-28" id="55l-zC-dnY"/>
                <constraint firstItem="6vS-8c-dKk" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="grS-tq-jVK"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
        </view>
    </objects>
</document>
