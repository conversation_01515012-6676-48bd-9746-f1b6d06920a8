//
//  BSTransferMusicFileVC.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSTransferMusicFileVC.h"
#import "BSTransferMusicAlertVC.h"

#import "BSMusicCell.h"
#import "BSMusic.h"

#import <MediaPlayer/MediaPlayer.h>

@interface BSTransferMusicFileVC () <UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) UITableView *tableView;

@end

@implementation BSTransferMusicFileVC

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
    
    if (self.dataArray.count > 0) {
        
        BOOL hasNoSelect = NO;
        for (BSMusic *model in self.dataArray) {
            if (!model.isSelected) {
                hasNoSelect = YES;
                break;
            }
        }
        
        self.allSelected = !hasNoSelect;
        
    }else {
        
        MPMediaLibraryAuthorizationStatus status = [MPMediaLibrary authorizationStatus];
        
        if (status == MPMediaLibraryAuthorizationStatusAuthorized) {
            [self requestMusic];
            [self showTransferMusicAlert];
        }else if (status == MPMediaLibraryAuthorizationStatusNotDetermined) {
            
            kWeakSelf
            [MPMediaLibrary requestAuthorization:^(MPMediaLibraryAuthorizationStatus status) {
                
                if (status == MPMediaLibraryAuthorizationStatusAuthorized) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        [weakSelf requestMusic];
                        [weakSelf showTransferMusicAlert];
                    });
                }
                
            }];
            
        }else {
            [self showAlertViewWithFileType:BSFileTypeMusic];
        }
        
    }
    
}

- (void)showTransferMusicAlert{
    
    BOOL isShowed = [[NSUserDefaults standardUserDefaults] boolForKey:@"transferMusicAlertShowed"];
    
    if (!isShowed) {
        BSTransferMusicAlertVC *vc = [[BSTransferMusicAlertVC alloc] init];
        vc.view.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.59];
        vc.modalPresentationStyle = UIModalPresentationOverCurrentContext;
        
        UITabBarController *tabBarController = (UITabBarController *)[AppDelegate getAppDelegate].window.rootViewController;
        UINavigationController *navController = (UINavigationController *)tabBarController.viewControllers[tabBarController.selectedIndex];
        
        [navController presentViewController:vc animated:NO completion:nil];
    }
    
}

- (void)setupUI {
    
    CGFloat topInset = [UIApplication sharedApplication].keyWindow.safeAreaInsets.top;
    UITableView *tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, topInset, kScreenWidth, self.contentHeight) style:UITableViewStylePlain];
    [tableView registerNib:[UINib nibWithNibName:@"BSMusicCell" bundle:[NSBundle mainBundle]] forCellReuseIdentifier:@"BSMusicCell"];
    tableView.contentInset = UIEdgeInsetsMake(0, 0, 61, 0);
    tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    tableView.backgroundColor = [UIColor whiteColor];
    tableView.dataSource = self;
    tableView.delegate = self;
    tableView.rowHeight = 112.0;
    tableView.estimatedSectionHeaderHeight = 0.0;
    tableView.estimatedSectionFooterHeight = 0.0;
    tableView.tableFooterView = [UIView new];
    [self.view addSubview:tableView];
    [tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.top.mas_equalTo(self.view.mas_topMargin);
        make.bottom.mas_equalTo(self.view.mas_bottomMargin);
    }];
    self.tableView = tableView;
    
}

- (void)editAction {
    
    self.allSelected = !self.isAllSelected;
    [MobClick event:@"Select_the_file" attributes:@{@"Music":@"全选"}];
    if (self.selectStatus && self.dataArray.count > 0) {
        self.selectStatus(self.isAllSelected, self.isAllSelected);
    }
    
    for (BSMusic *model in self.dataArray) {
        model.selected = self.allSelected;
    }
    [self.tableView reloadData];
    
}

- (void)senderAction {
    
    NSMutableArray *dataArray = @[].mutableCopy;
    
    for (BSMusic *model in self.dataArray) {
        if (model.isSelected) {
            [dataArray addObject:model];
        }
    }
    
    [[BSSendFileManager manager] sendFileWithPeerModel:self.peerModel datas:dataArray sendFileType:BSFileTypeMusic];
    
}

- (void)sendDataSuccessed {
    self.allSelected = NO;
    
    if (self.selectStatus) {
        self.selectStatus(self.isAllSelected, self.isAllSelected);
    }
    
    for (BSMusic *model in self.dataArray) {
        model.selected = self.allSelected;
    }
    [self.tableView reloadData];
}

- (void)reciveDataSuccessed:(NSInteger)reciveType {
    if (reciveType == BSFileTypeMusic) {
        [self requestMusic];
    }
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArray.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    BSMusicCell *cell = [tableView dequeueReusableCellWithIdentifier:@"BSMusicCell" forIndexPath:indexPath];
    cell.model = self.dataArray[indexPath.item];
    return cell;
}


#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [MobClick event:@"Select_the_file" attributes:@{@"Music":@"选择某文件"}];
    BSMusic *model = self.dataArray[indexPath.item];
    model.selected = !model.isSelected;
    [self.tableView reloadRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationNone];
    
    BOOL hasNoSelect = NO;
    for (BSMusic *model in self.dataArray) {
        if (!model.isSelected) {
            hasNoSelect = YES;
            break;
        }
    }
    
    self.allSelected = !hasNoSelect;
    
    BOOL hasSelect = NO;
    if (!self.allSelected) {
        for (BSMusic *model in self.dataArray) {
            if (model.isSelected) {
                hasSelect = YES;
                break;
            }
        }
    }else {
        hasSelect = YES;
    }
    
    
    if (self.selectStatus) {
        self.selectStatus(hasSelect, self.allSelected);
    }
    
}

#pragma mark - JXCategoryListContentViewDelegate
- (void)listDidAppear {
    [super listDidAppear];
    
    BOOL hasSelect = NO;
    if (!self.allSelected) {
        for (BSMusic *model in self.dataArray) {
            if (model.isSelected) {
                hasSelect = YES;
                break;
            }
        }
    }else {
        hasSelect = YES;
    }
    
    
    if (self.selectStatus) {
        self.selectStatus(hasSelect, self.allSelected);
    }
}

#pragma mark - Request
- (void)requestMusic {
    
    MPMediaQuery *query = [[MPMediaQuery alloc] init];
    MPMediaPropertyPredicate *albumNamePredicate = [MPMediaPropertyPredicate predicateWithValue:[NSNumber numberWithInt:MPMediaTypeMusic] forProperty:MPMediaItemPropertyMediaType];
    [query addFilterPredicate:albumNamePredicate];
    
    NSArray *items = [query items];
    
    [self.dataArray removeAllObjects];
    
    for (MPMediaItem *item in items) {
        
        NSString *title = [item valueForProperty:MPMediaItemPropertyTitle];
        NSURL *fileURL = [item valueForProperty:MPMediaItemPropertyAssetURL];
        NSString *singer = [item valueForProperty:MPMediaItemPropertyArtist];
        
        if (!singer) {
            singer = @"未知歌手";
        }
        
        MPMediaItemArtwork *artwork = [item valueForProperty:MPMediaItemPropertyArtwork];
        
        UIImage *image;
        if (artwork) {
            image = [artwork imageWithSize:CGSizeMake(72, 72)];
        }
        
        if (fileURL && title && singer) {
            NSDictionary *infoDict = @{
                                       @"title":title,
                                       @"fileURL":fileURL,
                                       @"singer":singer
                                       };
            BSMusic *music = [[BSMusic alloc] init];
            music.infoDict = infoDict;
            music.image = image;
            [self.dataArray addObject:music];
        }

    }
    
    [self.tableView reloadData];
    
}

@end
