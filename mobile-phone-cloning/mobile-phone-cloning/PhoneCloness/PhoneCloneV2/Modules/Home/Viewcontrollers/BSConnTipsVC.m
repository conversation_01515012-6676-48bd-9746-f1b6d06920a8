//
//  BSConnTipsVC.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSConnTipsVC.h"

@interface BSConnTipsVC ()

@property (weak, nonatomic) IBOutlet UIButton *confirmBtn;

@end

@implementation BSConnTipsVC

- (void)viewDidLoad {
    [super viewDidLoad];

    [self setupUI];
    
}

- (void)setupUI {
    
    self.view.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.84];
    BSViewBorderRadius(self.confirmBtn, 7.0, 1.0, [UIColor whiteColor]);
    
}

- (IBAction)btnAction:(id)sender {
    
    [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"connTipsVCShowed"];
    [[NSUserDefaults standardUserDefaults] synchronize];
    
    [self dismissViewControllerAnimated:NO completion:nil];
}


@end
