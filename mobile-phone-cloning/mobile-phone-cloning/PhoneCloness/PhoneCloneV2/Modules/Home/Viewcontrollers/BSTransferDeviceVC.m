//
//  BSTransferDeviceVC.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSTransferDeviceVC.h"

@interface BSTransferDeviceVC ()

@property (weak, nonatomic) IBOutlet UIView *pcView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *heightConstant;
@end

@implementation BSTransferDeviceVC

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
}

- (void)setupUI {
    if(AppDelegate.getAppDelegate.showWebLink&&self.isPC)
    {
        self.pcView.hidden = NO;
        self.heightConstant.constant = 212+46+24;
    }
    else
    {
        self.pcView.hidden = YES;
        self.heightConstant.constant = 212;
    }
    self.view.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
    
}

- (IBAction)backAction:(id)sender {
    [self dismissViewControllerAnimated:NO completion:nil];
}

- (IBAction)funcBtnAction:(UIButton *)sender {
    
    [self backAction:nil];
    
    if (self.selectIndex) {
        self.selectIndex(sender.tag);
    }
    
    
}


@end
