//
//  BSTransferFileBaseVC.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSTransferFileBaseVC.h"

@interface BSTransferFileBaseVC ()

@property (nonatomic, assign) BOOL isSendDataSuccessed;

@end

@implementation BSTransferFileBaseVC

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.edgesForExtendedLayout = UIRectEdgeNone;
    self.automaticallyAdjustsScrollViewInsets = NO;
    self.navigationController.navigationBar.translucent =  NO;
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(receiveNotificationHandler:) name:@"BSTransferFileEditNot" object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(receiveNotificationHandler:) name:@"BSTransferFileSendNot" object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(receiveNotificationHandler:) name:@"sendDataSuccessed" object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(receiveNotificationHandler:) name:@"reciveDataSuccessed" object:nil];

}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)receiveNotificationHandler:(NSNotification *)not {
    
    if ([not.name isEqualToString:@"BSTransferFileEditNot"]) {
        NSInteger type = [not.userInfo[@"type"] integerValue];
        
        if (type == self.type) {
            [self editAction];
        }
        
        
    }else if ([not.name isEqualToString:@"BSTransferFileSendNot"]) {
        NSInteger type = [not.userInfo[@"type"] integerValue];
        
        if (type == self.type) {
            self.peerModel = not.userInfo[@"peerModel"];
            [self senderAction];
        }
        
    }else if ([not.name isEqualToString:@"sendDataSuccessed"]) {
        NSInteger type = [not.userInfo[@"type"] integerValue];
        if (type == self.type) {
            self.isSendDataSuccessed = YES;
        }
        
    }else if ([not.name isEqualToString:@"reciveDataSuccessed"]) {
        
        BSFileType reciveType = [not.userInfo[@"reciveType"] integerValue];
        
        kWeakSelf
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf reciveDataSuccessed:reciveType];
        });
        
    }
}

- (void)editAction {
    
}

- (void)senderAction {
    
}

- (void)sendDataSuccessed {
    
}

- (void)reciveDataSuccessed:(NSInteger)reciveType {
    
}

- (void)showAlertViewWithFileType:(BSFileType)fileType {
    
    NSString *title;
    NSString *message;
    
    switch (fileType) {
        case BSFileTypePhoto:
        {
            //相册权限
            title = NSLocalizedString(@"请授权相册权限", nil);
            message = NSLocalizedString(@"请在iPhone的\"设置-隐私-相册\"选项中,允许App访问你的相册", nil);
        }
            break;
        case BSFileTypeVideo:
        {
            //视频权限
            //相册权限
            title = NSLocalizedString(@"请授权相册权限", nil);
            message = NSLocalizedString(@"请在iPhone的\"设置-隐私-相册\"选项中,允许App访问你的相册", nil);
        }
            break;
        case BSFileTypeContact:
        {
            //通讯录权限
            title = NSLocalizedString(@"请授权通讯录权限", nil);
            message = NSLocalizedString(@"请在iPhone的\"设置-隐私-通讯录\"选项中,允许App访问你的通讯录", nil);
        }
            break;
        case BSFileTypeMusic:
        {
            //多媒体权限
            title = NSLocalizedString(@"请授权媒体与Apple Music权限", nil);
            message = NSLocalizedString(@"请在iPhone的\"设置-隐私-媒体与Apple Music\"选项中,允许App访问你的媒体与Apple Music", nil);
        }
            break;
        case BSFileTypeCalendar:
        {
            //日历权限
            title = NSLocalizedString(@"请授权日历权限", nil);
            message = NSLocalizedString(@"请在iPhone的\"设置-隐私-日历\"选项中,允许App访问你的日历", nil);
        }
            break;
            
        default:
            break;
    }
    
    if (!title) {
        return;
    }
    
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:title message:message preferredStyle: UIAlertControllerStyleAlert];
    
    kWeakSelf
    UIAlertAction *cancleAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"gotIt", @"知道了") style:UIAlertActionStyleCancel handler:^(UIAlertAction * _Nonnull action) {
        [weakSelf.navigationController popViewControllerAnimated:YES];
    }];
    [alertController addAction:cancleAction];
    
    UIAlertAction *settingAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"toSettings", @"去设置") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        
        NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
        if ([[UIApplication sharedApplication] canOpenURL:url]) {
            [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
        }
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.25 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [weakSelf.navigationController popViewControllerAnimated:YES];
        });
        
    }];
    
    [alertController addAction:settingAction];
    
    UITabBarController *tabBarController = (UITabBarController *)[AppDelegate getAppDelegate].window.rootViewController;
    UINavigationController *navController = (UINavigationController *)tabBarController.viewControllers[tabBarController.selectedIndex];
    
    [navController presentViewController:alertController animated:YES completion:nil];
    
}

#pragma mark - JXCategoryListContentViewDelegate
- (UIView *)listView {
    return self.view;
}

- (void)listDidAppear {
    
    if (self.isSendDataSuccessed) {
        self.isSendDataSuccessed = NO;
        [self sendDataSuccessed];
    }
    
}

@end
