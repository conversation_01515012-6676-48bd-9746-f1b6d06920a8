//
//  BSTransferResVC.m
//  PhoneCloneV2
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSTransferResVC.h"
#import "BSTransferPhotoFileVC.h"
#import "BSTransferVideoFileVC.h"
#import "BSTransferContactFileVC.h"
#import "BSTransferMusicFileVC.h"
#import "BSTransferCalendarFileVC.h"

#import "BSResGroupModel.h"

#import "JXCategoryView.h"
#import "JXCategoryListContainerView.h"

@interface BSTransferResVC () <JXCategoryViewDelegate, JXCategoryListContainerViewDelegate>

@property (nonatomic, strong) UIButton *sendBtn;
@property (nonatomic, strong) UIButton *editBtn;

@property (nonatomic, strong) JXCategoryTitleView *categoryView;
@property (nonatomic, strong) JXCategoryListContainerView *listContainerView;
@property (nonatomic, strong) NSArray <NSString *> *titles;

@property (nonatomic, assign, getter=isAllSelected) BOOL allSelected;

@property (nonatomic, strong) BSPeerModel *peerModel;


@end

@implementation BSTransferResVC

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
}

- (void)setupUI {
    
    self.view.backgroundColor = [UIColor whiteColor];
    CGFloat topInset = [UIApplication sharedApplication].keyWindow.safeAreaInsets.top;
    self.categoryView = [[JXCategoryTitleView alloc] init];
    self.categoryView.frame = CGRectMake(20,topInset+40, [UIScreen mainScreen].bounds.size.width - 40, kNavgationBarHeight);
    self.categoryView.backgroundColor = [UIColor whiteColor];
    self.categoryView.delegate = self;
    self.categoryView.defaultSelectedIndex = self.defaultSelectedIndex;
    self.categoryView.titleColorGradientEnabled = YES;
    self.categoryView.contentEdgeInsetLeft = 0.0;
    self.categoryView.contentEdgeInsetRight = 0.0;
    self.categoryView.cellSpacing = 40.0;
    self.categoryView.averageCellSpacingEnabled = NO;
    self.categoryView.titleColor = [UIColor jk_colorWithHexString:@"#898888"];
    self.categoryView.titleSelectedColor = [UIColor jk_colorWithHexString:@"#3B94E9"];
    self.categoryView.titleFont = [UIFont systemFontOfSize:16.0];
    self.categoryView.titleSelectedFont = [UIFont systemFontOfSize:16.0 weight:UIFontWeightMedium];
    [self.view addSubview:self.categoryView];
    
    JXCategoryIndicatorLineView *lineView = [[JXCategoryIndicatorLineView alloc] init];
    lineView.indicatorLineViewColor = [UIColor jk_colorWithHexString:@"#3B94E9"];
    lineView.indicatorCornerRadius = 2.5;
    lineView.indicatorHeight = 4.0;
    lineView.verticalMargin = 6.0;
    self.categoryView.indicators = @[lineView];
    
    self.listContainerView = [[JXCategoryListContainerView alloc] initWithDelegate:self];
    self.listContainerView.frame = CGRectMake(0, CGRectGetMaxY(self.categoryView.frame), self.view.bounds.size.width, kScreenHeight - CGRectGetMaxY(self.categoryView.frame) - kBottomSpaceHeight);
    self.listContainerView.backgroundColor = [UIColor whiteColor];
    self.listContainerView.defaultSelectedIndex = 0;
    [self.view addSubview:self.listContainerView];
    
    self.categoryView.contentScrollView = self.listContainerView.scrollView;
    self.categoryView.defaultSelectedIndex = self.defaultSelectedIndex;
    self.categoryView.titles = self.titles;
    [self.categoryView reloadData];
    
    self.listContainerView.defaultSelectedIndex = self.defaultSelectedIndex;
    [self.listContainerView reloadData];
    
    self.editBtn = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, 60, 44)];
    [self.editBtn setTitle:NSLocalizedString(@"selectAll", nil) forState:UIControlStateNormal];
    self.editBtn.titleLabel.font = [UIFont systemFontOfSize:14];
    self.editBtn.tag = 2;
    [self.editBtn setTitleColor: [UIColor blackColor] forState:0];
    [self.editBtn addTarget:self action:@selector(funcBtnAction:) forControlEvents:UIControlEventTouchUpInside];
    UIBarButtonItem *rightBarButtonItem = [[UIBarButtonItem alloc] initWithCustomView:self.editBtn];
    self.navigationItem.rightBarButtonItem = rightBarButtonItem;
    
    CGFloat sendBtnX = (kScreenWidth - 154) / 2.0;
    CGFloat sendBtnY = kScreenHeight - kStatusBarAndNavgationBarHeight - 31 - 30 - kBottomSpaceHeight;
    self.sendBtn = [[UIButton alloc] initWithFrame:CGRectMake(sendBtnX, sendBtnY, 154, 31)];
    [self.sendBtn setTitle:NSLocalizedString(@"confirm", nil) forState:UIControlStateNormal];
    [self.sendBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.sendBtn.backgroundColor = [UIColor jk_colorWithHexString:@"#6FB5F4"];
    self.sendBtn.titleLabel.font = [UIFont systemFontOfSize:13.0];
    self.sendBtn.layer.cornerRadius = 15.5;
    self.sendBtn.layer.masksToBounds = YES;
    self.sendBtn.tag = 1;
    [self.sendBtn addTarget:self action:@selector(funcBtnAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.sendBtn];
    
}

#pragma mark - Action
- (void)funcBtnAction:(UIButton *)sender {
    if (sender.tag == 1) {
        //确认
        [self.navigationController popViewControllerAnimated:YES];
        
    }else if (sender.tag == 2) {
        //全选
        
        [[NSNotificationCenter defaultCenter] postNotificationName:@"BSTransferFileEditNot" object:nil userInfo:@{@"type": @(self.categoryView.selectedIndex)}];
        
    }
}


#pragma mark - JXCategoryViewDelegate
- (void)categoryView:(JXCategoryBaseView *)categoryView didClickSelectedItemAtIndex:(NSInteger)index {
    [self.listContainerView didClickSelectedItemAtIndex:index];
}

- (void)categoryView:(JXCategoryBaseView *)categoryView scrollingFromLeftIndex:(NSInteger)leftIndex toRightIndex:(NSInteger)rightIndex ratio:(CGFloat)ratio {
    [self.listContainerView scrollingFromLeftIndex:leftIndex toRightIndex:rightIndex ratio:ratio selectedIndex:categoryView.selectedIndex];
}

#pragma mark - JXCategoryListContainerViewDelegate
- (id<JXCategoryListContentViewDelegate>)listContainerView:(JXCategoryListContainerView *)listContainerView initListForIndex:(NSInteger)index {
    
    BSResGroupModel *resGroup = self.resGroupArray[index];
    
    NSString *title = self.titles[index];
    if ([title isEqualToString:NSLocalizedString(@"Photos", @"照片")]) {
        BSTransferPhotoFileVC *vc = [[BSTransferPhotoFileVC alloc] init];
        vc.contentHeight = self.listContainerView.bounds.size.height;
        vc.type = index;
        vc.peerModel = self.peerModel;
        vc.dataArray = resGroup.resArray;
        kWeakSelf
        vc.selectStatus = ^(BOOL hasSelect, BOOL allSelect) {
            weakSelf.allSelected = allSelect;
//            weakSelf.sendBtn.hidden = !hasSelect;
        };
        return vc;
        
    }else if ([title isEqualToString:NSLocalizedString(@"Videos", @"视频")]) {
        BSTransferVideoFileVC *vc = [[BSTransferVideoFileVC alloc] init];
        vc.contentHeight = self.listContainerView.bounds.size.height;
        vc.type = index;
        vc.peerModel = self.peerModel;
        vc.dataArray = resGroup.resArray;
        kWeakSelf
        vc.selectStatus = ^(BOOL hasSelect, BOOL allSelect) {
            weakSelf.allSelected = allSelect;
//            weakSelf.sendBtn.hidden = !hasSelect;
        };
        return vc;
    }else if ([title isEqualToString:NSLocalizedString(@"contacts", @"联系人")]) {
        BSTransferContactFileVC *vc = [[BSTransferContactFileVC alloc] init];
        vc.contentHeight = self.listContainerView.bounds.size.height;
        vc.type = index;
        vc.peerModel = self.peerModel;
        vc.dataArray = resGroup.resArray;
        kWeakSelf
        vc.selectStatus = ^(BOOL hasSelect, BOOL allSelect) {
            weakSelf.allSelected = allSelect;
//            weakSelf.sendBtn.hidden = !hasSelect;
        };
        return vc;
    }else if ([title isEqualToString:NSLocalizedString(@"music", @"音乐")]) {
        BSTransferMusicFileVC *vc = [[BSTransferMusicFileVC alloc] init];
        vc.contentHeight = self.listContainerView.bounds.size.height;
        vc.type = index;
        vc.peerModel = self.peerModel;
        vc.dataArray = resGroup.resArray;
        kWeakSelf
        vc.selectStatus = ^(BOOL hasSelect, BOOL allSelect) {
            weakSelf.allSelected = allSelect;
//            weakSelf.sendBtn.hidden = !hasSelect;
        };
        return vc;
    }else if ([title isEqualToString:NSLocalizedString(@"calendar", @"日历")]) {
        BSTransferCalendarFileVC *vc = [[BSTransferCalendarFileVC alloc] init];
        vc.contentHeight = self.listContainerView.bounds.size.height;
        vc.type = index;
        vc.peerModel = self.peerModel;
        vc.dataArray = resGroup.resArray;
        kWeakSelf
        vc.selectStatus = ^(BOOL hasSelect, BOOL allSelect) {
            weakSelf.allSelected = allSelect;
//            weakSelf.sendBtn.hidden = !hasSelect;
        };
        return vc;
    }
    
    
    return nil;
}

- (NSInteger)numberOfListsInlistContainerView:(JXCategoryListContainerView *)listContainerView {
    return self.titles.count;
}

#pragma mark - GET
- (NSArray<NSString *> *)titles {
    if (!_titles) {
        _titles = @[NSLocalizedString(@"Photos", @"相册"), NSLocalizedString(@"Videos", @"视频"), NSLocalizedString(@"contacts", @"联系人"), NSLocalizedString(@"music", @"音乐"), NSLocalizedString(@"calendar", @"日历")];
    }
    return _titles;
}

- (void)setAllSelected:(BOOL)allSelected {
    _allSelected = allSelected;
    
    NSString *title = allSelected ? NSLocalizedString(@"deselected", @"取消选择") : NSLocalizedString(@"selectAll", @"选择全部");
    [self.editBtn setTitle:title forState:UIControlStateNormal];
}


@end
