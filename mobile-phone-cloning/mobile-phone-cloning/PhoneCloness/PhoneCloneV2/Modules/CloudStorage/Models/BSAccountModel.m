//
//  BSAccountModel.m
//  PhoneClone
//
//  Created by 张加宁 on 2020/6/21.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSAccountModel.h"

@implementation BSAccountModel
/// 创建用户账号的model对象(用户本地保存部分登录信息,实现自动登录)
/// @param email 邮箱(账号名称)
/// @param path 云存储保存路径
+ (instancetype)createObjectWithEmail:(NSString *)email path:(NSString*)path{
    BSAccountModel *accountmodel = [[BSAccountModel alloc] init];
    [accountmodel setEmail:email];
    accountmodel.path = path;
    return accountmodel;
}

/// 创建用户账号的model对象(用于记录注册界面用户账号基本信息,包括账号名称和账号密码)
/// @param email 用户账号信息中的[邮箱](账号名称)
/// @param password 用户账号信息中的[密码]
+ (instancetype)createObjectWithEmail:(NSString*)email
                             password:(NSString*)password{
    BSAccountModel *accountModel = [[BSAccountModel alloc] init];
    accountModel.email = email;
    accountModel.password = password;
    
    return accountModel;
}

/// 创建用户账号的model对象(用于密保重置密码)
/// @param email 用户账号(一般为邮箱)
/// @param createTime 注册时间
/// @param q1_ans 密保第一个问题
/// @param q2_ans 密保第二个问题
/// @param q3_ans 密保第三个问题
+ (instancetype)createObjectWithEmail:(NSString*)email
                           createTime:(NSString*)createTime
                               Q1_ans:(NSString*)q1_ans
                               q2_ans:(NSString*)q2_ans
                               q3_ans:(NSString*)q3_ans{
    BSAccountModel *accountModel = [[BSAccountModel alloc] init];
    accountModel.email = email;
    accountModel.createTime = createTime;
    accountModel.q1_ans = q1_ans;
    accountModel.q2_ans = q2_ans;
    accountModel.q3_ans = q3_ans;
    return accountModel;
}

/// 创建用户账号的model对象(用于用户账号注册)
/// @param email 用户账号信息中的[邮箱](账号名称)
/// @param createTime 注册时间
/// @param password 用户账号信息中的[密码]
/// @param path 用户账号信息中的[云存储保存路径]
/// @param q1_ans 密保第一个问题
/// @param q2_ans 密保第二个问题
/// @param q3_ans 密保第三个问题
+ (instancetype)createObjectWithEmail:(NSString*)email
                           createTime:(NSString*)createTime
                             password:(NSString*)password
                                 path:(NSString*)path
                               q1_ans:(NSString*)q1_ans
                               q2_ans:(NSString*)q2_ans
                               q3_ans:(NSString*)q3_ans{
    BSAccountModel *accountModel = [[BSAccountModel alloc] init];
    accountModel.email = email;
    accountModel.createTime = createTime;
    accountModel.password = password;
    accountModel.path= path;
    accountModel.q1_ans = q1_ans;
    accountModel.q2_ans = q2_ans;
    accountModel.q3_ans = q3_ans;
    
    return accountModel;
}

@end
