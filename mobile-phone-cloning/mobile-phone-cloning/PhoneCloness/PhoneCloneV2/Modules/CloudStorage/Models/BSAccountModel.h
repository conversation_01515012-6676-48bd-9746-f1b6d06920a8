//
//  BSAccountModel.h
//  PhoneClone
//
//  Created by 张加宁 on 2020/6/21.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/// 用户账号信息model
@interface BSAccountModel : NSObject
/// 用户账号信息中的[邮箱]
@property (nonatomic, copy) NSString *email;
/// 用户账号信息中的[注册时间]
@property (nonatomic, copy) NSString *createTime;
/// 用户账号信息中的[密码]
@property (nonatomic, copy) NSString *password;
/// 用户账号信息中的[保存路径]
@property (nonatomic, copy) NSString *path;
/// 密保第一个问题
@property (nonatomic, copy) NSString *q1_ans;
/// 密保第二个问题
@property (nonatomic, copy) NSString *q2_ans;
/// 密保第三个问题
@property (nonatomic, copy) NSString *q3_ans;

/// 创建用户账号的model对象(用户本地保存部分登录信息,实现自动登录)
/// @param email 邮箱(账号名称)
/// @param path 云存储保存路径
+ (instancetype)createObjectWithEmail:(NSString *)email path:(NSString*)path;

/// 创建用户账号的model对象(用于记录注册界面用户账号基本信息,包括账号名称和账号密码)
/// @param email 用户账号信息中的[邮箱](账号名称)
/// @param password 用户账号信息中的[密码]
+ (instancetype)createObjectWithEmail:(NSString*)email password:(NSString*)password;

/// 创建用户账号的model对象(用于密保重置密码)
/// @param email 用户账号(一般为邮箱)
/// @param createTime 注册时间
/// @param q1_ans 密保第一个问题
/// @param q2_ans 密保第二个问题
/// @param q3_ans 密保第三个问题
+ (instancetype)createObjectWithEmail:(NSString*)email
                           createTime:(NSString*)createTime
                               Q1_ans:(NSString*)q1_ans
                               q2_ans:(NSString*)q2_ans
                               q3_ans:(NSString*)q3_ans;

/// 创建用户账号的model对象(用于用户账号注册)
/// @param email 用户账号信息中的[邮箱](账号名称)
/// @param createTime 注册时间
/// @param password 用户账号信息中的[密码]
/// @param path 用户账号信息中的[云存储保存路径]
/// @param q1_ans 密保第一个问题
/// @param q2_ans 密保第二个问题
/// @param q3_ans 密保第三个问题
+ (instancetype)createObjectWithEmail:(NSString*)email
                           createTime:(NSString*)createTime
                             password:(NSString*)password
                                 path:(NSString*)path
                               q1_ans:(NSString*)q1_ans
                               q2_ans:(NSString*)q2_ans
                               q3_ans:(NSString*)q3_ans;

@end

NS_ASSUME_NONNULL_END
