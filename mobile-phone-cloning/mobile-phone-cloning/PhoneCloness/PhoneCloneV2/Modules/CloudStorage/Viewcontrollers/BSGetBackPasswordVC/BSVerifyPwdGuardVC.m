//
//  BSVerifyPwdGuardVC.m
//  PhoneClone
//
//  Created by macbookair on 23/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSVerifyPwdGuardVC.h"
#import "BSPwdGuardTextField.h"
#import "BSResetPasswordVC.h"
@interface BSVerifyPwdGuardVC ()
/// 输入框信息的集合数组
@property (nonatomic, strong) NSArray *messageOfTextField;
#define Question_Of_Pwd_Guard @"question" //密保输入框的问题的key
#define Placeholder_Of_Pwd_Guard @"placeholder"  //密保输入框的提示语的key
#define Alert_To_Empty_Of_Pwd_Guard @"alert_to_empty"  //输入框为[空]时的弹出框提示语
#define Alert_To_Error_Ans_Of_Pwd_Guard @"alert_to_error_ans"  //输入框为[错误答案]时的弹出框提示语
/// 密保第一个问题的输入框控件(密保1:您最喜欢的人?)
@property (nonatomic, strong) BSPwdGuardTextField *pwdGuardTF01;
/// 密保第二个问题的输入框控件(密保2:您的学校?)
@property (nonatomic, strong) BSPwdGuardTextField *pwdGuardTF02;
/// 密保第三个问题的输入框控件(密保3:您最喜欢的电影?)
@property (nonatomic, strong) BSPwdGuardTextField *pwdGuardTF03;
@end

@implementation BSVerifyPwdGuardVC
- (instancetype)init{
    self = [super init];
    if (self) {
        NSArray *progressTextArray = @[NSLocalizedString(@"用户账号", nil),
                                       NSLocalizedString(@"验证身份", nil),
                                       NSLocalizedString(@"重置密码", nil),
                                       NSLocalizedString(@"完成", nil),];
        [self loadCustomWithTitle:NSLocalizedString(@"找回密码", nil)
                progressTextArray:progressTextArray
                  currentProgress:2
                        isNextBtn:YES];
        [self loadUserInterface];
    }
    return self;
}
-(void)loadUserInterface{
    [self loadTextFieldsView];
    [self.nextBtn addTarget:self action:@selector(nextBtnAction) forControlEvents:UIControlEventTouchUpInside];
}

/// 加载输入框集合的试图
- (void)loadTextFieldsView{
    self.pwdGuardTF01 = [[BSPwdGuardTextField alloc] init];
    self.pwdGuardTF02 = [[BSPwdGuardTextField alloc] init];
    self.pwdGuardTF03 = [[BSPwdGuardTextField alloc] init];
    self.bsPwdGuardTFArr = @[
        self.pwdGuardTF01,
        self.pwdGuardTF02,
        self.pwdGuardTF03,
    ];
    
    //遍历加载输入框控件
    for (int i=0; i<self.messageOfTextField.count; i++) {
        NSDictionary *dic = self.messageOfTextField[i];
        BSPwdGuardTextField *pwdGuardTextField = self.bsPwdGuardTFArr[i];
        [pwdGuardTextField loadCustomWithTopText:dic[Placeholder_Of_Pwd_Guard] placeholder:dic[Placeholder_Of_Pwd_Guard]];
        [self.view addSubview:pwdGuardTextField];
        [pwdGuardTextField mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(@0);
            make.width.equalTo(self.view.mas_width).multipliedBy(0.8);
            make.top.equalTo(self.mainProgressView.mas_bottom).offset(i*80);
            make.height.equalTo(@80);
        }];
    }
}

/// [下一步]按钮的点击事件
- (void)nextBtnAction{
//    [self.pwdGuardTF01 setInputText:@"mother"];
//    [self.pwdGuardTF02 setInputText:@"school"];
//    [self.pwdGuardTF03 setInputText:@"movie"];
    
    //如果存在输入内容不合法,结束[下一步]操作
    if (![self checkTextFieldAction]) {
        return;
    }
    
        [self gotoResetPasswordViewController];
}

- (void)gotoResetPasswordViewController{
    BSResetPasswordVC *resetPwdViewCtl = [[BSResetPasswordVC alloc] init];
    resetPwdViewCtl.accountModel = [[BSAccountModel alloc] init];
    resetPwdViewCtl.accountModel.email = self.accountModel.email;
    resetPwdViewCtl.accountModel.createTime = self.accountModel.createTime;
    [self.navigationController pushViewController:resetPwdViewCtl animated:YES];
}

#pragma mark ================== 工具类型的方法 start ==================
/// 检验所有输入框输入内容是否符合规范
/// @return YES:所有输入内容全部合法  NO:存在输入内容不合法
- (BOOL)checkTextFieldAction{
    //输入框的空判断
    for (int i=0; i < self.bsPwdGuardTFArr.count; i++) {
        BSPwdGuardTextField *pwdGuardTF = self.bsPwdGuardTFArr[i];
        if ([pwdGuardTF getInputText].length == 0) {
            NSDictionary *messageOfPwdGuardDic = self.messageOfTextField[i];
            [BSAlertTool loadAlertOfMessage:messageOfPwdGuardDic[Alert_To_Empty_Of_Pwd_Guard]
                                     okText:NSLocalizedString(@"确认", nil)
                          currentController:self
                                    handler:^(UIAlertAction * _Nonnull action) {
                [pwdGuardTF becomeFirstResponderAction];
            }];
            return NO;
        }
    }
    
    NSArray *ansArr = @[
        self.accountModel.q1_ans,
        self.accountModel.q2_ans,
        self.accountModel.q3_ans,
    ];
    for (int i=0; i<self.bsPwdGuardTFArr.count; i++) {
        BSPwdGuardTextField *pwdGuardTF = self.bsPwdGuardTFArr[i];
        NSString *ansStr = ansArr[i];
        if (![ansStr isEqualToString:[pwdGuardTF getInputText]]) {
            NSDictionary *messageOfPwdGuardDic = self.messageOfTextField[i];
            [BSAlertTool loadAlertOfMessage:messageOfPwdGuardDic[Alert_To_Error_Ans_Of_Pwd_Guard]
                                     okText:NSLocalizedString(@"确认", nil)
                          currentController:self
                                    handler:^(UIAlertAction * _Nonnull action) {
                [pwdGuardTF clearInputText];
                [pwdGuardTF becomeFirstResponderAction];
            }];
            return NO;
        }
    }
    
    return YES;
}

#pragma mark ================== 工具类型的方法 end ==================
#pragma mark ================== Get和Set方法 start ==================
-(NSArray *)messageOfTextField{
    if(!_messageOfTextField){
        _messageOfTextField = @[
            @{Question_Of_Pwd_Guard : NSLocalizedString(@"密保1:您最喜欢的人?", nil),
              Placeholder_Of_Pwd_Guard : NSLocalizedString(@"请输入您最喜欢的人", nil),
              Alert_To_Empty_Of_Pwd_Guard : NSLocalizedString(@"密保问题1不能为空!", nil),
              Alert_To_Error_Ans_Of_Pwd_Guard : NSLocalizedString(@"密保问题1的答案有误!", nil),
            },
            @{Question_Of_Pwd_Guard : NSLocalizedString(@"密保2:您的学校?", nil),
              Placeholder_Of_Pwd_Guard : NSLocalizedString(@"请输入您的学校", nil),
              Alert_To_Empty_Of_Pwd_Guard : NSLocalizedString(@"密保问题2不能为空!", nil),
              Alert_To_Error_Ans_Of_Pwd_Guard : NSLocalizedString(@"密保问题2的答案有误!", nil),
            },
            @{Question_Of_Pwd_Guard : NSLocalizedString(@"密保3:您最喜欢的电影?", nil),
              Placeholder_Of_Pwd_Guard : NSLocalizedString(@"请输入您最喜欢的电影", nil),
              Alert_To_Empty_Of_Pwd_Guard : NSLocalizedString(@"密保问题3不能为空!", nil),
              Alert_To_Error_Ans_Of_Pwd_Guard : NSLocalizedString(@"密保问题3的答案有误!", nil),
            },
        ];
    }
    return _messageOfTextField;
}
#pragma mark ================== Get和Set方法 end ==================
@end
