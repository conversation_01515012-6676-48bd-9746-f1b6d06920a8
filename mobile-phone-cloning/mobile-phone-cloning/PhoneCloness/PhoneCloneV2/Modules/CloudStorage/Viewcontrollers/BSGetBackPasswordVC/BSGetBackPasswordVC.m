//
//  BSGetBackPasswordVC.m
//  PhoneClone
//
//  Created by macbookair on 23/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSGetBackPasswordVC.h"
#import "BSVerifyPwdGuardVC.h"
#import "BSAWSDynamoDBMgr.h"
@interface BSGetBackPasswordVC ()

/// [账户名称]的输入框控件
@property (nonatomic, strong) BSTextField *accountTF;
@end

@implementation BSGetBackPasswordVC
- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    self.tabBarController.tabBar.hidden = YES;
}
- (instancetype)init{
    self = [super init];
    if (self) {
        NSArray *progressTextArray = @[NSLocalizedString(@"用户账号", nil),
                                       NSLocalizedString(@"验证身份", nil),
                                       NSLocalizedString(@"重置密码", nil),
                                       NSLocalizedString(@"完成", nil),];
        [self loadCustomWithTitle:NSLocalizedString(@"找回密码", nil)
                progressTextArray:progressTextArray
                  currentProgress:1
                        isNextBtn:YES];
        [self loadUserInterface];
    }
    return self;
}

-(void)loadUserInterface{
    self.accountTF = [[BSTextField alloc] init];
    self.bsTextFieldArr = @[self.accountTF];
    [self.accountTF loadCutomWithIconName:@"yonghuming" placeholder:NSLocalizedString(@"请输入邮箱或用户名", nil)];
    [self.view addSubview:self.accountTF];
    [self.accountTF mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mainProgressView.mas_bottom).offset(50);
        make.centerX.equalTo(@0);
        make.width.equalTo(self.view.mas_width).multipliedBy(0.8);
        make.height.equalTo(@50);
    }];
    
    [self.nextBtn addTarget:self action:@selector(nextBtnAction) forControlEvents:UIControlEventTouchUpInside];
}

- (void)nextBtnAction{
//    [self.accountTF setInputText:@"<EMAIL>"];
    //如果存在输入内容不合法,结束[下一步]操作
    if (![self checkTextFieldAction]) {
        return;
    }
    
    //弹出等待试图,等待app查询数据库中是否存储该邮箱
    [BSWaitingTool startWaitWithSuperview:self.view];
    [BSAWSDynamoDBMgr queryWithEmail:[self.accountTF getInputText] completionHandler:^(AWSDynamoDBQueryOutput * _Nonnull response, NSError * _Nonnull error) {
        //查询结束后,关闭等待试图
        dispatch_async(dispatch_get_main_queue(), ^{
            [BSWaitingTool stopWaitWithSuperview];
        });
        if (error) {
            BKdislog(error);
            return;
        }
        AWSDynamoDBQueryOutput *getItemOutput = response;
        if (getItemOutput.items.count == 0){
            //如果数据库不存在该账号,弹出该账号没有注册的提示
            dispatch_async(dispatch_get_main_queue(), ^{
                [BSAlertTool loadAlertOfMessage:NSLocalizedString(@"该账号不存在!", nil) okText:NSLocalizedString(@"确认", nil) currentController:self handler:^(UIAlertAction * _Nonnull action) {
                    [self.accountTF clearInputText];
                    [self.accountTF becomeFirstResponderAction];
                }];
            });
            return;
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            NSDictionary *itemDict = [getItemOutput.items firstObject];
            BSVerifyPwdGuardVC *verifyPwdGuardVC = [[BSVerifyPwdGuardVC alloc] init];
            AWSDynamoDBAttributeValue *email = itemDict[Email_Of_DynamoDB_Key];
            AWSDynamoDBAttributeValue *createTime = itemDict[CreateTime_Of_DynamoDB_Key];
            AWSDynamoDBAttributeValue *Q1_ans = itemDict[Q1_Ans_Of_DynamoDB_Key];
            AWSDynamoDBAttributeValue *Q2_ans = itemDict[Q2_Ans_Of_DynamoDB_Key];
            AWSDynamoDBAttributeValue *Q3_ans = itemDict[Q3_Ans_Of_DynamoDB_Key];
            verifyPwdGuardVC.accountModel = [BSAccountModel createObjectWithEmail:email.S
                                                                       createTime:createTime.S
                                                                           Q1_ans:Q1_ans.S
                                                                           q2_ans:Q2_ans.S
                                                                           q3_ans:Q3_ans.S];
            [self.navigationController pushViewController:verifyPwdGuardVC animated:YES];
        });
    }];
}

#pragma mark ================== 工具类型的方法 start ==================
/// 检验所有输入框输入内容是否符合规范
/// @return YES:所有输入内容全部合法  NO:存在输入内容不合法
- (BOOL)checkTextFieldAction{
    //输入框的空判断
    if([self.accountTF getInputText].length == 0){
        [BSAlertTool loadAlertOfMessage:NSLocalizedString(@"账号名称不能为空!", nil)
                                 okText:NSLocalizedString(@"确认", nil)
                      currentController:self
                                handler:^(UIAlertAction * _Nonnull action) {
            [self.accountTF becomeFirstResponderAction];
        }];
        return NO;
    }
    
    return YES;
}

#pragma mark ================== 工具类型的方法 end ==================
@end
