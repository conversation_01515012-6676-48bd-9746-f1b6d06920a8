//
//  BSResetPasswordVC.m
//  PhoneClone
//
//  Created by macbookair on 23/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSResetPasswordVC.h"
#import "BSAWSDynamoDBMgr.h"
#import "BSResetPwdFinishVC.h"
@interface BSResetPasswordVC ()
/// 输入框信息的集合数组
@property (nonatomic, strong) NSArray *messageOfTextField;
#define Icon_Name_Of_Account @"icon"  //图标名称的key
#define Placeholder_Of_Account @"placeholder"  //密保输入框的提示语的key
#define Alert_To_Empty_Of_Account @"alert_to_empty"  //输入框为空时的弹出框提示语
/// [账户密码]的输入框控件
@property (nonatomic, strong) BSTextField *passwordTF;
/// [确认密码]的输入框控件
@property (nonatomic, strong) BSTextField *pwdConfirmTF;

@end

@implementation BSResetPasswordVC
- (instancetype)init{
    self = [super init];
    if (self) {
        NSArray *progressTextArray = @[NSLocalizedString(@"用户账号", nil),
                                       NSLocalizedString(@"验证身份", nil),
                                       NSLocalizedString(@"重置密码", nil),
                                       NSLocalizedString(@"完成", nil),];
        [self loadCustomWithTitle:NSLocalizedString(@"找回密码", nil)
                progressTextArray:progressTextArray
                  currentProgress:3
                        isNextBtn:YES];
        [self loadUserInterface];
    }
    return self;
}

-(void)loadUserInterface{
    [self loadTextFieldsView];
    [self.nextBtn addTarget:self action:@selector(nextBtnAction) forControlEvents:UIControlEventTouchUpInside];
}

// 加载输入框集合的试图
- (void)loadTextFieldsView{
    self.passwordTF = [[BSTextField alloc] init];
    self.pwdConfirmTF = [[BSTextField alloc] init];
    self.bsTextFieldArr = @[
        self.passwordTF,
        self.pwdConfirmTF,
    ];
    
    //遍历加载输入框控件
    for (int i=0; i<self.messageOfTextField.count; i++) {
        NSDictionary *dic = self.messageOfTextField[i];
        BSTextField *accountTF = self.bsTextFieldArr[i];
        [accountTF loadCutomWithIconName:dic[Icon_Name_Of_Account]
                             placeholder:dic[Placeholder_Of_Account]];
        [self.view addSubview:accountTF];
        [accountTF mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(@0);
            make.width.equalTo(self.view.mas_width).multipliedBy(0.8);
            make.top.equalTo(self.mainProgressView.mas_bottom).offset(i*80);
            make.height.equalTo(@50);
        }];
    }
    self.passwordTF.mainTextField.secureTextEntry = YES;
    self.pwdConfirmTF.mainTextField.secureTextEntry = YES;
}


/// [下一步]点击事件
- (void)nextBtnAction{
//    [self.passwordTF setInputText:@"********"];
//    [self.pwdConfirmTF setInputText:@"********"];
    //如果存在输入内容不合法,结束[下一步]操作
    if (![self checkTextFieldAction]) {
        return;
    }
    
    //弹出等待试图,等待AWS进行密码重置操作
    [BSWaitingTool startWaitWithSuperview:self.view];
    [BSAWSDynamoDBMgr updateItemWithEmail:self.accountModel.email
                               createTime:self.accountModel.createTime
                              newPassword:[self.passwordTF getInputText]
                        completionHandler:^(AWSDynamoDBUpdateItemOutput * _Nonnull response, NSError * _Nonnull error) {
        //AWS密码重置操作结束后,关闭等待试图
        dispatch_async(dispatch_get_main_queue(), ^{
            [BSWaitingTool stopWaitWithSuperview];
        });
        if (error) {
            BKdislog(error);
            return;
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            BSResetPwdFinishVC *resetPwdFinishVC = [[BSResetPwdFinishVC alloc] init];
            [self.navigationController pushViewController:resetPwdFinishVC animated:YES];
        });
        BKdislog(@"密码重置成功!");
    }];
    
}
#pragma mark ================== 工具类型的方法 start ==================
/// 检验所有输入框输入内容是否符合规范
/// @return YES:所有输入内容全部合法  NO:存在输入内容不合法
- (BOOL)checkTextFieldAction{
    //输入框的空判断
    for (int i=0; i < self.bsTextFieldArr.count; i++) {
        BSTextField *accountTF = self.bsTextFieldArr[i];
        if ([accountTF getInputText].length == 0) {
            NSDictionary *messageOfPwdGuardDic = self.messageOfTextField[i];
            [BSAlertTool loadAlertOfMessage:messageOfPwdGuardDic[Alert_To_Empty_Of_Account]
                                     okText:NSLocalizedString(@"确认", nil)
                          currentController:self
                                    handler:^(UIAlertAction * _Nonnull action) {
                [accountTF becomeFirstResponderAction];
            }];
            return NO;
        }
    }
    
    // [输入密码]和[确认密码]是否一致
    NSString *password = [self.passwordTF getInputText];
    NSString *pwdConfirm = [self.pwdConfirmTF getInputText];
    if(![password isEqualToString:pwdConfirm]){
        [BSAlertTool loadAlertOfMessage:NSLocalizedString(@"两次输入密码不一致!", nil) okText:NSLocalizedString(@"确认", nil) currentController:self handler:^(UIAlertAction * _Nonnull action) {
            [self.passwordTF clearInputText];
            [self.pwdConfirmTF clearInputText];
            [self.passwordTF becomeFirstResponderAction];
        }];
        return NO;
    }
    
    return YES;
}
#pragma mark ================== 工具类型的方法 end ==================
#pragma mark ================== Get和Set方法 start ==================
/// 输入框信息的集合数组
-(NSArray *)messageOfTextField{
    if (!_messageOfTextField) {
        _messageOfTextField = @[
            @{Icon_Name_Of_Account : @"mima",
              Placeholder_Of_Account : NSLocalizedString(@"请输入密码", nil),
              Alert_To_Empty_Of_Account : NSLocalizedString(@"账号密码不能为空!", nil),
            },
            @{Icon_Name_Of_Account : @"mima",
              Placeholder_Of_Account : NSLocalizedString(@"请确认密码", nil),
              Alert_To_Empty_Of_Account : NSLocalizedString(@"确认密码不能为空!", nil),
            },
        ];
    }
    return _messageOfTextField;
}
#pragma mark ================== Get和Set方法 end ==================

@end
