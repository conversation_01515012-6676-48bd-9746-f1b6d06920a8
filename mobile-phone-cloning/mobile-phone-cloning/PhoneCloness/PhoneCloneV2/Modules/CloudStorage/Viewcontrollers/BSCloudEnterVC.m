//
//  BSCloudEnterVC.m
//  PhoneClone
//
//  Created by macbookair on 16/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSCloudEnterVC.h"
#import "BSCloudLoginVC.h"
#import "BSCloudStorageVC.h"
#import "BSVIPRechargeVC.h"
@interface BSCloudEnterVC ()
/// 等待提示的文本控件
@property (nonatomic, strong) UILabel *waitMessageLab;

@end

@implementation BSCloudEnterVC
- (void)viewDidLoad {
    [super viewDidLoad];
    self.waitMessageLab = [[UILabel alloc] init];
    self.waitMessageLab.text = NSLocalizedString(@"正在刷新界面...", nil);
    self.waitMessageLab.font = [UIFont boldSystemFontOfSize:20];
    self.waitMessageLab.numberOfLines = 1;
    self.waitMessageLab.adjustsFontSizeToFitWidth = YES;
    self.waitMessageLab.textAlignment = NSTextAlignmentCenter;
    [self.view addSubview:self.waitMessageLab];
    [self.waitMessageLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.centerY.equalTo(@0);
        make.width.equalTo(self.view.mas_width).multipliedBy(0.8);
        make.height.equalTo(@50);
    }];
    
    [self showBannerView:CGRectMake(0, self.view.jk_bottom-90, kDeviceWidth, 90) size:CGSizeMake(kDeviceWidth, 90)];
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    UIViewController *mainViewCtl;
    BOOL isLogin = [BSUserDefaultManager isExistLoginUser];
    BOOL isVip = [StoreIAPManager featureVip];
    if (isLogin == YES && isVip == YES) {
        [MobClick event:@"Subscribe" attributes:@{@"source":@"传输"}];
        
        //如果用户已经登录,并且订阅VIP有效未过期,直接进入[云存储]界面
        mainViewCtl = [[BSCloudStorageVC alloc] init];
    } else {
        //其他情况进入到[账号登录]界面
         mainViewCtl = [[BSCloudLoginVC alloc] init];
    }

    [self.navigationController pushViewController:mainViewCtl animated:NO];
}


@end
