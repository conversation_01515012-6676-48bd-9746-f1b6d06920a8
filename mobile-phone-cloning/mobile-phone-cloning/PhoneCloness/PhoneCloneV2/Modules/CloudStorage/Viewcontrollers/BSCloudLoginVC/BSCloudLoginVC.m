//
//  BSCloudLoginVC.m
//  PhoneClone
//
//  Created by macbookair on 16/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSCloudLoginVC.h"
#import "BSAWSDynamoDBMgr.h"
#import "BSCloudLoginHeadView.h"
#import "BSTextField.h"
#import "BSLabelButtonView.h"
#import "BSCloudRegisterVC.h"
#import "BSGetBackPasswordVC.h"
#import "BSVIPRechargeVC.h"
#import "NSString+Predicate.h"
@interface BSCloudLoginVC ()
/// 云存储登录界面的头部试图
@property (nonatomic, strong) BSCloudLoginHeadView *headView;
@property (nonatomic, strong) NSArray *messageOfTextField;
#define Icon_Name_Of_Account @"icon"  //图标名称的key
#define Placeholder_Of_Account @"placeholder"  //密保输入框的提示语的key
#define Alert_To_Empty_Of_Account @"alert_to_empty"  //输入框为空时的弹出框提示语
/// 用户账户的输入框控件
@property (nonatomic, strong) BSTextField *accountTF;
/// 密码的输入框控件
@property (nonatomic, strong) BSTextField *passwordTF;
/// [忘记密码]的按钮控件
@property (nonatomic, strong) UIButton *forgetPwdBtn;
/// [普通]输入框的数组集合
@property (nonatomic, strong) NSArray <BSTextField*> *bsTextFieldArr;

/// [登录]核心按钮
@property (nonatomic, strong) UIButton *mainLoginBtn;


/// 底部文本与按钮的组合控件
@property (nonatomic, strong) BSLabelButtonView *bottonLbBtnView;

@end

@implementation BSCloudLoginVC
/// 控制器试图出现前的调用方法
/// @param animated 是否动画
- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    //该控制器试图显示前,显示tabBar底部菜单栏控件
    self.tabBarController.tabBar.hidden = NO;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.fd_prefersNavigationBarHidden = YES;
    self.headView = [[BSCloudLoginHeadView alloc] init];
    [self.view addSubview:self.headView];
    [self.headView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@0);
        make.left.equalTo(@0);
        make.right.equalTo(@0);
        make.height.equalTo(@(1.0f/3 * kScreenHeight));
    }];
    [self.headView.vipBtn addTarget:self action:@selector(vipBtnAction) forControlEvents:UIControlEventTouchUpInside];
    self.headView.vipBtn.hidden = YES;
    
    self.accountTF = [[BSTextField alloc] init];
    self.passwordTF = [[BSTextField alloc] init];
    self.bsTextFieldArr = @[
        self.accountTF,
        self.passwordTF,
    ];
    for (int i=0; i<self.bsTextFieldArr.count; i++) {
        NSDictionary *dic = self.messageOfTextField[i];
        BSTextField *textField = self.bsTextFieldArr[i];
        [textField loadCutomWithIconName:dic[Icon_Name_Of_Account]
                             placeholder:dic[Placeholder_Of_Account]];
        [self.view addSubview:textField];
    }
    [self.accountTF mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.headView.mas_bottom).offset(60);
        make.centerX.equalTo(@0);
        make.width.equalTo(self.view.mas_width).multipliedBy(0.8);
        make.height.equalTo(@35);
    }];
    [self.passwordTF mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.accountTF.mas_bottom).offset(60);
        make.left.equalTo(self.accountTF.mas_left);
        make.right.equalTo(self.accountTF.mas_right);
        make.height.equalTo(self.accountTF.mas_height);
    }];
    self.passwordTF.mainTextField.secureTextEntry = YES;
    
    self.forgetPwdBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.forgetPwdBtn setTitle:NSLocalizedString(@"忘记密码?", nil) forState:UIControlStateNormal];
    self.forgetPwdBtn.titleLabel.numberOfLines = 1;
    self.forgetPwdBtn.titleLabel.adjustsFontSizeToFitWidth = YES;
    [self.view addSubview:self.forgetPwdBtn];
    [self.forgetPwdBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.passwordTF.mas_bottom);
        make.right.equalTo(self.passwordTF.mas_right);
        make.width.equalTo(self.passwordTF.mas_width).multipliedBy(1.0f/3.5);
        make.height.equalTo(@35);
    }];
    [self.forgetPwdBtn addTarget:self action:@selector(forgetPwdBtnAction) forControlEvents:UIControlEventTouchUpInside];
    
    self.mainLoginBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.mainLoginBtn setTitle:NSLocalizedString(@"登录", nil) forState:UIControlStateNormal];
    self.mainLoginBtn.titleLabel.font = [UIFont boldSystemFontOfSize:20];
    [self.mainLoginBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [self.mainLoginBtn setBackgroundColor:BKgetColorFrom(96, 178, 250, 1.0)];
    self.mainLoginBtn.layer.cornerRadius = 30.0f;
    self.mainLoginBtn.layer.masksToBounds = YES;
    [self.view addSubview:self.mainLoginBtn];
    [self.mainLoginBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.forgetPwdBtn.mas_bottom).offset(20);
        make.centerX.equalTo(@0);
        make.width.equalTo(self.view.mas_width).multipliedBy(0.8);
        make.height.equalTo(@60);
    }];
    [self.mainLoginBtn addTarget:self action:@selector(mainLoginBtnAction) forControlEvents:UIControlEventTouchUpInside];
    
    self.bottonLbBtnView = [[BSLabelButtonView alloc] init];
    [self.bottonLbBtnView loadCustomWithLabelText:NSLocalizedString(@"还没有账号?", nil) buttonText:NSLocalizedString(@"立即注册", nil)];
    [self.view addSubview:self.bottonLbBtnView];
    [self.bottonLbBtnView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mainLoginBtn.mas_bottom);
        make.centerX.equalTo(@0);
        make.width.equalTo(self.view.mas_width).multipliedBy(0.6);
        make.height.equalTo(@35);
    }];
    [self.bottonLbBtnView.rightBtn addTarget:self action:@selector(registerBtnAction) forControlEvents:UIControlEventTouchUpInside];
    
    //给全屏添加点击事件,取消所有输入框的输入法弹框,方便用户点击[确认]按钮
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(resignFirstResponderAcion)];
    [self.view addGestureRecognizer:tap];
}

- (void)mainLoginBtnAction{
   // [self.accountTF setInputText:@"<EMAIL>"];
   // [self.passwordTF setInputText:@"********"];
    //如果存在输入内容不合法,结束[下一步]操作
    if (![self checkTextFieldAction]) {
        return;
    }
    
    //网络请求AWSDynamoDB数据库前,[启动]等待交互,冻结界面
    [BSWaitingTool startWaitWithSuperview:self.view];
    [BSAWSDynamoDBMgr queryWithEmail:[self.accountTF getInputText] completionHandler:^(AWSDynamoDBQueryOutput * _Nonnull response, NSError * _Nonnull error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            //网络请求AWSDynamoDB数据库结束后,[关闭]等待交互,解锁界面
            [BSWaitingTool stopWaitWithSuperview];
        });
        if (error) {
            BKdislog(error);
        } else {
            AWSDynamoDBQueryOutput *getItemOutput = response;
            NSDictionary *itemDictionary = [getItemOutput.items firstObject];
            AWSDynamoDBAttributeValue *pwdValue = itemDictionary[Password_Of_DynamoDB_Key];
            dispatch_async(dispatch_get_main_queue(), ^{
                if ([[self.passwordTF getInputText] isEqualToString:pwdValue.S]) {
                    AWSDynamoDBAttributeValue *emailValue = itemDictionary[Email_Of_DynamoDB_Key];
                    AWSDynamoDBAttributeValue *pathValue = itemDictionary[Path_Of_DynamoDB_Key];
                    
                    [BSUserDefaultManager saveLoginUserWithUserModel:[BSAccountModel createObjectWithEmail:emailValue.S path:pathValue.S]];
                    //如果该用户尚未订阅VIP,将弹出VIP订阅界面
                    if(![StoreIAPManager featureVip]){
                        [MobClick event:@"Subscribe" attributes:@{@"source":@"云存储"}];
                        BSVIPRechargeVC *vipRechargeViewCtl = [[BSVIPRechargeVC alloc] init];
                        vipRechargeViewCtl.from = @"云存储";
                        vipRechargeViewCtl.modalPresentationStyle = 0;
                        [self presentViewController:vipRechargeViewCtl animated:YES completion:nil];
                        vipRechargeViewCtl.success = ^{
                            [self.navigationController popViewControllerAnimated:NO];
                        };
                        return;
                    }
                    
                    [self.navigationController popViewControllerAnimated:NO];
                    NSLog(@"用户登录成功>>邮箱=%@  保存路径=%@\n====================================", emailValue.S, pathValue.S);
                } else {
                    [BSAlertTool loadAlertOfMessage:NSLocalizedString(@"邮箱或者密码输入有误", nil)
                                             okText:NSLocalizedString(@"确认", nil)
                                  currentController:self
                                            handler:^(UIAlertAction * _Nonnull action) {
                        [self.accountTF clearInputText];
                        [self.passwordTF clearInputText];
                    }];
                    BKdislog(@"用户名或者密码错误!");
                }
            });
        }
    }];
}


/// [忘记密码]按钮的点击事件
- (void)forgetPwdBtnAction{
    BSGetBackPasswordVC *getBackPwdVC = [[BSGetBackPasswordVC alloc] init];
    [self.navigationController pushViewController:getBackPwdVC animated:YES];
}

/// [立即注册]按钮的点击事件
- (void)registerBtnAction{
    BSCloudRegisterVC *rgstViewCtl = [[BSCloudRegisterVC alloc] init];
    [self.navigationController pushViewController:rgstViewCtl animated:YES];
}

/// [vip充值]按钮的点击事件
- (void)vipBtnAction{
    BSVIPRechargeVC *vipRechargeViewCtl = [[BSVIPRechargeVC alloc] init];
    vipRechargeViewCtl.modalPresentationStyle = 0;
    [self presentViewController:vipRechargeViewCtl animated:YES completion:nil];
}

#pragma mark ================== 工具类型的方法 start ==================
/// 检验所有输入框输入内容是否符合规范
/// @return YES:所有输入内容全部合法  NO:存在输入内容不合法
- (BOOL)checkTextFieldAction{
    //输入框的空判断
    for (int i=0; i < self.bsTextFieldArr.count; i++) {
        BSTextField *accountTF = self.bsTextFieldArr[i];
        if ([accountTF getInputText].length == 0) {
            NSDictionary *messageOfPwdGuardDic = self.messageOfTextField[i];
            [BSAlertTool loadAlertOfMessage:messageOfPwdGuardDic[Alert_To_Empty_Of_Account]
                                     okText:NSLocalizedString(@"确认", nil)
                          currentController:self
                                    handler:^(UIAlertAction * _Nonnull action) {
                [accountTF becomeFirstResponderAction];
            }];
            return NO;
        }
    }
    
    //正则表达式的邮箱格式校验
    if(![NSString validateEmail:[self.accountTF getInputText]]){
        [BSAlertTool loadAlertOfMessage:NSLocalizedString(@"邮箱格式有误!", nil) okText:NSLocalizedString(@"确认", nil) currentController:self handler:^(UIAlertAction * _Nonnull action) {
            [self.accountTF clearInputText];
            [self.accountTF becomeFirstResponderAction];
        }];
        return NO;
    }
    
    return YES;
}

/// 取消所有输入框的输入法弹框(方便用户点击[确认]按钮)
- (void)resignFirstResponderAcion{
    [self.accountTF resignFirstResponderAction];
    [self.passwordTF resignFirstResponderAction];
}
#pragma mark ================== 工具类型的方法 end ==================

#pragma mark ================== Get和Set方法 start ==================
/// 输入框信息的集合数组
-(NSArray *)messageOfTextField{
    if (!_messageOfTextField) {
        _messageOfTextField = @[
            @{Icon_Name_Of_Account : @"yonghuming",
              Placeholder_Of_Account : NSLocalizedString(@"请输入您的邮箱", nil),
              Alert_To_Empty_Of_Account : NSLocalizedString(@"账号名称不能为空!", nil),
            },
            @{Icon_Name_Of_Account : @"mima",
              Placeholder_Of_Account : NSLocalizedString(@"请输入密码", nil),
              Alert_To_Empty_Of_Account : NSLocalizedString(@"账号密码不能为空!", nil),
            },
        ];
    }
    return _messageOfTextField;
}
#pragma mark ================== Get和Set方法 end ==================
@end
