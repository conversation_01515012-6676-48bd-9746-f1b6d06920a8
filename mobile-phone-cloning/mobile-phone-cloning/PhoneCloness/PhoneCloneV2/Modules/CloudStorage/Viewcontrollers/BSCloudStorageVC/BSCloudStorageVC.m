//
//  BSCloudStorageVC.m
//  PhoneClone
//
//  Created by macbookair on 9/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSCloudStorageVC.h"
#import "BSCloudStorageHeadView.h"
#import "BSCsBodyCellView.h"
#import "BSAccountModel.h"
#import "BSAWSS3Manager.h"
#import "BSVIPRechargeVC.h"
#import "BSAWSDynamoDBMgr.h"
#import <TZImagePickerController.h>
@interface BSCloudStorageVC ()<BSCsBodyCellViewProtocol, BSCloudStorageHeadViewProtocol, TZImagePickerControllerDelegate>
/// 用户账号的model对象
@property (nonatomic, strong) BSAccountModel *accountModel;

/// 云存储的头部试图
@property(nonatomic, strong) BSCloudStorageHeadView *headView;

/// 主体单元试图信息的数组集合
@property (nonatomic, strong) NSArray <NSDictionary*> *bodyCellViewInfoArr;
#define IconOfBodyCellView @"icon"
#define TitleOfBodyCellView @"title"
#define BtnTitleOfBodyCellView @"btnTitle"
#define BtnTagOfBodyCellView @"btnTag"
/// 按钮标识码
typedef NS_OPTIONS(NSUInteger, TagOfBodyCellView){
    UploadTag = 10001,
    DownloadTag = 10002,
};

/// 相册所有图像的数组集合
@property(nonatomic,strong) NSMutableArray <PHAsset*> *photoAssets;
/// 云存储所有图像的key集合
@property (nonatomic, strong) NSMutableArray <NSString*> *cloudStorageKeysMarr;

/// 图像上传的线程锁
@property(nonatomic, strong)NSLock *uploadLock;

/// 将要上传或者下载的数量
@property(nonatomic, assign)CGFloat uploadWillNum;

/// 已经完成上传或者下载的数量
@property(nonatomic, assign)CGFloat uploadDidNum;

@property (strong, nonatomic) IBOutlet UIProgressView *progressView;

@end

@implementation BSCloudStorageVC
/// 试图加载完成时的调用方法
- (void)viewDidLoad {
    [super viewDidLoad];
    //预先判断用户是否登录,或者用户登录是否过期
    if (![BSUserDefaultManager isExistLoginUser]) {
        //如果没有登录用户,强制退出云存储界面
        [self.navigationController popViewControllerAnimated:NO];
        return;
    }
    self.accountModel = [BSUserDefaultManager getUserModelByUserDefault];
    
    self.fd_prefersNavigationBarHidden = YES;
    self.view.backgroundColor = BKgetColorFrom(245, 245, 245, 1.0);
    
    /// 云存储的头部试图加载
    self.headView = [[BSCloudStorageHeadView alloc] init];
    [self.view addSubview:self.headView];
    [self.headView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_top);
        make.left.equalTo(self.view.mas_left);
        make.right.equalTo(self.view.mas_right);
        make.height.equalTo(@(1.0f/4*kScreenHeight));
    }];
    [self.headView setUsername:self.accountModel.email];
    [self.headView setlocalFilesNum:self.photoAssets.count];
    [self.headView setcloudFilesNum:0];
    self.headView.delegate = self;
    [self.headView.vipBtn addTarget:self action:@selector(vipBntAction) forControlEvents:UIControlEventTouchUpInside];
    // 主体单元试图加载
    CGFloat space = 29.0f;
    CGFloat bodyCellViewWidth = (kScreenWidth - 3*space)/2.0f;
    CGFloat bodyCellViewHeight = 1.0/4*kScreenHeight;
    for (int i = 0; i < self.bodyCellViewInfoArr.count; i++) {
        NSDictionary *infoDic = self.bodyCellViewInfoArr[i];
        NSInteger btnTag = [BKStrWithObj(infoDic[BtnTagOfBodyCellView]) integerValue];
        BSCsBodyCellView *bodyCellView = [[BSCsBodyCellView alloc] init];
        bodyCellView.backgroundColor = [UIColor whiteColor];
        [self.view addSubview:bodyCellView];
        [bodyCellView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.headView.mas_bottom).offset(space);
            make.left.equalTo(@(space*(i+1) + bodyCellViewWidth*i));
            make.width.equalTo(@(bodyCellViewWidth));
            make.height.equalTo(@(bodyCellViewHeight));
        }];
        [bodyCellView loadBodyCellViewWithIcon:infoDic[IconOfBodyCellView]
                                         title:infoDic[TitleOfBodyCellView]
                                      btnTitle:infoDic[BtnTitleOfBodyCellView]
                                           tag:btnTag];
        bodyCellView.bodyCellViewDelegate = self;
    }
    
    self.progressView = [[UIProgressView alloc] init];
    self.progressView.frame = CGRectMake(0.1f*kScreenWidth, 500, 0.8f*kScreenWidth, 30.0f);
    [self.view addSubview:self.progressView];
}

/// 控制器试图出现前的调用方法
/// @param animated 是否动画
- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    //该控制器试图显示前,显示tabBar底部菜单栏控件
    self.tabBarController.tabBar.hidden = NO;
    
    self.cloudStorageKeysMarr = [NSMutableArray array];
    //进行[检索云存储图像库]操作前,开启等待动画
    [BSWaitingTool startWaitWithSuperview:self.view];
    [BSAWSS3Manager getListObjectsWithPath:self.accountModel.path
                         completionHandler:^(AWSS3ListObjectsOutput * _Nonnull response, NSError * _Nonnull error) {
        //完成[检索云存储图像库]操作后,关闭等待动画
        dispatch_async(dispatch_get_main_queue(), ^{
            [BSWaitingTool stopWaitWithSuperview];
        });
        if(error){
            BKdislog(error);
            return;
        }
        //将检索到的云存储图像的key,保存到数组中
        dispatch_async(dispatch_get_main_queue(), ^{
            for (AWSS3Object *s3Object in response.contents) {
                [self.cloudStorageKeysMarr addObject:s3Object.key];
            }
            [self.headView setcloudFilesNum:response.contents.count];
        });
    }];
}

/// 主体单元试图的点击事件
/// @param tag 按钮标识码
- (void)clickActionOfBodyCellView:(NSInteger)tag{
    if (tag == UploadTag) {
        TZImagePickerController *imagePickerVc = [[TZImagePickerController alloc] initWithMaxImagesCount:255 delegate:self];
        [imagePickerVc setDidFinishPickingPhotosHandle:^(NSArray<UIImage *> *photos, NSArray *assets, BOOL isSelectOriginalPhoto) {
            self.uploadWillNum = photos.count;
            self.uploadDidNum = 0;
            [self updateProgressView];
            for (int i=0; i<self.uploadWillNum; i++) {
                UIImage *selectImg = photos[i];
                PHAsset *asset = assets[i];
                [self uploadWithImage:selectImg name:[asset valueForKey:@"filename"]];
            }
        }];
        [self presentViewController:imagePickerVc animated:YES completion:nil];
    } else if (tag == DownloadTag) {
        [self downloadAction];
    }
}

/// 上传到云端的方法
- (void)uploadWithImage:(UIImage*)image name:(NSString*)name{
    //预先判断用户是否登录,或者用户登录是否过期
    if (![BSUserDefaultManager isExistLoginUser]) {
        //如果没有登录用户,强制退出云存储界面
        [self.navigationController popViewControllerAnimated:NO];
        return;
    }
    [BSAWSS3Manager uploadWithImage:image
                               name:name
                               path:self.accountModel.path
                  completionHandler:^(AWSS3TransferUtilityUploadTask * _Nonnull task, NSError * _Nullable error) {
        //============完成后的回调代码块start==============
        if (error) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [BSWaitingTool stopWaitWithSuperview];
            });
            NSLog(@"Error: %@", error);
            return;
        } else {
            //每完成一个上传,更新一次进度条
            dispatch_async(dispatch_get_main_queue(), ^{
                self.uploadDidNum = self.uploadDidNum + 1;
                [self updateProgressView];
            });
            //上传操作结束后将锁打开
            [self.uploadLock unlock];
        }
        //============完成后的回调代码块end============
    } progressBlock:^(AWSS3TransferUtilityTask * _Nonnull task, NSProgress * _Nonnull progress) {
    } continueWithBlock:^id(AWSTask *task) {
        //============开始上传的回调代码块tart==============
        if (task.error) {
            NSLog(@"Error: %@", task.error);
            return nil;
        }
        if (task.result) {
            //上传操作开始即需要上锁
            [self.uploadLock lock];
        }
        return nil;
        //============开始上传的回调代码块end==============
    }];
}

/// 下载到本地的方法
- (void)downloadAction{
    //预先判断用户是否登录,或者用户登录是否过期
    if (![BSUserDefaultManager isExistLoginUser]) {
        //如果没有登录用户,强制退出云存储界面
        [self.navigationController popViewControllerAnimated:NO];
        return;
    }
    
    self.uploadWillNum = self.cloudStorageKeysMarr.count;
    self.uploadDidNum = 0;
    [self updateProgressView];
    for (NSString *key in self.cloudStorageKeysMarr) {
        NSString *fileName = [BSCloudStorageVC getFileNameWithKey:key];
        //如果本地已经存在该张照片,本地处理即可
        if([BSCloudStorageVC isExistTheImageInLocal:fileName]){
            [self saveImageToPhotoAlbumWithImageName:fileName];
            return;
        }
        //如果本地不存在该张照片,从云存储上面下载
        [BSAWSS3Manager downloadWithKey:key
                         saveToFileName:fileName
                      completionHandler:^(AWSS3GetObjectOutput * _Nonnull response, NSError * _Nonnull error) {
            if ((error)) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    [BSWaitingTool stopWaitWithSuperview];
                });
                BKdislog(error);
                return;
            }
            //每完成一个下载,更新一次进度条,并且将图片保存到相册中
            if (response){
                BKdislog(BKStr(@"图片 %@ 下载成功!", fileName));
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self saveImageToPhotoAlbumWithImageName:fileName];
                });
            }
        }];
    }
}

/// 退出用户登录
- (void)exitUserLoginAction{
    [BSUserDefaultManager exitLoginUser];
    [self.navigationController popViewControllerAnimated:NO];
}

/// 注销账户
- (void)deletUserLoginAction{
    //预先判断用户是否登录,或者用户登录是否过期
    if (![BSUserDefaultManager isExistLoginUser]) {
        //如果没有登录用户,强制退出云存储界面
        [self.navigationController popViewControllerAnimated:NO];
        return;
    }
    UIAlertController* al = [UIAlertController alertControllerWithTitle:local(@"提示") message:local(@"注销账号会永久删除您的云上所有文件！请问确认要注销账户吗？") preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction* act = [UIAlertAction actionWithTitle:local(@"确定") style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
        [SVProgressHUD show];
        [BSAWSDynamoDBMgr deleteUserWithEmail:self.accountModel.email createTime:self.accountModel.createTime completionHandler:^(AWSDynamoDBDeleteItemOutput * _Nonnull response, NSError * _Nonnull error) {
            [BSUserDefaultManager exitLoginUser];
            [BSAWSS3Manager getListObjectsWithPath:self.accountModel.path completionHandler:^(AWSS3ListObjectsOutput * _Nonnull response, NSError * _Nonnull error) {
                [BSAWSS3Manager deletePathWithPaths:response];
            }];
            dispatch_async(dispatch_get_main_queue(), ^{
                [SVProgressHUD dismiss];
                
                [self.navigationController popViewControllerAnimated:NO];
            });
            
        }];
        
    }];
    
    UIAlertAction* act2 = [UIAlertAction actionWithTitle:local(@"取消") style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        
    }];
    
    [al addAction:act];
    [al addAction:act2];
    [self presentViewController:al animated:YES completion:^{
        
    }];
}

/// 更新进度条进度
- (void)updateProgressView{
    //增加一个点击上传后,有响应的效果,优化用户体验
    if(self.uploadDidNum < self.uploadWillNum){
        [BSWaitingTool startWaitWithSuperview:self.view];
    } else {
        [BSWaitingTool stopWaitWithSuperview];
    }
    //=============================================
    [self.progressView setProgress:1.0f*self.uploadDidNum/self.uploadWillNum animated:YES];
}

- (void)vipBntAction{
    BSVIPRechargeVC *vipRechargeViewCtl = [[BSVIPRechargeVC alloc] init];
    vipRechargeViewCtl.modalPresentationStyle = 0;
    [self presentViewController:vipRechargeViewCtl animated:YES completion:nil];
}

#pragma mark ================== 工具类型的方法 start ==================
/// 通过云存储key获取文件名
/// @param key AWS云存储的文件key
+ (NSString*)getFileNameWithKey:(NSString*)key{
    NSArray *strArr = [key componentsSeparatedByString:@"/"];
    NSString *fileName = [strArr lastObject];
    return fileName;
}

+ (BOOL)isExistTheImageInLocal:(NSString*)imgName{
    NSArray *path = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    NSString *documentsPath = [path objectAtIndex:0]; //Get the docs directory
    NSString *imagePath = [documentsPath stringByAppendingPathComponent:imgName];
    NSFileHandle *hand= [NSFileHandle fileHandleForReadingAtPath:imagePath];
    if (hand == NULL) {
        return NO;
    }
    
    BKdislog(BKStr(@"图片 %@ 本地已经存在", imagePath));
    return YES;
}


/// 将下载图片保存到相册
/// @param imgName 图片名称
- (void)saveImageToPhotoAlbumWithImageName:(NSString*)imgName{
    NSArray *path = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    NSString *documentsPath = [path objectAtIndex:0]; //Get the docs directory
    NSString *imagePath = [documentsPath stringByAppendingPathComponent:imgName];
    UIImage *image = [UIImage imageWithContentsOfFile:imagePath];
    //将图片保存到相册
    UIImageWriteToSavedPhotosAlbum(image, NULL, NULL, NULL);
    //图像保存到相册后,删除缓存的图片
    //    [[NSFileManager defaultManager] removeItemAtPath:imagePath error:nil];
    
    self.uploadDidNum = self.uploadDidNum + 1;
    [self updateProgressView];
}
#pragma mark ================== 工具类型的方法 end ==================
#pragma mark ================== Get和Set方法 start ==================
/// 主体单元试图信息的数组集合
-(NSArray *)bodyCellViewInfoArr{
    if (!_bodyCellViewInfoArr) {
        _bodyCellViewInfoArr = @[
            @{IconOfBodyCellView:@"yunpanshangchuan", TitleOfBodyCellView:NSLocalizedString(@"上传到云端", nil), BtnTitleOfBodyCellView:NSLocalizedString(@"立即上传", nil), BtnTagOfBodyCellView:@(UploadTag)},
            @{IconOfBodyCellView:@"tianjiawenjianjia", TitleOfBodyCellView:NSLocalizedString(@"下载到本地", nil), BtnTitleOfBodyCellView:NSLocalizedString(@"立即下载", nil), BtnTagOfBodyCellView:@(DownloadTag)}
        ];
    }
    return _bodyCellViewInfoArr;
}

/// 相册所有图像的数组集合
- (NSMutableArray<PHAsset *> *)photoAssets{
    if (!_photoAssets) {
        _photoAssets = [NSMutableArray array];
        PHFetchOptions *option = [[PHFetchOptions alloc] init];
        option.sortDescriptors = @[[NSSortDescriptor sortDescriptorWithKey:@"creationDate" ascending:YES]];
        PHFetchResult *result = [PHAsset fetchAssetsWithMediaType:PHAssetMediaTypeImage options:option];
        for (id obj in result) {
            PHAsset *asset = (PHAsset *)obj;
            [_photoAssets addObject:asset];
        }
    }
    return _photoAssets;
}

/// 图像上传的线程锁
- (NSLock *)uploadLock{
    if (_uploadLock) {
        _uploadLock = [[NSLock alloc] init];
    }
    return _uploadLock;
}
#pragma mark ================== Get和Set方法 end ==================
@end
