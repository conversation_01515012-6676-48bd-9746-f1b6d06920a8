//
//  BSRegisterBaseVC.m
//  PhoneClone
//
//  Created by macbookair on 23/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSRegisterBaseVC.h"
@interface BSRegisterBaseVC ()

@end

@implementation BSRegisterBaseVC
- (void)viewDidLoad {
    [super viewDidLoad];
}

- (void)loadUserInterface{
    
}

/// 加载注册,忘记密码的基本试图架构
/// @param title 导航栏标题
/// @param textArray 进度条文本内容的集合数组
/// @param currentProgress 当前进度(从1开始)
/// @param isNextBtn 是否加载[下一步]按钮
- (instancetype)loadCustomWithTitle:(NSString*)title
                  progressTextArray:(NSArray*)textArray
                    currentProgress:(NSInteger)currentProgress
                          isNextBtn:(BOOL)isNextBtn{
    self.title = title;
    
    self.mainProgressView = [[BSRegisterProgressView alloc] init];
    [self.mainProgressView loadCustomWithProgressTextArray:textArray
                                           currentProgress:currentProgress
                                                 superview:self.view
                                                 setHeight:180.0f];
    
    /// [下一步]图标按钮控件
    if (isNextBtn) {
        self.nextBtn = [UIButton buttonWithType:UIButtonTypeSystem];
        [self.nextBtn setImage:[UIImage loadOriginalImage:@"xiayibu"] forState:UIControlStateNormal];
        [self.nextBtn.imageView setContentMode:UIViewContentModeCenter];
        [self.view addSubview:self.nextBtn];
        [self.nextBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(@-80);
            make.centerX.equalTo(@0);
            make.width.equalTo(@65);
            make.height.equalTo(@65);
        }];
    }
    
    //给全屏添加点击事件,取消所有输入框的输入法弹框,方便用户点击[确认]按钮
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(resignFirstResponderAcion)];
    [self.view addGestureRecognizer:tap];
    
    return self;
}

/// 取消所有输入框的输入法弹框(方便用户点击[确认]按钮)
- (void)resignFirstResponderAcion{
    for (BSTextField *textField in self.bsTextFieldArr) {
        [textField resignFirstResponderAction];
    }
    for (BSPwdGuardTextField *textField in self.bsPwdGuardTFArr) {
        [textField resignFirstResponderAction];
    }
}


@end
