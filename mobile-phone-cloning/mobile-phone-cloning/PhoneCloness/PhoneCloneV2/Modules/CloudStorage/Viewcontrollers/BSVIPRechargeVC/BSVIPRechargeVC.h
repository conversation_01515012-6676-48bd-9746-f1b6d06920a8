//
//  BSVIPRechargeVC.h
//  PhoneClone
//
//  Created by macbookair on 24/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
//这里是重点,整个代码块赋值类型以及返回值类型都在这里实现
typedef void(^SuccessOfSubscribeVip)(void);

@interface BSVIPRechargeVC : UIViewController

@property(nonatomic,copy) SuccessOfSubscribeVip success;
@property NSString* from;
@end

NS_ASSUME_NONNULL_END
