//
//  BSVIPRechargeVC.m
//  PhoneClone
//
//  Created by macbookair on 24/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSVIPRechargeVC.h"
#import "BSIconDetailsLabelView.h"
#import "BSVipRadioView.h"
#import "BSEtWebUrlView.h"
#import "Reachability.h"
#import "StoreIAPManager.h"
@interface BSVIPRechargeVC ()<MKStoreKitDelegate>
/// 头部logo图标的图像控件
@property (nonatomic, strong) UIImageView *headIconImgView;
/// 头部标题的文本控件(解锁所有功能)
@property (nonatomic, strong) UILabel *titleLab;

/// 详情介绍的文本控件(解锁更智能的一键克隆功能)
@property (nonatomic, strong) BSIconDetailsLabelView *detailsLabel01;
/// 详情介绍的文本控件(无广告体验)
@property (nonatomic, strong) BSIconDetailsLabelView *detailsLabel02;
@property (nonatomic, strong) BSVipRadioView *vipRadioView00;
@property (nonatomic, strong) BSVipRadioView *vipRadioView01;
@property (nonatomic, strong) BSVipRadioView *vipRadioView02;
@property (nonatomic, strong) BSVipRadioView *vipRadioView03;
/// VIP订阅单选框的标识码枚举
typedef NS_OPTIONS(NSUInteger, RechargeRadioTag){
    /// 第一个订阅选项控件的标识码(1-个月-¥22.00/每月)
    RechargeRadioTag00 = 10000,
    /// 第一个订阅选项控件的标识码(1-个月-¥22.00/每月)
    RechargeRadioTag01 = 10001,
    /// 第二个订阅选项控件的标识码(12-个月-¥148.00/每年)
    RechargeRadioTag02 = 10002,
    /// 第三个订阅选项控件的标识码(永久会员-永久使用权-¥163.00)
    RechargeRadioTag03 = 10003,
};
/// VIP订阅单选框的集合数组
@property (nonatomic, strong) NSArray <BSVipRadioView*> *vipRadioViewArr;
/// 选中的vip订阅单选框的tag
@property (nonatomic, assign) RechargeRadioTag selectVipRadioTag;

/// [立即开始]按钮控件
@property (nonatomic, strong) UIButton *mainBeginBtn;
/// 底部网页链接按钮的集合试图
@property (nonatomic, strong) BSEtWebUrlView *etWebUrlView;
/// WEB链接按钮的标识码枚举
typedef NS_OPTIONS(NSUInteger, WebUrlButtonTag){
    /// 第一个WEB链接按钮的标识码(隐私政策)
    WebUrlButtonTag01 = 30001,
    /// 第二个WEB链接按钮的标识码(恢复购买)
    WebUrlButtonTag02 = 30002,
    /// 第三个WEB链接按钮的标识码(使用条款)
    WebUrlButtonTag03 = 30003,
};
/// 订阅描述的文本控件
@property (nonatomic, strong) UILabel *subscribeDescribeLab;

/// [关闭]按钮的控件
@property (nonatomic, strong) UIButton *closeBtn;

@end

@implementation BSVIPRechargeVC
- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    self.tabBarController.tabBar.hidden = YES;
    self.navigationController.navigationBarHidden = YES;
}
- (BOOL)prefersStatusBarHidden
{
    return YES;//隐藏为YES，显示为NO
}
- (void)viewDidLoad {
    [super viewDidLoad];
    [self loadHeadView];
    [self loadVipRadioAndMainBtnView];
    [self loadWebUrlBtnAndSubscribeDescribeView];
    
    if (@available(iOS 13.0, *)) {
        self.closeBtn = [UIButton buttonWithType:UIButtonTypeClose];
    } else {
        self.closeBtn = [UIButton buttonWithType:UIButtonTypeSystem];
        [self.closeBtn setTitle:@"X" forState:UIControlStateNormal];
        self.closeBtn.titleLabel.font = [UIFont boldSystemFontOfSize:17];
        [self.closeBtn setTintColor:[UIColor darkGrayColor]];
        [self.closeBtn setBackgroundColor:[UIColor lightGrayColor]];
        self.closeBtn.layer.cornerRadius = 15.0f;
        self.closeBtn.layer.masksToBounds = YES;
    }
    [self.view addSubview:self.closeBtn];
    [self.closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(15));
        make.left.equalTo(@20);
        make.width.equalTo(@30);
        make.height.equalTo(@30);
    }];
    [self.closeBtn addTarget:self action:@selector(closeBtnAction) forControlEvents:UIControlEventTouchUpInside];
}

/// 加载头部试图(headIconImgView titleLab detailsLabel01 detailsLabel02)
- (void)loadHeadView{
    self.headIconImgView = [[UIImageView alloc] init];
    self.headIconImgView.image = [UIImage imageNamed:@"logo.png"];
    [self.view addSubview:self.headIconImgView];
    [self.headIconImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@30);
        make.centerX.equalTo(@0);
        make.width.equalTo(@90);
        make.height.equalTo(@90);
    }];
    
    self.titleLab = [[UILabel alloc] init];
    self.titleLab.text = NSLocalizedString(@"解锁所有功能", nil);
    self.titleLab.font = [UIFont boldSystemFontOfSize:25];
    self.titleLab.textAlignment = NSTextAlignmentCenter;
    self.titleLab.numberOfLines = 1;
    self.titleLab.adjustsFontSizeToFitWidth = YES;
    [self.view addSubview:self.titleLab];
    [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.headIconImgView.mas_bottom);
        make.centerX.equalTo(@0);
        make.width.equalTo(self.view.mas_width).multipliedBy(0.6);
        make.height.equalTo(@40);
    }];
    
    self.detailsLabel01 = [BSIconDetailsLabelView loadCucstomWithIcon:@"duigou" details:NSLocalizedString(@"● 文件传输不再限制次数", nil)];
    [self.view addSubview:self.detailsLabel01];
    [self.detailsLabel01 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLab.mas_bottom).offset(20);
        make.centerX.equalTo(@0);
        make.width.equalTo(self.view.mas_width).multipliedBy(0.8);
        make.height.equalTo(@40);
    }];
    
    self.detailsLabel02 = [BSIconDetailsLabelView loadCucstomWithIcon:@"duigou" details:NSLocalizedString(@"● 解锁云存储的功能", nil)];
    [self.view addSubview:self.detailsLabel02];
    [self.detailsLabel02 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.detailsLabel01.mas_bottom).offset(10);
        make.centerX.equalTo(@0);
        make.width.equalTo(self.view.mas_width).multipliedBy(0.8);
        make.height.equalTo(@40);
    }];
}

/// 加载主体试图(vipRadioView01 vipRadioView02 vipRadioView03 mainBeginBtn)
- (void)loadVipRadioAndMainBtnView{
    NSString *weekPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureWId"];
    NSString *monthPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureAId"];
    NSString *yeraPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureBId"];
    NSString *buyPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureCId"];

    // 修复逻辑错误并使用新的默认价格
    if (!weekPrice) {
        weekPrice = @"¥12";
    }
    if (!monthPrice) {
        monthPrice = @"¥22";
    }
    if (!yeraPrice) {
        yeraPrice = @"¥138";
    }
    if (!buyPrice) {
        buyPrice = @"¥168";
    }
    self.vipRadioView00 = [BSVipRadioView loadCustomWithVipContent:[NSString stringWithFormat:NSLocalizedString(@"1-个周-%@/每周",nil),weekPrice]
                                                               tag:RechargeRadioTag00];
    self.vipRadioView01 = [BSVipRadioView loadCustomWithVipContent:[NSString stringWithFormat:NSLocalizedString(@"1-个月-%@/每月",nil),monthPrice]
                                                               tag:RechargeRadioTag01];
    self.vipRadioView02 = [BSVipRadioView loadCustomWithVipContent:[NSString stringWithFormat:NSLocalizedString(@"12-个月-%@/每年",nil),yeraPrice]
                                                               tag:RechargeRadioTag02];
    self.vipRadioView03 = [BSVipRadioView loadCustomWithVipContent:[NSString stringWithFormat:NSLocalizedString(@"永久会员-永久使用权-%@",nil),buyPrice]
                                                               tag:RechargeRadioTag03];
    self.vipRadioViewArr = @[
        self.vipRadioView00,
        self.vipRadioView01,
        self.vipRadioView02,
        self.vipRadioView03,
    ];
    NSInteger viewCount = self.vipRadioViewArr.count;
    CGFloat space = 15.0f;
    CGFloat width = 1.0f*(kScreenWidth - space*(viewCount+1))/viewCount;
    UIScrollView* sc = [UIScrollView new];
    [self.view addSubview:sc];
    [sc mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.detailsLabel02.mas_bottom).offset(40);
        make.left.right.mas_equalTo(0);
        make.height.equalTo(@133);
    }];
    
    for (int i=0; i<self.vipRadioViewArr.count; i++) {
        BSVipRadioView *vipRadioView = self.vipRadioViewArr[i];
        [sc addSubview:vipRadioView];
        [vipRadioView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(0);
            make.left.equalTo(@(space + (width+space)*i));
            make.width.equalTo(@(width));
            make.height.equalTo(@133);
            if(i==self.vipRadioViewArr.count-1)
            {
                make.right.mas_equalTo(-space);
            }
        }];
        [vipRadioView addTarget:self action:@selector(vipRadioClickAction:) forControlEvents:UIControlEventTouchUpInside];
        if ([[NSUserDefaults standardUserDefaults] boolForKey:@"checkKeyifOn"]) {
            vipRadioView.hidden = YES;
        }
    }
    [self.vipRadioView00 setSelectRadioWithState:YES];
    self.selectVipRadioTag = RechargeRadioTag00;
    
    self.mainBeginBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    if ([[NSUserDefaults standardUserDefaults] boolForKey:@"checkKeyifOn"]) {
        
        [self.mainBeginBtn setTitle:NSLocalizedString(@"立即体验", nil) forState:UIControlStateNormal];
    }else
    {
        [self.mainBeginBtn setTitle:NSLocalizedString(@"立即订阅", nil) forState:UIControlStateNormal];
    }
    self.mainBeginBtn.titleLabel.font = [UIFont boldSystemFontOfSize:20];
    [self.mainBeginBtn setTintColor:[UIColor whiteColor]];
    [self.mainBeginBtn setBackgroundColor:BKgetColorFrom(96, 178, 250, 1.0)];
    self.mainBeginBtn.layer.cornerRadius = 25.0;
    self.mainBeginBtn.layer.masksToBounds = YES;
    [self.view addSubview:self.mainBeginBtn];
    [self.mainBeginBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.vipRadioView01.mas_bottom).offset(20);
        make.centerX.equalTo(@0);
        make.width.equalTo(self.view.mas_width).multipliedBy(0.8);
        make.height.equalTo(@50);
    }];
    [self.mainBeginBtn addTarget:self action:@selector(mainBeginBtnAction) forControlEvents:UIControlEventTouchUpInside];
    if ([[NSUserDefaults standardUserDefaults] boolForKey:@"checkKeyifOn"]) {
        UILabel * showpricelabel = [[UILabel alloc] init];
        showpricelabel.text = [NSString stringWithFormat:NSLocalizedString(@"免费试用3天,之后%@/每年",nil),yeraPrice];
        showpricelabel.textColor = [UIColor grayColor];
        showpricelabel.textAlignment = NSTextAlignmentCenter;
        showpricelabel.font = [UIFont boldSystemFontOfSize:13];
        [self.view addSubview:showpricelabel];
        [showpricelabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.vipRadioView01.mas_bottom).offset(-60);
            make.centerX.equalTo(@0);
            make.width.equalTo(self.view.mas_width).multipliedBy(0.8);
            make.height.equalTo(@50);
        }];
    }
}

/// 加载尾试图(etWebUrlView subscribeDescribeLab)
- (void)loadWebUrlBtnAndSubscribeDescribeView{
    NSArray *titleArr = @[
        NSLocalizedString(@"隐私政策", nil),
        NSLocalizedString(@"恢复", nil),
        NSLocalizedString(@"使用条款", nil)
    ];
    NSArray *webUrlBtnTagArr = @[
        @(WebUrlButtonTag01),
        @(WebUrlButtonTag02),
        @(WebUrlButtonTag03),
    ];
    self.etWebUrlView = [[BSEtWebUrlView alloc] init];
    [self.view addSubview:self.etWebUrlView];
    [self.etWebUrlView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.view.mas_centerX);
        make.width.equalTo(self.view.mas_width).multipliedBy(0.6);
        make.top.equalTo(self.mainBeginBtn.mas_bottom).offset(10);
        make.height.equalTo(@15);
    }];
    [self.etWebUrlView loadCustomWithTitleArray:titleArr tagArray:webUrlBtnTagArr];
    for (UIButton *webUrlBtn in self.etWebUrlView.webUrlBtnMarr) {
        [webUrlBtn addTarget:self action:@selector(webUrlBtnAction:) forControlEvents:UIControlEventTouchUpInside];
    }
    
    self.subscribeDescribeLab = [[UILabel alloc] init];
    self.subscribeDescribeLab.text = NSLocalizedString(@"SubscriptionInstruction", nil);
    self.subscribeDescribeLab.numberOfLines = 0;
    self.subscribeDescribeLab.adjustsFontSizeToFitWidth = YES;
    self.subscribeDescribeLab.textColor = [UIColor lightGrayColor];
    [self.view addSubview:self.subscribeDescribeLab];
    [self.subscribeDescribeLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.etWebUrlView.mas_bottom).offset(5);
        make.centerX.equalTo(@0);
        make.width.equalTo(self.view.mas_width).multipliedBy(0.9);
        make.bottom.equalTo(@-15);
    }];
}

- (void)vipRadioClickAction:(BSVipRadioView*)sender{
    for (BSVipRadioView *vipRadioView in self.vipRadioViewArr) {
        [vipRadioView setSelectRadioWithState:NO];
    }
    [sender setSelectRadioWithState:YES];
    self.selectVipRadioTag = sender.tag;
}

- (void)mainBeginBtnAction{
    [BSWaitingTool startWaitWithSuperview:self.view];
    Reachability *reach = [Reachability reachabilityWithHostName:@"www.apple.com"];
    NetworkStatus status = [reach currentReachabilityStatus];
    if (status == NotReachable) {
        [BSAlertTool loadAlertOfMessage:@"当前网络不可用" okText:@"确认" currentController:self handler:nil];
        return;
    }
    [StoreIAPManager sharedManager].delegate = self;
    switch (self.selectVipRadioTag) {
        case RechargeRadioTag00://第一个订阅选项控件的标识码(1-个月-¥22.00/每月)
            [[StoreIAPManager sharedManager] buyFeatureW];
            break;
        case RechargeRadioTag01://第一个订阅选项控件的标识码(1-个月-¥22.00/每月)
            [[StoreIAPManager sharedManager] buyFeatureA];
            break;
            
        case RechargeRadioTag02://第二个订阅选项控件的标识码(1个月使用期限 ¥22/月)
            [[StoreIAPManager sharedManager] buyFeatureB];
            break;
            
        case RechargeRadioTag03://第三个订阅选项控件的标识码(永久会员-永久使用权-¥163.00)
            [[StoreIAPManager sharedManager] buyFeatureC];
            break;
    }
}

/// 底部网页链接按钮的点击事件
- (void)webUrlBtnAction:(UIButton*)sender{
    switch (sender.tag) {
        case WebUrlButtonTag01:
            //隐私政策
            [[UIApplication sharedApplication] openURL:[NSURL URLWithString:@"https://www.freeprivacypolicy.com/privacy/view/2e6556241582cf0a67fbb9b06cd98b0d"]];
            break;
            
        case WebUrlButtonTag02:
            [[StoreIAPManager sharedManager] restoreButClick:^{}];
            break;
            
        case WebUrlButtonTag03:
            //服务条款
            [[UIApplication sharedApplication] openURL:[NSURL URLWithString:@"https://www.freeprivacypolicy.com/privacy/view/2e6556241582cf0a67fbb9b06cd98b0d"]];
            break;
    }
}

- (void)closeBtnAction{
    [self dismissViewControllerAnimated:YES completion:nil];
    [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"fromscreenshow"];
}

#pragma mark - MKStoreKitDelegate - 发起内购请求后的回调方法
/// 购买成功后的回调方法
-(void)buySuccessBack{
    [BSWaitingTool stopWaitWithSuperview];
    BKdislog(@"订阅成功!");
}

/// 购买失败后的回调方法
- (void)failed{
    [BSWaitingTool stopWaitWithSuperview];
    BKdislog(@"订阅失败!");
}

/// 订阅产品A后的回调方法
- (void)productAPurchased{
    [BSWaitingTool stopWaitWithSuperview];
    [self dismissViewControllerAnimated:YES completion:nil];
    if (self.success != nil) {
        self.success();
    }
    else
    {
        [self backAction];
    }
    [MobClick event:@"Subscribe" attributes:@{@"succeed":[NSString stringWithFormat:@"%@ %@ 购买成功",self.from,@"A套餐"]}];
    BKdislog(@"A套餐订阅成功!");
}

/// 订阅产品B后的回调方法
- (void)productBPurchased{
    [BSWaitingTool stopWaitWithSuperview];
    [self dismissViewControllerAnimated:YES completion:nil];
    if (self.success != nil) {
        self.success();
    }
    else
    {
        [self backAction];
    }
    BKdislog(@"B套餐订阅成功!");
    [MobClick event:@"Subscribe" attributes:@{@"succeed":[NSString stringWithFormat:@"%@ %@ 购买成功",self.from,@"B套餐"]}];
}

/// 订阅产品C后的回调方法
- (void)productCPurchased{
    [BSWaitingTool stopWaitWithSuperview];
    [self dismissViewControllerAnimated:YES completion:nil];
    if (self.success != nil) {
        self.success();
    }
    else
    {
        [self backAction];
    }
    BKdislog(@"C套餐订阅成功!");
    [MobClick event:@"Subscribe" attributes:@{@"succeed":[NSString stringWithFormat:@"%@ %@ 购买成功",self.from,@"C套餐"]}];
}

- (void)backAction
{
    if(self.navigationController&&self.navigationController.viewControllers.count>1)
    {
        [self.navigationController popViewControllerAnimated:YES];
    }
    else
    {
        [self dismissViewControllerAnimated:YES completion:^{
            
        }];
    }
}

- (void)productWPurchased{
    [BSWaitingTool stopWaitWithSuperview];
    [self dismissViewControllerAnimated:YES completion:nil];
    if (self.success != nil) {
        self.success();
    }
    else
    {
        [self backAction];
    }
    BKdislog(@"W套餐订阅成功!");
    [MobClick event:@"Subscribe" attributes:@{@"succeed":[NSString stringWithFormat:@"%@ %@ 购买成功",self.from,@"W套餐"]}];
}
/// 取消订阅后的回调方法
- (void)cancelVipViewction{
    [self dismissViewControllerAnimated:YES completion:nil];
    BKdislog(@"已经取消订阅!");
}
#pragma mark =====================================================
@end
