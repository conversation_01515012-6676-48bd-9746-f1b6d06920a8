//
//  BSRegisterBaseVC.h
//  PhoneClone
//
//  Created by macbookair on 23/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "BSRegisterProgressView.h"
#import "BSTextField.h"
#import "BSPwdGuardTextField.h"
NS_ASSUME_NONNULL_BEGIN
@protocol BSRegisterBaseVCProtocol <NSObject>
/// 加载控制器的核心试图(子类必须重写该方法)
@required
- (void)loadUserInterface;
@end

@interface BSRegisterBaseVC : UIViewController
/// 顶部注册进度试图
@property (nonatomic, strong) BSRegisterProgressView *mainProgressView;
/// [下一步]图标按钮控件
@property (nonatomic, strong) UIButton *nextBtn;
/// [普通]输入框的数组集合
@property (nonatomic, strong) NSArray <BSTextField*> *bsTextFieldArr;
/// [密保]输入框的数组集合
@property (nonatomic, strong) NSArray <BSPwdGuardTextField*> *bsPwdGuardTFArr;

/// 加载注册,忘记密码的基本试图架构
/// @param title 导航栏标题
/// @param textArray 进度条文本内容的集合数组
/// @param currentProgress 当前进度(从1开始)
/// @param isNextBtn 是否加载[下一步]按钮
- (instancetype)loadCustomWithTitle:(NSString*)title
progressTextArray:(NSArray*)textArray
  currentProgress:(NSInteger)currentProgress
        isNextBtn:(BOOL)isNextBtn;
/// 加载控制器的主体试图(子类务必重写该方法)
- (void)loadUserInterface;
@end

NS_ASSUME_NONNULL_END
