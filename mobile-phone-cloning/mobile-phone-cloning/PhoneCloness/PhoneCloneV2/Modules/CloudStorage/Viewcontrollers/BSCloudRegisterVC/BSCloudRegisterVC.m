//
//  BSCloudRegisterVC.m
//  PhoneClone
//
//  Created by macbookair on 16/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSCloudRegisterVC.h"
#import "BSAWSDynamoDBMgr.h"
#import "BSRegisterProgressView.h"
#import "BSTextField.h"
#import "BSPasswordGuardVC.h"
#import "NSString+Predicate.h"
@interface BSCloudRegisterVC ()
/// 输入框信息的集合数组
@property (nonatomic, strong) NSArray *messageOfTextField;
#define Icon_Name_Of_Account @"icon"  //图标名称的key
#define Placeholder_Of_Account @"placeholder"  //密保输入框的提示语的key
#define Alert_To_Empty_Of_Account @"alert_to_empty"  //输入框为空时的弹出框提示语
/// [账户名称]的输入框控件
@property (nonatomic, strong) BSTextField *accountTF;
/// [账户密码]的输入框控件
@property (nonatomic, strong) BSTextField *passwordTF;
/// [确认密码]的输入框控件
@property (nonatomic, strong) BSTextField *pwdConfirmTF;

@end

@implementation BSCloudRegisterVC
/// 试图出现前的调用方法
/// @param animated 是否动画
- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    self.tabBarController.tabBar.hidden = YES;
}
- (instancetype)init{
    self = [super init];
    if (self) {
        NSArray *progressTextArray = @[NSLocalizedString(@"账号信息", nil),
                                       NSLocalizedString(@"密保设置", nil),
                                       NSLocalizedString(@"完成", nil),];
        [self loadCustomWithTitle:NSLocalizedString(@"注册", nil)
                progressTextArray:progressTextArray
                  currentProgress:1
                        isNextBtn:YES];
        [self loadUserInterface];
    }
    return self;
}

/// 加载控制器的核心试图
- (void)loadUserInterface{
    [self loadTextFieldsView];
    [self.nextBtn addTarget:self action:@selector(nextBtnAction) forControlEvents:UIControlEventTouchUpInside];
}

/// 加载输入框集合的试图
- (void)loadTextFieldsView{
    self.accountTF = [[BSTextField alloc] init];
    self.passwordTF = [[BSTextField alloc] init];
    self.pwdConfirmTF = [[BSTextField alloc] init];
    self.bsTextFieldArr = @[
        self.accountTF,
        self.passwordTF,
        self.pwdConfirmTF,
    ];
    
    //遍历加载输入框控件
    for (int i=0; i<self.messageOfTextField.count; i++) {
        NSDictionary *dic = self.messageOfTextField[i];
        BSTextField *accountTF = self.bsTextFieldArr[i];
        [accountTF loadCutomWithIconName:dic[Icon_Name_Of_Account]
                             placeholder:dic[Placeholder_Of_Account]];
        [self.view addSubview:accountTF];
        [accountTF mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(@0);
            make.width.equalTo(self.view.mas_width).multipliedBy(0.8);
            make.top.equalTo(self.mainProgressView.mas_bottom).offset(i*80);
            make.height.equalTo(@50);
        }];
    }
    self.passwordTF.mainTextField.secureTextEntry = YES;
    self.pwdConfirmTF.mainTextField.secureTextEntry = YES;
}


/// [账号信息注册]的[下一步]点击事件
- (void)nextBtnAction{
//    [self.accountTF setInputText:@"<EMAIL>"];
//    [self.passwordTF setInputText:@"********"];
//    [self.pwdConfirmTF setInputText:@"********"];
    //如果存在输入内容不合法,结束[下一步]操作
    if (![self checkTextFieldAction]) {
        return;
    }
    
    //弹出等待试图,等待app查询数据库中是否存储该邮箱
    [BSWaitingTool startWaitWithSuperview:self.view];
    [BSAWSDynamoDBMgr queryWithEmail:[self.accountTF getInputText]
                   completionHandler:^(AWSDynamoDBQueryOutput * _Nonnull response, NSError * _Nonnull error) {
        //查询结束后,关闭等待试图
        dispatch_async(dispatch_get_main_queue(), ^{
            [BSWaitingTool stopWaitWithSuperview];
        });
        if (error) {
            BKdislog(error);
            return;
        }
        AWSDynamoDBQueryOutput *getItemOutput = response;
        if (getItemOutput.items.count != 0) {
            //如果数据库存在该邮箱,弹出已经被注册的提示
            dispatch_async(dispatch_get_main_queue(), ^{
                [BSAlertTool loadAlertOfMessage:NSLocalizedString(@"该邮箱已经被注册!", nil) okText:NSLocalizedString(@"确认", nil) currentController:self handler:^(UIAlertAction * _Nonnull action) {
                    [self.accountTF clearInputText];
                    [self.accountTF becomeFirstResponderAction];
                }];
            });
            return;
        } else {
            //如果数据库不存在该邮箱,进入到[密码保护注册]界面
            dispatch_async(dispatch_get_main_queue(), ^{
                [self gotoPwdGuardRegisterView];
            });
        }
    }];
}

/// 前往[密码保护注册]试图
- (void)gotoPwdGuardRegisterView{
    BSPasswordGuardVC *passwordGuardViewCtl = [[BSPasswordGuardVC alloc] init];
    passwordGuardViewCtl.accountModel = [BSAccountModel createObjectWithEmail:[self.accountTF getInputText]
                                                                     password:[self.passwordTF getInputText]];
    [self.navigationController pushViewController:passwordGuardViewCtl animated:YES];
}

#pragma mark ================== 工具类型的方法 start ==================
/// 检验所有输入框输入内容是否符合规范
/// @return YES:所有输入内容全部合法  NO:存在输入内容不合法
- (BOOL)checkTextFieldAction{
    //输入框的空判断
    for (int i=0; i < self.bsTextFieldArr.count; i++) {
        BSTextField *accountTF = self.bsTextFieldArr[i];
        if ([accountTF getInputText].length == 0) {
            NSDictionary *messageOfPwdGuardDic = self.messageOfTextField[i];
            [BSAlertTool loadAlertOfMessage:messageOfPwdGuardDic[Alert_To_Empty_Of_Account]
                                     okText:NSLocalizedString(@"确认", nil)
                          currentController:self
                                    handler:^(UIAlertAction * _Nonnull action) {
                [accountTF becomeFirstResponderAction];
            }];
            return NO;
        }
    }
    
    //正则表达式的邮箱格式校验
    if(![NSString validateEmail:[self.accountTF getInputText]]){
        [BSAlertTool loadAlertOfMessage:NSLocalizedString(@"邮箱格式有误!", nil) okText:NSLocalizedString(@"确认", nil) currentController:self handler:^(UIAlertAction * _Nonnull action) {
            [self.accountTF clearInputText];
            [self.accountTF becomeFirstResponderAction];
        }];
        return NO;
    }
    
    // [输入密码]和[确认密码]是否一致
    NSString *password = [self.passwordTF getInputText];
    NSString *pwdConfirm = [self.pwdConfirmTF getInputText];
    if(![password isEqualToString:pwdConfirm]){
        [BSAlertTool loadAlertOfMessage:NSLocalizedString(@"两次输入密码不一致!", nil) okText:NSLocalizedString(@"确认", nil) currentController:self handler:^(UIAlertAction * _Nonnull action) {
            [self.passwordTF clearInputText];
            [self.pwdConfirmTF clearInputText];
            [self.passwordTF becomeFirstResponderAction];
        }];
        return NO;
    }
    
    return YES;
}

#pragma mark ================== 工具类型的方法 end ==================
#pragma mark ================== Get和Set方法 start ==================
/// 输入框信息的集合数组
-(NSArray *)messageOfTextField{
    if (!_messageOfTextField) {
        _messageOfTextField = @[
            @{Icon_Name_Of_Account : @"yonghuming",
              Placeholder_Of_Account : NSLocalizedString(@"请输入您的邮箱", nil),
              Alert_To_Empty_Of_Account : NSLocalizedString(@"账号名称不能为空!", nil),
            },
            @{Icon_Name_Of_Account : @"mima",
              Placeholder_Of_Account : NSLocalizedString(@"请输入密码", nil),
              Alert_To_Empty_Of_Account : NSLocalizedString(@"账号密码不能为空!", nil),
            },
            @{Icon_Name_Of_Account : @"mima",
              Placeholder_Of_Account : NSLocalizedString(@"请确认密码", nil),
              Alert_To_Empty_Of_Account : NSLocalizedString(@"确认密码不能为空!", nil),
            },
        ];
    }
    return _messageOfTextField;
}
#pragma mark ================== Get和Set方法 end ==================
@end
