//
//  BSRegisterFinishVC.m
//  PhoneClone
//
//  Created by macbookair on 22/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSRegisterFinishVC.h"

@interface BSRegisterFinishVC ()
/// [恭喜您账号注册成功啦]的文本控件
@property (nonatomic, strong) UILabel *messageLab;
/// [立即登录]的按钮控件
@property (nonatomic, strong) UIButton *mainBtn;
@end

@implementation BSRegisterFinishVC
- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    self.navigationController.navigationBarHidden = YES;
}
- (instancetype)init{
    self = [super init];
    if (self) {
        NSArray *progressTextArray = @[NSLocalizedString(@"账号信息", nil),
                                       NSLocalizedString(@"密保设置", nil),
                                       NSLocalizedString(@"完成", nil),];
        [self loadCustomWithTitle:NSLocalizedString(@"注册", nil)
                progressTextArray:progressTextArray
                  currentProgress:3 isNextBtn:NO];
        [self loadUserInterface];
    }
    return self;
}

-(void)loadUserInterface{
    self.messageLab = [[UILabel alloc] init];
    self.messageLab.text = NSLocalizedString(@"恭喜您账号注册成功啦!", nil);
    self.messageLab.font = [UIFont boldSystemFontOfSize:20];
    self.messageLab.numberOfLines = 1;
    self.messageLab.adjustsFontSizeToFitWidth = YES;
    self.messageLab.textAlignment = NSTextAlignmentCenter;
    [self.view addSubview:self.messageLab];
    [self.messageLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(1.0f/3 * kScreenHeight));
        make.centerX.equalTo(@0);
        make.width.equalTo(self.view.mas_width).multipliedBy(0.8f);
        make.height.equalTo(@50);
    }];
    
    self.mainBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.mainBtn setTitle:NSLocalizedString(@"立即登录", nil) forState:UIControlStateNormal];
    [self.mainBtn setTintColor:[UIColor whiteColor]];
    [self.mainBtn setBackgroundColor:BKgetColorFrom(96, 178, 250, 1.0)];
    self.mainBtn.layer.cornerRadius = 25.0f;
    self.mainBtn.layer.masksToBounds = YES;
    [self.view addSubview:self.mainBtn];
    [self.mainBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(@0);
        make.centerX.equalTo(@0);
        make.width.equalTo(self.view.mas_width).multipliedBy(0.8f);
        make.height.equalTo(@50);
    }];
    [self.mainBtn addTarget:self action:@selector(mainBtnAction) forControlEvents:UIControlEventTouchUpInside];
}



/// [立即登录]按钮的点击事件
- (void)mainBtnAction{
    [self.navigationController popToRootViewControllerAnimated:YES];
}

@end
