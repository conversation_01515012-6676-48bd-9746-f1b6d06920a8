//
//  BSPasswordGuardVC.m
//  PhoneClone
//
//  Created by macbookair on 20/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSPasswordGuardVC.h"
#import "BSRegisterProgressView.h"
#import "BSPwdGuardTextField.h"
#import "BSAWSDynamoDBMgr.h"
#import "BSRegisterFinishVC.h"
@interface BSPasswordGuardVC ()
/// 输入框信息的集合数组
@property (nonatomic, strong) NSArray *messageOfTextField;
#define Question_Of_Pwd_Guard @"question" //密保输入框的问题的key
#define Placeholder_Of_Pwd_Guard @"placeholder"  //密保输入框的提示语的key
#define Alert_To_Empty_Of_Pwd_Guard @"alert_to_empty"  //输入框为空时的弹出框提示语
/// 密保第一个问题的输入框控件(密保1:您最喜欢的人?)
@property (nonatomic, strong) BSPwdGuardTextField *pwdGuardTF01;
/// 密保第二个问题的输入框控件(密保2:您的学校?)
@property (nonatomic, strong) BSPwdGuardTextField *pwdGuardTF02;
/// 密保第三个问题的输入框控件(密保3:您最喜欢的电影?)
@property (nonatomic, strong) BSPwdGuardTextField *pwdGuardTF03;
@end
@implementation BSPasswordGuardVC
- (instancetype)init{
    self = [super init];
    if (self) {
        NSArray *progressTextArray = @[NSLocalizedString(@"账号信息", nil),
                                       NSLocalizedString(@"密保设置", nil),
                                       NSLocalizedString(@"完成", nil),];
        [self loadCustomWithTitle:NSLocalizedString(@"注册", nil)
                progressTextArray:progressTextArray
                  currentProgress:2
                        isNextBtn:YES];
        [self loadUserInterface];
    }
    return self;
}
/// 加载控制器的核心试图
-(void)loadUserInterface{
    [self loadTextFieldsView];
    [self.nextBtn addTarget:self action:@selector(nextBtnAction) forControlEvents:UIControlEventTouchUpInside];
}

/// 加载输入框集合的试图
- (void)loadTextFieldsView{
    self.pwdGuardTF01 = [[BSPwdGuardTextField alloc] init];
    self.pwdGuardTF02 = [[BSPwdGuardTextField alloc] init];
    self.pwdGuardTF03 = [[BSPwdGuardTextField alloc] init];
    self.bsPwdGuardTFArr = @[
        self.pwdGuardTF01,
        self.pwdGuardTF02,
        self.pwdGuardTF03,
    ];
    
    //遍历加载输入框控件
    for (int i=0; i<self.messageOfTextField.count; i++) {
        NSDictionary *dic = self.messageOfTextField[i];
        BSPwdGuardTextField *pwdGuardTextField = self.bsPwdGuardTFArr[i];
        [pwdGuardTextField loadCustomWithTopText:dic[Placeholder_Of_Pwd_Guard] placeholder:dic[Placeholder_Of_Pwd_Guard]];
        [self.view addSubview:pwdGuardTextField];
        [pwdGuardTextField mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(@0);
            make.width.equalTo(self.view.mas_width).multipliedBy(0.8);
            make.top.equalTo(self.mainProgressView.mas_bottom).offset(i*80);
            make.height.equalTo(@80);
        }];
    }
}

/// [下一步]按钮的点击事件
- (void)nextBtnAction{
//    [self.pwdGuardTF01 setInputText:@"mother"];
//    [self.pwdGuardTF02 setInputText:@"school"];
//    [self.pwdGuardTF03 setInputText:@"movie"];
    
    //如果存在输入内容不合法,结束[下一步]操作
    if (![self checkTextFieldAction]) {
        return;
    }
    
    [self doRegisterAction];
}

/// 进行[注册账号]操作
- (void)doRegisterAction{
    self.accountModel.createTime = BKStrWithFloat([[NSDate date] timeIntervalSince1970]);
    self.accountModel.path = [BSPasswordGuardVC createPathStrWithEmail:self.accountModel.email];
    self.accountModel.q1_ans = [self.pwdGuardTF01 getInputText];
    self.accountModel.q2_ans = [self.pwdGuardTF02 getInputText];
    self.accountModel.q3_ans = [self.pwdGuardTF03 getInputText];
    [BSAWSDynamoDBMgr putItemWithAccountModel:self.accountModel completionHandler:^(AWSDynamoDBPutItemOutput * _Nonnull response, NSError * _Nonnull error) {
        if (error) {
            BKdislog(error);
            return;
        }
        
        //回到主线程,更新UI
        dispatch_async(dispatch_get_main_queue(), ^{
            BSRegisterFinishVC *registerFinishViewCtl = [[BSRegisterFinishVC alloc] init];
            [self.navigationController pushViewController:registerFinishViewCtl animated:YES];
            BKdislog(@"注册成功!");
        });
    }];
}

#pragma mark ================== 工具类型的方法 start ==================
/// 检验所有输入框输入内容是否符合规范
/// @return YES:所有输入内容全部合法  NO:存在输入内容不合法
- (BOOL)checkTextFieldAction{
    //输入框的空判断
    for (int i=0; i < self.bsPwdGuardTFArr.count; i++) {
        BSPwdGuardTextField *pwdGuardTF = self.bsPwdGuardTFArr[i];
        if ([pwdGuardTF getInputText].length == 0) {
            NSDictionary *messageOfPwdGuardDic = self.messageOfTextField[i];
            [BSAlertTool loadAlertOfMessage:messageOfPwdGuardDic[Alert_To_Empty_Of_Pwd_Guard]
                                     okText:NSLocalizedString(@"确认", nil)
                          currentController:self
                                    handler:^(UIAlertAction * _Nonnull action) {
                [pwdGuardTF becomeFirstResponderAction];
            }];
            return NO;
        }
    }
    
    return YES;
}

+ (NSString*)createPathStrWithEmail:(NSString*)email{
    NSString *UUID = [[NSUUID UUID] UUIDString];
    NSArray *uuidArr = [UUID componentsSeparatedByString:@"-"];
    
    NSMutableString *mString = [NSMutableString stringWithString:email];
    NSRange range=[mString rangeOfString:@"@"];
    [mString deleteCharactersInRange:range];
    range=[mString rangeOfString:@"."];
    [mString deleteCharactersInRange:range];
    
    NSString *guid = [NSString stringWithFormat:@"%@_%@", mString, [uuidArr lastObject]];
    return guid;
}
#pragma mark ================== 工具类型的方法 end ==================
#pragma mark ================== Get和Set方法 start ==================
-(NSArray *)messageOfTextField{
    if(!_messageOfTextField){
        _messageOfTextField = @[
            @{Question_Of_Pwd_Guard : NSLocalizedString(@"密保1:您最喜欢的人?", nil),
              Placeholder_Of_Pwd_Guard : NSLocalizedString(@"请输入您最喜欢的人", nil),
              Alert_To_Empty_Of_Pwd_Guard : NSLocalizedString(@"密保问题1不能为空!", nil),
            },
            @{Question_Of_Pwd_Guard : NSLocalizedString(@"密保2:您的学校?", nil),
              Placeholder_Of_Pwd_Guard : NSLocalizedString(@"请输入您的学校", nil),
              Alert_To_Empty_Of_Pwd_Guard : NSLocalizedString(@"密保问题2不能为空!", nil),
            },
            @{Question_Of_Pwd_Guard : NSLocalizedString(@"密保3:您最喜欢的电影?", nil),
              Placeholder_Of_Pwd_Guard : NSLocalizedString(@"请输入您最喜欢的电影", nil),
              Alert_To_Empty_Of_Pwd_Guard : NSLocalizedString(@"密保问题3不能为空!", nil),
            },
        ];
    }
    return _messageOfTextField;
}
#pragma mark ================== Get和Set方法 end ==================
@end
