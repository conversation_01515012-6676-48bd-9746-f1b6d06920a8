//
//  BSRegisterProgressView.m
//  PhoneClone
//
//  Created by macbookair on 19/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSRegisterProgressView.h"
@interface BSRegisterProgressView ()
/// 核心进度条试图
@property (nonatomic, strong) UIView *mainProgressView;
/// 进度试图的数组(保存每个进度的试图节点)
@property (nonatomic, strong) NSMutableArray <NSDictionary*> *progressViewMarr;

@end
@implementation BSRegisterProgressView
/// 控件的总长度
#define BKWidth 0.75*kScreenWidth

/// 阶段节点的半径
CGFloat const RadiusOfPoint = 7.5f;

/// 横线的高度
CGFloat const HeightOfLine = 2.0f;

/// 默认颜色
#define Color_Of_Normal BKgetColorFrom(231, 231, 231, 1.0)

/// 进度颜色
#define Color_Of_Progress BKgetColorFrom(96, 178, 250, 1.0)

/// 节点试图的key
#define Point_Key @"point"

/// 横线试图的key
#define Line_Key @"line"
/// 加载注册进度条
/// @param textArray 底本文本提示的集合数组
/// @param currentProgress 当前进度值(从1开始,到总进度值结束)
/// @param superview 被加载到的父试图
/// @param height 整个试图的高度
- (void)loadCustomWithProgressTextArray:(NSArray*)textArray currentProgress:(NSInteger)currentProgress superview:(UIView*)superview setHeight:(CGFloat)height{
    NSInteger sumProgress = textArray.count;
    //核心进度条试图的高度
    CGFloat heightOfProgressView = RadiusOfPoint * 2;
    CGFloat heightOfMainView = height;
    [superview addSubview:self];
    CGFloat topInset = [UIApplication sharedApplication].keyWindow.safeAreaInsets.top;
    
    [self mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(topInset);
        make.left.equalTo(@0);
        make.right.equalTo(@0);
        make.height.equalTo(@(heightOfMainView));
    }];
    
    
    CGFloat widthOfProgressView = 0.8*BKWidth;
    //文本控件的高度
    CGFloat heightOfLabel = 30.0f;
    //文本控件的宽度
    CGFloat widthOfLabel = 60.0f;
    //节点间的间隔距离
    CGFloat spaceOfPoint = (1.0f*widthOfProgressView-2*RadiusOfPoint)/(sumProgress-1);
    //横线间的间隔距离
    CGFloat spaceOfLine = (1.0f*widthOfProgressView-2*RadiusOfPoint)/(sumProgress-1);
    self.mainProgressView = [[UIView alloc] init];
    [self addSubview:self.mainProgressView];
    // 加载核心进度条试图
    [self.mainProgressView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(@0);
        make.centerX.equalTo(@0);
        make.width.equalTo(@(widthOfProgressView));
        make.height.equalTo(@(heightOfProgressView));
    }];
    
    self.progressViewMarr = [NSMutableArray array];
    // 加载开始位置的节点试图
    UIView *startPoint = [self createPoint];
    startPoint.backgroundColor = Color_Of_Progress;
    [startPoint mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@(RadiusOfPoint*2));
        make.height.equalTo(@(RadiusOfPoint*2));
        make.top.equalTo(@0);
        make.left.equalTo(@0);
    }];
    NSDictionary *pointLineDic = @{Point_Key : startPoint};
    [self.progressViewMarr addObject:pointLineDic];
    // 加载开始位置的底部提示文本控件
    UILabel *startLabel = [self createLabelWithText:[textArray firstObject]];
    [startLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(startPoint.mas_centerX);
        make.top.equalTo(startPoint.mas_bottom);
        make.width.equalTo(@(widthOfLabel));
        make.height.equalTo(@(heightOfLabel));
    }];
    
    // 根据总进度值,加载后续节点试图和行线试图
    for (int i = 0; i< sumProgress-1; i++) {
        // 加载进度条的节点试图
        UIView *pointView = [self createPoint];
        [pointView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(@(RadiusOfPoint*2));
            make.height.equalTo(@(RadiusOfPoint*2));
            make.top.equalTo(@0);
            make.left.equalTo(@0).offset((i+1)*spaceOfPoint);
        }];
        // 加载进度条的横线试图
        UIView *lineView = [self createLine];
        [lineView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(@(HeightOfLine));
            make.width.equalTo(@(spaceOfLine));
            make.right.equalTo(pointView.mas_right);
            make.centerY.equalTo(pointView.mas_centerY);
        }];
        NSDictionary *pointLineDic = @{
            Point_Key : pointView,
            Line_Key : lineView
        };
        [self.progressViewMarr addObject:pointLineDic];
        
        UILabel *textLabel = [self createLabelWithText:textArray[i+1]];
        [textLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(pointView.mas_bottom);
            make.centerX.equalTo(pointView.mas_centerX);
            make.width.equalTo(@(widthOfLabel));
            make.height.equalTo(@(heightOfLabel));
        }];
    }
    // 设置当前进度值
    [self setMainProgress:currentProgress];
}

/// 设置当前进度值
/// @param progress 进度值(从1开始,到总进度值结束)
- (void)setMainProgress:(NSInteger)progress{
    NSInteger count = self.progressViewMarr.count;
    // 数组越界容错处理
    if ((progress>count) || (progress<0)) {
        BKdisplayError(@"当前进度值已经超过总进度值或者小于0!!");
        return;
    }
    // 先将以前的进度值恢复到最初状态
    for (NSDictionary *dic in self.progressViewMarr) {
        if ([self getPointViewByDictionary:dic]) {
            [self getPointViewByDictionary:dic].backgroundColor = Color_Of_Normal;
        }
        if ([self getLineViewByDictionary:dic]) {
            [self getLineViewByDictionary:dic].backgroundColor = Color_Of_Normal;
        }
    }
    // 设置当前进度值
    for (int i=0; i<progress; i++) {
        if ([self getPointViewByDictionary:self.progressViewMarr[i]]) {
            [self getPointViewByDictionary:self.progressViewMarr[i]].backgroundColor = Color_Of_Progress;
        }
        if ([self getLineViewByDictionary:self.progressViewMarr[i]]) {
            [self getLineViewByDictionary:self.progressViewMarr[i]].backgroundColor = Color_Of_Progress;
        }
    }
}
#pragma mark ================== 内部方法 start ==================
/// 创建进度条的节点试图
- (UIView*)createPoint{
    UIView *pointView = [[UIView alloc] init];
    pointView.backgroundColor = Color_Of_Normal;
    pointView.layer.cornerRadius = RadiusOfPoint;
    pointView.layer.masksToBounds = YES;
    [self.mainProgressView addSubview:pointView];
    return pointView;
}

/// 创建进度条的行线试图
- (UIView*)createLine{
    UIView *lineView = [[UIView alloc] init];
    lineView.backgroundColor = Color_Of_Normal;
    [self.mainProgressView addSubview:lineView];
    return lineView;
}

/// 创建底部提示的文本控件
/// @param text 文本内容
- (UILabel*)createLabelWithText:(NSString*)text{
    UILabel *textLabel = [[UILabel alloc] init];
    textLabel.text = text;
    textLabel.textColor = [UIColor lightGrayColor];
    textLabel.textAlignment = NSTextAlignmentCenter;
    textLabel.numberOfLines = 1;
    textLabel.adjustsFontSizeToFitWidth = YES;
    [self.mainProgressView addSubview:textLabel];
    return textLabel;
}

/// 从进度条字典集合中获取[节点]试图
- (UIView*)getPointViewByDictionary:(NSDictionary*)dictionary{
    return (UIView*)dictionary[Point_Key];
}

/// 从进度条字典集合中获取[横线]试图
- (UIView*)getLineViewByDictionary:(NSDictionary*)dictionary{
    return (UIView*)dictionary[Line_Key];
}
#pragma mark ================== 内部方法 end ==================
@end
