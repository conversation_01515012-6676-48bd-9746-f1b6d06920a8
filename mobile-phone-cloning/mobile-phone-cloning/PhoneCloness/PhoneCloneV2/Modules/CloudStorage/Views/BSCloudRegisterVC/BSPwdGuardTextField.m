//
//  BSPwdGuardTextField.m
//  PhoneClone
//
//  Created by macbookair on 20/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSPwdGuardTextField.h"
@interface BSPwdGuardTextField ()
/// 密保输入框的顶部提问的文本控件
@property (nonatomic, strong) UILabel *topLabel;
/// 核心输入框控件
@property (nonatomic, strong) UITextField *mainTextField;
/// 底部下划线
@property (nonatomic, strong) UIView *bottomLine;


@end
@implementation BSPwdGuardTextField
/// 加载密保的自定义输入框控件
/// @param topText 顶部提问的文本内容
/// @param placeholder 输入框的提示语
- (void)loadCustomWithTopText:(NSString*)topText placeholder:(NSString*)placeholder{
    self.topLabel = [[UILabel alloc] init];
    self.topLabel.text = topText;
    [self addSubview:self.topLabel];
    [self.topLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@0);
        make.left.equalTo(@0);
        make.right.equalTo(@0);
        make.height.equalTo(self.mas_height).multipliedBy(1.0f/2).offset(-1);
    }];
    
    self.mainTextField = [[UITextField alloc] init];
    self.mainTextField.placeholder = placeholder;
    [self addSubview:self.mainTextField];
    [self.mainTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.topLabel.mas_bottom);
        make.left.equalTo(@0);
        make.right.equalTo(@0);
        make.height.equalTo(self.mas_height).multipliedBy(1.0f/2).offset(-1);
    }];
    
    self.bottomLine = [[UIView alloc] init];
    self.bottomLine.backgroundColor = BKgetColorFrom(236, 236, 236, 1.0f);
    [self addSubview:self.bottomLine];
    [self.bottomLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.mas_bottom);
        make.left.equalTo(@0);
        make.right.equalTo(@0);
        make.height.equalTo(@1);
    }];
}

/// [获取]输入框输入的内容
/// @return 输入框输入的内容
- (NSString*)getInputText{
    return self.mainTextField.text;
}

/// [设置]输入框输入的内容
/// @param text 输入的内容
- (void)setInputText:(NSString*)text{
    self.mainTextField.text = text;
}

/// 清空输入框输入内容
- (void)clearInputText{
    self.mainTextField.text = 0;
}

/// [取消]输入框的第一响应
- (void)resignFirstResponderAction{
    [self.mainTextField resignFirstResponder];
}

/// [启动]输入框的第一响应
- (void)becomeFirstResponderAction{
    [self.mainTextField becomeFirstResponder];
}

@end
