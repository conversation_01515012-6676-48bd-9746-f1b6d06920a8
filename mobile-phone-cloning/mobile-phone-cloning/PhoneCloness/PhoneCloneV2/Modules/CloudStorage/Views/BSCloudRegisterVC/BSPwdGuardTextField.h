//
//  BSPwdGuardTextField.h
//  PhoneClone
//
//  Created by macbookair on 20/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
/// 密保的自定义输入框
@interface BSPwdGuardTextField : UIView
/// 加载密保的自定义输入框控件
/// @param topText 顶部提问的文本内容
/// @param placeholder 输入框的提示语
- (void)loadCustomWithTopText:(NSString*)topText placeholder:(NSString*)placeholder;


/// [获取]输入框输入的内容
/// @return 输入框输入的内容
- (NSString*)getInputText;

/// [设置]输入框输入的内容
/// @param text 输入的内容
- (void)setInputText:(NSString*)text;

/// 清空输入框输入内容
- (void)clearInputText;

/// [取消]输入框的第一响应
- (void)resignFirstResponderAction;

/// [启动]输入框的第一响应
- (void)becomeFirstResponderAction;

@end

NS_ASSUME_NONNULL_END
