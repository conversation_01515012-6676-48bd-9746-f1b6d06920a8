//
//  BSRegisterProgressView.h
//  PhoneClone
//
//  Created by macbookair on 19/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
/// 注册界面的进度条
@interface BSRegisterProgressView : UIView
/// 加载注册进度条
/// @param textArray 底本文本提示的集合数组
/// @param currentProgress 当前进度值(从1开始,到总进度值结束)
/// @param superview 被加载到的父试图
/// @param height 整个试图的高度
- (void)loadCustomWithProgressTextArray:(NSArray*)textArray currentProgress:(NSInteger)currentProgress superview:(UIView*)superview setHeight:(CGFloat)height;

/// 设置当前进度值
/// @param progress 进度值(从1开始,到总进度值结束)
- (void)setMainProgress:(NSInteger)progress;

@end

NS_ASSUME_NONNULL_END
