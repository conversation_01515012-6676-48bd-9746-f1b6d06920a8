//
//  BSTextField.m
//  PhoneClone
//
//  Created by macbookair on 19/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSTextField.h"
@interface BSTextField ()
/// 左端图标
@property (nonatomic, strong) UIImageView *leftIcon;

/// 底部输入线
@property (nonatomic, strong) UIView *bottonLine;
@end
@implementation BSTextField
/// 加载 自定义输入框 控件
/// @param iconName 左端图标的图像名称
/// @param placeholder 核心输入框的提示文字
- (void)loadCutomWithIconName:(NSString*)iconName placeholder:(NSString*)placeholder{
    self.leftIcon = [[UIImageView alloc] initWithImage:[UIImage loadOriginalImage:iconName]];
    [self addSubview:self.leftIcon];
    [self.leftIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@0);
        make.centerY.equalTo(@0);
        make.width.equalTo(@25);
        make.height.equalTo(@25);
    }];
    
    self.mainTextField = [[UITextField alloc] init];
    self.mainTextField.placeholder = placeholder;
    [self addSubview:self.mainTextField];
    [self.mainTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@0);
        make.left.equalTo(self.leftIcon.mas_right).offset(5);
        make.bottom.equalTo(@0);
        make.right.equalTo(@0);
    }];
    
    self.bottonLine = [[UIView alloc] init];
    self.bottonLine.backgroundColor = BKgetColorFrom(226, 226, 226, 1.0);
    [self addSubview:self.bottonLine];
    [self.bottonLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.mainTextField.mas_left);
        make.right.equalTo(self.mainTextField.mas_right);
        make.bottom.equalTo(self.mas_bottom);
        make.height.equalTo(@1);
    }];
}

/// [获取]输入框输入的内容
/// @return 输入框输入的内容
- (NSString*)getInputText{
    return self.mainTextField.text;
}

/// [设置]输入框输入的内容
/// @param text 输入的内容
- (void)setInputText:(NSString*)text{
    self.mainTextField.text = text;
}

/// 清空输入框输入内容
- (void)clearInputText{
    self.mainTextField.text = 0;
}

/// [取消]输入框的第一响应
- (void)resignFirstResponderAction{
    [self.mainTextField resignFirstResponder];
}

/// [启动]输入框的第一响应
- (void)becomeFirstResponderAction{
    [self.mainTextField becomeFirstResponder];
}

@end
