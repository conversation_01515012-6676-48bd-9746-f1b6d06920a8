//
//  BSCloudLoginHeadView.m
//  PhoneClone
//
//  Created by macbookair on 16/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSCloudLoginHeadView.h"
#import "BSLogoTitleView.h"
@interface BSCloudLoginHeadView()
/// logo图标与标题的组合控件
@property (nonatomic, strong) BSLogoTitleView *logoTitleView;

/// 副标题的文本控件
@property (nonatomic, strong) UILabel *detailsLab;

@end
@implementation BSCloudLoginHeadView
- (instancetype)init{
    self = [super init];
    if (self) {
        [self initUI];
    }
    return self;
}

- (void)initUI{
    [self setImage:[UIImage imageNamed:@"trans_header_bg"]];
    self.userInteractionEnabled = YES;
    
    self.vipBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.vipBtn setImage:[UIImage loadOriginalImage:@"huiyuan"] forState:UIControlStateNormal];
    [self.vipBtn.imageView setContentMode:UIViewContentModeCenter];
    [self addSubview:self.vipBtn];
    [self.vipBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(kStatusBarHeight));
        make.right.equalTo(@(-5));
        make.width.equalTo(@40);
        make.height.equalTo(@40);
    }];
    
    self.logoTitleView = [BSLogoTitleView loadCustomWithImgName:@"yunduan" title:NSLocalizedString(@"云存储", nil)];
    [self addSubview:self.logoTitleView];
    [self.logoTitleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mas_bottom).multipliedBy(1.0f/2.5);
        make.centerX.equalTo(@0);
        make.width.equalTo(@180);
        make.height.equalTo(@40);
    }];

    self.detailsLab = [[UILabel alloc] init];
    self.detailsLab.text = NSLocalizedString(@"上传图片到云存储", nil);
    self.detailsLab.textColor = [UIColor whiteColor];
    self.detailsLab.numberOfLines = 1;
    self.detailsLab.adjustsFontSizeToFitWidth = YES;
    self.detailsLab.textAlignment = NSTextAlignmentCenter;
    [self addSubview:self.detailsLab];
    [self.detailsLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.logoTitleView.mas_bottom);
        make.left.equalTo(self.logoTitleView.mas_left);
        make.right.equalTo(self.logoTitleView.mas_right);
        make.height.equalTo(@40);
    }];

}

@end
