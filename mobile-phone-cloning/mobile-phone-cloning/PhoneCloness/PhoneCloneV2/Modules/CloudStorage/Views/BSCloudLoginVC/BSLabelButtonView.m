//
//  BSLabelButtonView.m
//  PhoneClone
//
//  Created by macbookair on 16/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSLabelButtonView.h"
@interface BSLabelButtonView()
/// 左边的文本控件
@property (nonatomic, strong) UILabel *leftLab;

@end
@implementation BSLabelButtonView
/// 加载 文本与按钮 组合控件
/// @param labelText 文本文字内容
/// @param buttonText 按钮文字内容
- (void)loadCustomWithLabelText:(NSString*)labelText buttonText:(NSString*)buttonText{
    self.leftLab = [[UILabel alloc] init];
    self.leftLab.text = labelText;
    self.leftLab.textAlignment = NSTextAlignmentRight;
    self.leftLab.textColor = [UIColor lightGrayColor];
    self.leftLab.numberOfLines = 1;
    self.leftLab.adjustsFontSizeToFitWidth = YES;
    [self addSubview:self.leftLab];
    [self.leftLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@0);
        make.bottom.equalTo(@0);
        make.right.equalTo(self.mas_right).multipliedBy(0.5);
        make.width.equalTo(self.mas_width).multipliedBy(0.5);
    }];
    
    self.rightBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.rightBtn setTitle:buttonText forState:UIControlStateNormal];
    [self.rightBtn.titleLabel setTextAlignment:NSTextAlignmentLeft];
    [self addSubview:self.rightBtn];
    [self.rightBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@0);
        make.bottom.equalTo(@0);
        make.left.equalTo(self.mas_right).multipliedBy(0.5);
        make.width.equalTo(self.mas_width).multipliedBy(0.5);
    }];
}
@end
