//
//  BSLogoTitleView.m
//  PhoneClone
//
//  Created by macbookair on 16/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSLogoTitleView.h"

@implementation BSLogoTitleView
/// 加载 logo图标和标题组合 的自定义试图
/// @param imgName logo图标名称
/// @param title 标题文本内容
+ (instancetype)loadCustomWithImgName:(NSString*)imgName title:(NSString*)title{
    BSLogoTitleView *mainView = [[BSLogoTitleView alloc] init];
    UIImageView *logoImgView = [[UIImageView alloc] initWithImage:[UIImage loadOriginalImage:imgName]];
    logoImgView.contentMode = UIViewContentModeScaleAspectFit;
    [mainView addSubview:logoImgView];
    [logoImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@0);
        make.bottom.equalTo(@0);
        make.centerX.equalTo(@0).multipliedBy(1.0f/3);
        make.width.equalTo(mainView.mas_width).multipliedBy(1.0f/3);
    }];
    
    UILabel *titleLab = [[UILabel alloc] init];
    titleLab.font = [UIFont boldSystemFontOfSize:30];
    titleLab.text = title;
    titleLab.numberOfLines = 1;
    titleLab.adjustsFontSizeToFitWidth = YES;

    titleLab.textColor = [UIColor whiteColor];
    titleLab.textAlignment = NSTextAlignmentCenter;
    [mainView addSubview:titleLab];
    [titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(@0);
        make.left.equalTo(logoImgView.mas_right).offset(2.5);
        make.height.equalTo(mainView.mas_height);
        make.right.equalTo(mainView.mas_right);
    }];
    
    return mainView;
}

@end
