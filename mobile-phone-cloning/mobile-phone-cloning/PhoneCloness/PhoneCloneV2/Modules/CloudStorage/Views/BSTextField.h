//
//  BSTextField.h
//  PhoneClone
//
//  Created by macbookair on 19/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
/// 登录注册界面的自定义输入框
@interface BSTextField : UIView
/// 核心输入框控件
@property (nonatomic, strong) UITextField *mainTextField;

/// 加载 自定义输入框 控件
/// @param iconName 左端图标的图像名称
/// @param placeholder 核心输入框的提示文字
- (void)loadCutomWithIconName:(NSString*)iconName placeholder:(NSString*)placeholder;
/// [获取]输入框输入的内容
/// @return 输入框输入的内容
- (NSString*)getInputText;

/// [设置]输入框输入的内容
/// @param text 输入的内容
- (void)setInputText:(NSString*)text;

/// 清空输入框输入内容
- (void)clearInputText;

/// [取消]输入框的第一响应
- (void)resignFirstResponderAction;

/// [启动]输入框的第一响应
- (void)becomeFirstResponderAction;

@end

NS_ASSUME_NONNULL_END
