//
//  BSCloudStorageHeadView.h
//  PhoneClone
//
//  Created by macbookair on 9/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
/// 云存储头部试图的协议
@protocol BSCloudStorageHeadViewProtocol <NSObject>
/// 退出用户登录的协议方法
- (void)exitUserLoginAction;
/// 注销用户登录的协议方法
- (void)deletUserLoginAction;
@end

/// 云存储的头部试图
@interface BSCloudStorageHeadView : UIView
/// 充值VIP按钮
@property (nonatomic, strong) UIButton *vipBtn;

/// 设置用户名的文本
/// @param username 用户名
- (void)setUsername:(NSString*)username;

/// 设置本地文件数量的文本
/// @param filesNum 文件数量
- (void)setlocalFilesNum:(NSInteger)filesNum;

/// 设置云端文件数量的文本
/// @param filesNum 文件数量
- (void)setcloudFilesNum:(NSInteger)filesNum;

/// 协议代理
@property(nonatomic, assign)id<BSCloudStorageHeadViewProtocol>delegate;

@end

NS_ASSUME_NONNULL_END
