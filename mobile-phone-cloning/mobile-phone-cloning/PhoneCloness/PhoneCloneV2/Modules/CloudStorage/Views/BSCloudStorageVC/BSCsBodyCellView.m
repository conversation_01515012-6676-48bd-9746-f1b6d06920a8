//
//  BSCsBodyCellView.m
//  PhoneClone
//
//  Created by macbookair on 11/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSCsBodyCellView.h"
@interface BSCsBodyCellView()
/// 控件的图标
@property (nonatomic, strong) UIButton *iconBtn;

/// 控件的标题文本
@property (nonatomic, strong) UILabel *titleLab;

/// 控件的核心按钮
@property (nonatomic, strong) UIButton *mainBtn;

@end
@implementation BSCsBodyCellView
/// 初始化
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.iconBtn = [UIButton buttonWithType:UIButtonTypeSystem];
        self.titleLab = [[UILabel alloc] init];
        self.mainBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    }
    return self;
}

/// 加载主体单元试图
/// @param icon 图标(图标图像名称)
/// @param title 主体标题文本
/// @param btnTitle 按钮标题
/// @param tag 按钮标记码
- (void)loadBodyCellViewWithIcon:(NSString*)icon title:(NSString*)title btnTitle:(NSString*)btnTitle tag:(NSInteger)tag{
    [self.iconBtn setImage:[UIImage loadOriginalImage:icon] forState:UIControlStateNormal];
    [self.iconBtn setContentMode:UIViewContentModeCenter];
    self.iconBtn.tag = tag;
    [self addSubview:self.iconBtn];
    [self.iconBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@35);
        make.centerX.equalTo(self.mas_centerX);
        make.width.equalTo(@40);
        make.height.equalTo(@33);
    }];
    
    self.titleLab.text = title;
    self.titleLab.numberOfLines = 1;
    self.titleLab.adjustsFontSizeToFitWidth = YES;
    [self addSubview:self.titleLab];
    [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.iconBtn.mas_bottom).offset(17);
        make.centerX.equalTo(self.mas_centerX);
        make.width.equalTo(@70);
        make.height.equalTo(@20);
    }];
    
    [self.mainBtn setTitle:btnTitle forState:UIControlStateNormal];
    [self.mainBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.mainBtn.titleLabel.numberOfLines = 1;
    self.mainBtn.titleLabel.adjustsFontSizeToFitWidth = YES;
    self.mainBtn.backgroundColor = BKgetColorFrom(93, 176, 250, 1.0);
    self.mainBtn.layer.cornerRadius = 15.0f;
    self.mainBtn.layer.masksToBounds = YES;
    self.mainBtn.tag = tag;
    [self addSubview:self.mainBtn];
    [self.mainBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.mas_bottom).offset(-20);
        make.centerX.equalTo(self.mas_centerX);
        make.width.equalTo(self.mas_width).offset(-40);
        make.height.equalTo(@30);
    }];
    
    [self.iconBtn addTarget:self action:@selector(btnClickAction:) forControlEvents:UIControlEventTouchUpInside];
    [self.mainBtn addTarget:self action:@selector(btnClickAction:) forControlEvents:UIControlEventTouchUpInside];
}

/// 按钮点击事件
- (void)btnClickAction:(UIButton*)sender{
    [self.bodyCellViewDelegate clickActionOfBodyCellView:sender.tag];
}


@end
