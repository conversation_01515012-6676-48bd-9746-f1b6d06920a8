//
//  BSCloudStorageHeadView.m
//  PhoneClone
//
//  Created by macbookair on 9/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSCloudStorageHeadView.h"
#import "BSCsNumLabel.h"
@interface BSCloudStorageHeadView()
/// 主体背景试图
@property (nonatomic, strong) UIImageView *mainBackground;
/// 头部icon图标
@property (nonatomic, strong) UIImageView *headIcon;
/// [退出]按钮
@property (nonatomic, strong) UIButton *exitBtn;
/// 用户名称的文本控件
@property (nonatomic, strong) UILabel *usernameLab;
/// 本地文件数量的文本控件
@property (nonatomic, strong) BSCsNumLabel *localFile;
/// 云服务文件数量的文本控件
@property (nonatomic, strong) BSCsNumLabel *cloudFile;
@end
@implementation BSCloudStorageHeadView
- (instancetype)init
{
    self = [super init];
    if (self) {
        [self initUI];
    }
    return self;
}

- (void)initUI{
    self.mainBackground = [[UIImageView alloc] initWithFrame:CGRectZero];
    [self.mainBackground setImage:[UIImage imageNamed:@"trans_header_bg"]];
    [self addSubview:self.mainBackground];
    self.mainBackground.userInteractionEnabled = YES;
    [self.mainBackground mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@0);
        make.left.equalTo(@0);
        make.right.equalTo(@0);
        make.bottom.equalTo(@0);
    }];
    
    self.vipBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.vipBtn setImage:[UIImage loadOriginalImage:@"huiyuan"] forState:UIControlStateNormal];
    [self.mainBackground addSubview:self.vipBtn];
    [self.vipBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(kStatusBarHeight));
        make.right.equalTo(@-16);
        make.width.equalTo(@24);
        make.height.equalTo(@17);
    }];
    
    self.headIcon = [[UIImageView alloc] init];
    [self.headIcon setImage:[UIImage imageNamed:@"yunduan"]];
    self.headIcon.contentMode = UIViewContentModeCenter;
    [self.mainBackground addSubview:self.headIcon];
    [self.headIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.vipBtn.mas_bottom).offset(15);
        make.left.equalTo(@35);
        make.width.equalTo(@41);
        make.height.equalTo(@32);
    }];
    
    self.exitBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.exitBtn setTitle:NSLocalizedString(@"退出", nil) forState:UIControlStateNormal];
    self.exitBtn.backgroundColor = BKgetColorFrom(206, 235, 255, 1.0);
    [self.mainBackground addSubview:self.exitBtn];
    [self.exitBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.headIcon.mas_centerY);
        make.right.equalTo(@-16);
        make.height.equalTo(@25);
        make.width.equalTo(@50);
    }];
    [self.exitBtn addTarget:self action:@selector(exitBtnAction) forControlEvents:UIControlEventTouchUpInside];
    
    UIButton* delet = [UIButton buttonWithType:UIButtonTypeSystem];
    [delet setTitle:local(@"注销") forState:UIControlStateNormal];
    delet.backgroundColor = BKgetColorFrom(206, 235, 255, 1.0);
    [self.mainBackground addSubview:delet];
    [delet mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.headIcon.mas_centerY);
        make.right.mas_equalTo(self.exitBtn.mas_left).offset(-16);
        make.height.equalTo(@25);
        make.width.greaterThanOrEqualTo(@50);
    }];
    [delet addTarget:self action:@selector(deleBtnAction) forControlEvents:UIControlEventTouchUpInside];
    
    
    self.usernameLab = [[UILabel alloc] init];
    self.usernameLab.text = @"**********.com";
    self.usernameLab.textColor = [UIColor whiteColor];
    [self.mainBackground addSubview:self.usernameLab];
    [self.usernameLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.headIcon.mas_bottom).offset(8);
        make.left.equalTo(self.headIcon.mas_left);
        make.width.equalTo(@(2.0f/3*kScreenWidth));
        make.height.equalTo(@20);
    }];
    
    self.localFile = [[BSCsNumLabel alloc] init];
    [self.mainBackground addSubview:self.localFile];
    [self.localFile mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.usernameLab.mas_bottom).offset(8);
        make.left.equalTo(self.usernameLab.mas_left);
        make.width.equalTo(@(1.0f/5 * kScreenWidth));
        make.height.equalTo(@22);
    }];
    [self.localFile loadContentWithNum:0 item:NSLocalizedString(@"本地", nil)];
    
    self.cloudFile = [[BSCsNumLabel alloc] init];
    [self.mainBackground addSubview:self.cloudFile];
    [self.cloudFile mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.localFile.mas_top);
        make.bottom.equalTo(self.localFile.mas_bottom);
        make.left.equalTo(self.localFile.mas_right).offset(20);
        make.width.equalTo(self.localFile.mas_width);
    }];
    [self.cloudFile loadContentWithNum:0 item:NSLocalizedString(@"云端", nil)];
}

/// [退出]按钮的点击事件
- (void)exitBtnAction{
    [self.delegate exitUserLoginAction];
}

- (void)deleBtnAction{
    [self.delegate deletUserLoginAction];
}

/// 设置用户名的文本
/// @param username 用户名
- (void)setUsername:(NSString*)username{
    [self.usernameLab setText:username];
}

/// 设置本地文件数量的文本
/// @param filesNum 文件数量
- (void)setlocalFilesNum:(NSInteger)filesNum{
    [self.localFile setNum:filesNum];
}

/// 设置云端文件数量的文本
/// @param filesNum 文件数量
- (void)setcloudFilesNum:(NSInteger)filesNum{
    [self.cloudFile setNum:filesNum];
}

@end
