//
//  BSCsNumLabel.m
//  PhoneClone
//
//  Created by macbookair on 9/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSCsNumLabel.h"
@interface BSCsNumLabel()
/// 数字的文本控件
@property (nonatomic, strong) UILabel *numLab;
/// 事物的文本控件
@property (nonatomic, strong) UILabel *itemLab;

@end
@implementation BSCsNumLabel
/// 初始化
- (instancetype)init
{
    self = [super init];
    if (self) {
        self.numLab = [[UILabel alloc] init];
        self.itemLab = [[UILabel alloc] init];
    }
    return self;
}

/// 加载数字和事物的文本内容
/// @param num 数字内容
/// @param item 事物内容
- (void)loadContentWithNum:(NSInteger)num item:(NSString*)item{
    self.numLab.text = BKStrWithNum(num);
    self.numLab.textColor = [UIColor whiteColor];
    self.numLab.textAlignment = NSTextAlignmentRight;
    self.numLab.numberOfLines = 1;
    self.numLab.adjustsFontSizeToFitWidth = YES;
    [self addSubview:self.numLab];
    [self.numLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mas_top);
        make.left.equalTo(self.mas_left);
        make.bottom.equalTo(self.mas_bottom);
        make.right.equalTo(self.mas_centerX).offset(-2.5f);
    }];
    
    self.itemLab.text = item;
    self.itemLab.textColor = self.numLab.textColor;
    self.itemLab.textAlignment = NSTextAlignmentRight;
    self.itemLab.numberOfLines = 1;
    self.itemLab.adjustsFontSizeToFitWidth = YES;
    [self addSubview:self.itemLab];
    [self.itemLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mas_top);
        make.right.equalTo(self.mas_right);
        make.bottom.equalTo(self.mas_bottom);
        make.left.equalTo(self.mas_centerX);
    }];
}

- (void)setNum:(NSInteger)num{
    [self.numLab setText:BKStrWithNum(num)];
}

@end
