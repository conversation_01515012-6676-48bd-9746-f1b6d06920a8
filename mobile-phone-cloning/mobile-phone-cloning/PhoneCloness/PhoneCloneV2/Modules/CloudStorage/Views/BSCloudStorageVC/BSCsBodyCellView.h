//
//  BSCsBodyCellView.h
//  PhoneClone
//
//  Created by macbookair on 11/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
@protocol BSCsBodyCellViewProtocol <NSObject>
/// 主体单元试图的点击事件
/// @param tag 按钮标识码
- (void)clickActionOfBodyCellView:(NSInteger)tag;
@end


@interface BSCsBodyCellView : UIView
/// 加载主体单元试图
/// @param icon 图标(图标名称)
/// @param title 主体标题文本
/// @param btnTitle 按钮标题
/// @param tag 按钮标记码
- (void)loadBodyCellViewWithIcon:(NSString*)icon title:(NSString*)title btnTitle:(NSString*)btnTitle tag:(NSInteger)tag;

@property(nonatomic, assign)id<BSCsBodyCellViewProtocol>bodyCellViewDelegate;

@end

NS_ASSUME_NONNULL_END
