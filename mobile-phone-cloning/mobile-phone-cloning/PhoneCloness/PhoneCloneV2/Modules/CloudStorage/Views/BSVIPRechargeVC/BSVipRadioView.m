//
//  BSVipRadioView.m
//  PhoneClone
//
//  Created by macbookair on 24/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSVipRadioView.h"
@interface BSVipRadioView ()
@property (nonatomic, strong) UILabel *topLabel;
@property (nonatomic, strong) UILabel *centerLabel;
@property (nonatomic, strong) UILabel *bottomLabel;

/// 是否选中的状态(YES:已选中  NO:未选中)
@property (nonatomic, assign) BOOL isSelectedState;

@end
@implementation BSVipRadioView
/// 初始化VIP充值单选试图
/// @param vipContent vip内容
/// @param tag 试图的标识码
+ (instancetype)loadCustomWithVipContent:(NSString*)vipContent tag:(NSInteger)tag{
    NSArray *strArr = [vipContent componentsSeparatedByString:@"-"];
    
    BSVipRadioView *mainView = [[BSVipRadioView alloc] init];
    mainView.layer.borderWidth = 2.5;
    mainView.layer.borderColor = [BKgetColorFrom(181, 181, 181, 1.0) CGColor];
    mainView.tag = tag;
    
    mainView.topLabel = [[UILabel alloc] init];
    mainView.topLabel.text = strArr[0];
    mainView.topLabel.textAlignment = NSTextAlignmentCenter;
    mainView.topLabel.font = [UIFont boldSystemFontOfSize:30];
    mainView.topLabel.numberOfLines = 1;
    mainView.topLabel.adjustsFontSizeToFitWidth = YES;
    [mainView addSubview:mainView.topLabel];
    [mainView.topLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@0);
        make.centerX.equalTo(@0);
        make.width.equalTo(mainView.mas_width).offset(-20);
        make.height.equalTo(mainView.mas_height).multipliedBy(0.5);
    }];
    
    mainView.centerLabel = [[UILabel alloc] init];
    mainView.centerLabel.text = strArr[1];
    mainView.centerLabel.textAlignment = NSTextAlignmentCenter;
    mainView.centerLabel.numberOfLines = 1;
    mainView.centerLabel.adjustsFontSizeToFitWidth = YES;
    [mainView addSubview:mainView.centerLabel];
    [mainView.centerLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(mainView.topLabel.mas_bottom);
        make.centerX.equalTo(@0);
        make.width.equalTo(mainView.mas_width).offset(-20);
        make.height.equalTo(mainView.mas_height).multipliedBy(0.25);
    }];
    
    mainView.bottomLabel = [[UILabel alloc] init];
    mainView.bottomLabel.text = strArr[2];
    mainView.bottomLabel.textColor = [UIColor redColor];
    mainView.bottomLabel.textAlignment = NSTextAlignmentCenter;
    mainView.bottomLabel.numberOfLines = 1;
    mainView.bottomLabel.adjustsFontSizeToFitWidth = YES;
    [mainView addSubview:mainView.bottomLabel];
    [mainView.bottomLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(mainView.centerLabel.mas_bottom);
        make.centerX.equalTo(@0);
        make.width.equalTo(mainView.mas_width).offset(-20);
        make.height.equalTo(mainView.mas_height).multipliedBy(0.25);
    }];
    
    return mainView;
}

/// 设置选中状态
/// @param state 选中状态(YES:已选中  NO:未选中)
- (void)setSelectRadioWithState:(BOOL)state{
    if (state) {
        self.isSelectedState = YES;
        self.layer.borderColor = [BKgetColorFrom(121, 187, 245, 1.0) CGColor];
    } else {
        self.isSelectedState = NO;
        self.layer.borderColor = [BKgetColorFrom(181, 181, 181, 1.0) CGColor];
    }
}

@end
