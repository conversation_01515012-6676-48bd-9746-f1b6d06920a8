//
//  BSVipRadioView.h
//  PhoneClone
//
//  Created by macbookair on 24/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface BSVipRadioView : UIButton

/// 初始化VIP充值单选试图
/// @param vipContent vip内容
/// @param tag 试图的标识码
+ (instancetype)loadCustomWithVipContent:(NSString*)vipContent tag:(NSInteger)tag;

/// 选中状态(YES:已选中  NO:未选中)
@property (readonly ,nonatomic, assign) BOOL isSelectedState;

/// 设置选中状态
/// @param state 选中状态(YES:已选中  NO:未选中)
- (void)setSelectRadioWithState:(BOOL)state;

@end

NS_ASSUME_NONNULL_END
