//
//  BSIconDetailsLabelView.m
//  PhoneClone
//
//  Created by macbookair on 24/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSIconDetailsLabelView.h"
@interface BSIconDetailsLabelView()
@property (nonatomic, strong) UIImageView *leftIcon;
@property (nonatomic, strong) UILabel *rightDetailsLab;


@end
@implementation BSIconDetailsLabelView
+ (instancetype)loadCucstomWithIcon:(NSString*)icon details:(NSString*)details{
    BSIconDetailsLabelView *mainView = [[BSIconDetailsLabelView alloc] init];
    mainView.leftIcon = [[UIImageView alloc] initWithImage:[UIImage imageNamed:icon]];
    mainView.leftIcon.contentMode = UIViewContentModeCenter;
    [mainView addSubview:mainView.leftIcon];
    [mainView.leftIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@0);
        make.left.equalTo(@0);
        make.bottom.equalTo(@0);
        make.width.equalTo(mainView.mas_height);
    }];
    
    mainView.rightDetailsLab = [[UILabel alloc] init];
    mainView.rightDetailsLab.text = details;
    mainView.rightDetailsLab.font = [UIFont systemFontOfSize:15];
    [mainView addSubview:mainView.rightDetailsLab];
    [mainView.rightDetailsLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@0);
        make.left.equalTo(mainView.leftIcon.mas_right);
        make.bottom.equalTo(@0);
        make.right.equalTo(@0);
    }];
    
    return mainView;
}

@end
