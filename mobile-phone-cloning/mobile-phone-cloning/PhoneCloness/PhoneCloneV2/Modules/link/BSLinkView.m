//
//  BSLinkView.m
//  PhoneCloneV2
//
//  Created by fs0011 on 2024/2/19.
//  Copyright © 2024 PhoneClone. All rights reserved.
//

#import "BSLinkView.h"
#import "BSLinkButton.h"
#import "UIView+cornerRadius.h"
@implementation BSLinkView
{
    BSLinkButton* link1;
    BSLinkButton* link2;
    BSLinkButton* link3;
}
-(instancetype)init
{
    if(self == [super init])
    {
        [self mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(kDeviceWidth-18*2);
            make.height.mas_equalTo(50*3+2);

        }];
        self.backgroundColor = [UIColor whiteColor];
        self.layer.cornerRadius = 8;
        self.layer.masksToBounds = YES;
        [self layoutIfNeeded];
        [self layout];
    }
    return self;
}

- (void)layout
{   NSMutableArray<BSLinkButton*>* arr = [NSMutableArray array];
    BSLinkButton* btn1 = [[BSLinkButton alloc]initWithName:@"扫一扫"];
    [self addSubview:btn1];
    [btn1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.mas_equalTo(1);
        make.right.mas_equalTo(-1);
        make.height.mas_equalTo(50);
    }];
    [btn1.superview layoutIfNeeded];
    [btn1 addRoundedCorners:UIRectCornerTopLeft | UIRectCornerTopRight withRadii:CGSizeMake(8, 8)];
    [arr addObject:btn1];
    [btn1 setChoose:YES];
    link1 = btn1;
    
    BSLinkButton* btn2 = [[BSLinkButton alloc]initWithName:@"我的二维码"];
    [self addSubview:btn2];
    [btn2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(1);
        make.right.mas_equalTo(-1);
        make.height.mas_equalTo(51);
        make.top.mas_equalTo(50);
    }];
    [btn2.superview layoutIfNeeded];
    [arr addObject:btn2];
    [btn2 setChoose:NO];
    link2 = btn2;
    
    BSLinkButton* btn3 = [[BSLinkButton alloc]initWithName:@"ip连接"];
    [self addSubview:btn3];
    [btn3 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(1);
        make.right.mas_equalTo(-1);
        make.height.mas_equalTo(50);
        make.bottom.mas_equalTo(-1);
    }];
    [btn3.superview layoutIfNeeded];
    [btn3 addRoundedCorners:UIRectCornerBottomLeft | UIRectCornerBottomRight withRadii:CGSizeMake(8, 8)];
    [arr addObject:btn3];
    [btn3 setChoose:NO];
    link3 = btn3;
    
    [arr bk_each:^(BSLinkButton * _Nonnull obj) {
        [obj bk_whenTapped:^{
            [arr bk_each:^(BSLinkButton * _Nonnull eachobj) {
                [eachobj setChoose:NO];
            }];
            [obj setChoose:YES];
            self.chooseCallback(@([arr indexOfObject:obj]));
        }];
    }];
    
    UIView* line1 = [UIView new];
    line1.backgroundColor = [UIColor whiteColor];
    [self addSubview:line1];
    [line1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(btn1.mas_bottom);
        make.height.mas_equalTo(0.5);
        make.left.mas_equalTo(48);
        make.right.mas_equalTo(-28);
    }];
    
    UIView* line2 = [UIView new];
    line2.backgroundColor = [UIColor whiteColor];
    [self addSubview:line2];
    [line2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(btn2.mas_bottom);
        make.height.mas_equalTo(0.5);
        make.left.mas_equalTo(48);
        make.right.mas_equalTo(-28);
    }];
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/
- (void)setchooseIndex:(NSInteger)index
{
    [link1 setChoose:NO];
    [link2 setChoose:NO];
    [link3 setChoose:NO];
    switch (index) {
        case 0:
            [link1 setChoose:YES];
            break;
        case 1:
            [link2 setChoose:YES];
            break;
        case 2:
            [link3 setChoose:YES];
            break;
            
        default:
            break;
    }
}
@end
