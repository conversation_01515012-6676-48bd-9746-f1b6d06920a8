//
//  startLinkViewController.m
//  mobiletransfer
//
//  Created by fs0011 on 2023/12/4.
//  Copyright © 2023 <PERSON><PERSON><PERSON>. All rights reserved.
//

#import "startLinkViewController.h"
#import "MySegmentedControl.h"
#import "BSTransferPeersVC.h"
#import <AVFoundation/AVFoundation.h>
#import "HTTPServer.h"
#import "MyHTTPConnection.h"
#import "tokenTool.h"
#import "BSLinkView.h"
@interface startLinkViewController ()<AVCaptureMetadataOutputObjectsDelegate>
@property UIImageView* mainImageView;
@property UIView* natMainSendView;
@property UIView* IPlinkView;

@property MySegmentedControl* mysegment;

@property UIView* ipView;
@property UIView* scanView;
@property UIView* mainView;
@property (strong, nonatomic) AVCaptureSession *captureSession;
@property UILabel* tipLabel;

@property UIView* qrcard;
@property UIView* qrcapview;
@property UIImageView* qrimageView;
@property BSLinkView* linkView;
@end

@implementation startLinkViewController
{
    HTTPServer *httpServer;
}
- (void)viewDidLoad {
    [super viewDidLoad];
//    self.title = local(@"连接");
    UIImage *image = [UIImage imageNamed:@"home_bg"];
    UIGraphicsBeginImageContextWithOptions(self.view.frame.size, NO, 0.f);
    [image drawInRect:self.view.bounds];
        UIImage *lastImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    self.view.backgroundColor = [UIColor colorWithPatternImage:lastImage];

    self.mainImageView = [[UIImageView alloc]initWithFrame:CGRectMake(0,0,kDeviceWidth,kDeviceHeight)];
    self.mainImageView.image = [UIImage imageNamed:@"coverBaseImage_icn"];
    self.mainImageView.userInteractionEnabled = YES;
    [self.view addSubview:self.mainImageView];
//    [self loadUI];
    [self requestCameraPermission];
    
    
    [self openserver];
    
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(receiveHttpJoin:) name:receiveJoin object:nil];
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(receiveJoinHttpServer:) name:joinServer object:nil];
    
    // Do any additional setup after loading the view.
}

- (void)requestCameraPermission {
    [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo completionHandler:^(BOOL granted) {
        if (granted) {
            // 用户同意了访问摄像头
            dispatch_async(dispatch_get_main_queue(), ^{
                [self loadUI];
            });
            NSLog(@"Access to camera granted.");
            
        } else {
            // 用户拒绝了访问摄像头
            NSLog(@"Access to camera denied.");
            dispatch_async(dispatch_get_main_queue(), ^{
                [SVProgressHUD showInfoWithStatus:local(@"需要先进入设置允许app使用相机功能")];
                [self.navigationController popViewControllerAnimated:YES];
            });
            
        }
    }];
}

- (void)loadUI
{
    [self loadNatView];
    [self loadSegement];
    [self loadMainView];
    self.ipView =  [self addipview];
    self.scanView = [self addQRView];
    
    if(self.from!=3)
    {
        [self.view bringSubviewToFront:self.mainView];
    }
    else
    {
        
        [self.qrcard bringSubviewToFront:self.qrcapview];
        [self.captureSession startRunning];
        UILabel* la = [UILabel createLabelWithTitle:local(@"1.将手机和电脑接入同一局域网下\n\n2.在电脑浏览器访问此链接：http://www.eassync.com/#/点击右上角在线传输按钮\n\n3.使用本模块扫描网页端二维码即可成功连接\n\n4.下载文件到电脑：选择需要下载的文件，点击发送到电脑即可\n\n5.发送文件给手机：点击“发送到手机“按钮，选择好需要发送的文件发送即可") textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentLeft font:[UIFont systemFontOfSize:14 weight:UIFontWeightMedium]];
        
        la.userInteractionEnabled = YES;
        [la addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(labelTapped:)]];
        la.numberOfLines = 0;
        
        NSMutableAttributedString *attributedText = [[NSMutableAttributedString alloc] initWithString:local(@"1.将手机和电脑接入同一局域网下\n\n2.在电脑浏览器访问此链接：http://www.eassync.com/#/点击右上角在线传输按钮\n\n3.使用本模块扫描网页端二维码即可成功连接\n\n4.下载文件到电脑：选择需要下载的文件，点击发送到电脑即可\n\n5.发送文件给手机：点击“发送到手机“按钮，选择好需要发送的文件发送即可") attributes:@{NSFontAttributeName: [UIFont systemFontOfSize:14 weight:UIFontWeightMedium], NSForegroundColorAttributeName: [UIColor whiteColor]}];

        // 寻找并修改链接文本的样式
        NSString *linkText = @"http://www.eassync.com/#/";
        NSRange linkRange = [attributedText.string rangeOfString:linkText];
        if (linkRange.location != NSNotFound) {
            [attributedText addAttribute:NSUnderlineStyleAttributeName value:@(NSUnderlineStyleSingle) range:linkRange];
            [attributedText addAttribute:NSForegroundColorAttributeName value:[UIColor colorWithHexString:@"002BC2" alpha:1] range:linkRange];
        }

        // 将NSMutableAttributedString设置给UILabel
        la.attributedText = attributedText;
        
        [self.view addSubview:la];
        [la mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(self.qrcapview.mas_left);
            make.right.mas_equalTo(self.qrcapview.mas_right);
            make.top.mas_equalTo(self.qrcapview.mas_bottom).offset(20);
        }];
    }
    
//    UILabel* tip = [UILabel createLabelWithTitle:local(@"若长时间无法连接，可能由于是部分wifi网络限制局域网内通讯，请尝试使用手机的个人热点功能进行连接！") textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentCenter font:[UIFont systemFontOfSize:14 weight:UIFontWeightBold]];
//    tip.numberOfLines = 0;
//    [self.view addSubview:tip];
//    [tip mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.bottom.mas_equalTo(self.view.mas_bottomMargin).offset(-20);
//        make.width.mas_equalTo(kDeviceWidth-24*2);
//        make.centerX.mas_equalTo(0);
//    }];
//    self.tipLabel = tip;
}
- (void)labelTapped:(UITapGestureRecognizer *)gesture
{
    if (gesture.state == UIGestureRecognizerStateEnded) {
        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
        pasteboard.string = @"http://www.eassync.com/#/";
        
        // 提示用户链接已复制
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:local(@"复制") message:local(@"已经复制链接到粘贴板") preferredStyle:UIAlertControllerStyleAlert];
        [alert addAction:[UIAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault handler:nil]];
        [self presentViewController:alert animated:YES completion:nil];
    }
}
- (void)loadMainView
{
    
    
    UIView*bgview = [UIView new];
    bgview.backgroundColor = [UIColor colorWithHexString:@"#4DD0A7" alpha:1];
    [self.view addSubview:bgview];
    [bgview mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.bottom.mas_equalTo(0);
        make.top.mas_equalTo(self.mysegment.mas_bottom).offset(20);
    }];
    [bgview.superview layoutIfNeeded];

    BSTransferPeersVC* send = [BSTransferPeersVC new];
        send.view.frame = bgview.bounds;
        [self addChildViewController:send];
        [bgview addSubview:send.view];
    send.type = self.from;
    send.selPeerSuc = ^(BSPeerModel *peer) {
        [[BSSendFileManager manager] sendFileWithPeerModel:peer datas:self.dataArray];
    };
    self.mainView = bgview;
    
    BSLinkView* lineview = [BSLinkView new];
    [self.view addSubview:lineview];
    [lineview mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.mysegment.mas_bottom).offset((kDeviceWidth-18*2)+40);
        make.centerX.mas_equalTo(0);
    }];
    self.linkView = lineview;
    self.linkView.hidden = YES;
    lineview.chooseCallback = ^(id sender) {
        switch ([sender intValue]) {
            case 0:
            {
                [MobClick event:@"Connect" attributes:@{@"source":@"扫码连接"}];
                                [self.view bringSubviewToFront:self.scanView];
                [self.qrcard bringSubviewToFront:self.qrcapview];
                        [self.captureSession startRunning];
                [self.view bringSubviewToFront:self.linkView];
            }
                break;
            case 1:
            {
                [MobClick event:@"Connect" attributes:@{@"source":@"扫码连接"}];
                                [self.view bringSubviewToFront:self.scanView];
                [self.qrcard bringSubviewToFront:self.qrimageView];
                        [self.captureSession stopRunning];
                [self.view bringSubviewToFront:self.linkView];
            }
                break;
            case 2:
            {
                [MobClick event:@"Connect" attributes:@{@"source":@"IP链接"}];
                [self.view bringSubviewToFront:self.ipView];
                [self.view bringSubviewToFront:self.linkView];
                [self.captureSession stopRunning];
            }
                break;
                
            default:
                break;
        }
    };
    
}

-(void)viewWillAppear:(BOOL)animated
{
    self.navigationController.navigationBar.hidden = YES;
}


- (void)loadNatView{
    
    self.natMainSendView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kDeviceWidth,64+KDNavH)];
    self.natMainSendView.userInteractionEnabled = YES;
    [self.view addSubview:self.natMainSendView];
    
    UIButton *backButton = [[UIButton alloc]initWithFrame:CGRectMake(15, 12+25+KDNavH, 30, 30)];
    [backButton setImage:[UIImage imageNamed:@"black_back_icn"] forState:UIControlStateNormal];
    [backButton addTarget:self action:@selector(backMainAction) forControlEvents:UIControlEventTouchUpInside];
    [self.natMainSendView addSubview:backButton];
    
//    UILabel *titleLabel = [[UILabel alloc]initWithFrame:CGRectMake(kDeviceWidth/2-50,25+KDNavH,100, 30)];
//    titleLabel.text = NSLocalizedString(@"连接",nil);
//    titleLabel.textColor = [UIColor whiteColor];
//    titleLabel.font = [UIFont systemFontOfSize:15];
//    titleLabel.textAlignment = NSTextAlignmentCenter;
//    [self.view addSubview:titleLabel];
    
    UIButton* btn = [UIButton createButtonWithNormalImageName:@"扫码连接" normalTitle:local(@"扫码链接") normalColor:[UIColor colorWithHexString:@"3b9af0" alpha:1] seletedName:@"返回连接" seletedTitle:local(@"返回数据链接") seletColor:[UIColor colorWithHexString:@"3b9af0" alpha:1] font:[UIFont systemFontOfSize:15 weight:UIFontWeightMedium]];
    btn.backgroundColor = [[UIColor whiteColor]colorWithAlphaComponent:0.75];
    btn.layer.cornerRadius = 4;
    btn.layer.masksToBounds = YES;
    [self.natMainSendView addSubview:btn];
    [btn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(-15);
        make.width.mas_equalTo(144);
        make.height.mas_equalTo(45);
        make.centerY.mas_equalTo(backButton);
    }];
    [btn.superview layoutIfNeeded];
    [btn setImagePositionWithType:SSImagePositionTypeLeft spacing:4];
    [btn bk_whenTapped:^{
        btn.selected = !btn.selected;
        [btn.superview layoutIfNeeded];
        [btn setImagePositionWithType:SSImagePositionTypeLeft spacing:4];
        if(btn.selected)
        {
            [MobClick event:@"Connect" attributes:@{@"source":@"扫码连接"}];
                            [self.view bringSubviewToFront:self.scanView];
            [self.qrcard bringSubviewToFront:self.qrcapview];
                    [self.captureSession startRunning];
            self.linkView.hidden = NO;
            [self.view bringSubviewToFront:self.linkView];
            [self.linkView setchooseIndex:0];
        }
        else
        {
            [MobClick event:@"Connect" attributes:@{@"source":@"点击链接"}];
                            [self.view bringSubviewToFront:self.mainView];
            [self.captureSession stopRunning];
            self.linkView.hidden = YES;
            [self.view bringSubviewToFront:self.linkView];
        }
        
    }];
    if(self.from==3)
    {
        btn.hidden = YES;
        [self.view bringSubviewToFront:self.scanView];
        [self.qrcard bringSubviewToFront:self.qrcapview];
        [self.captureSession startRunning];
        [self.view bringSubviewToFront:self.linkView];
        [self.linkView setchooseIndex:0];
    }
    
}
- (void)backMainAction
{
    [[httpNework shareNetwork] closeServer];
    [self.navigationController popViewControllerAnimated:YES];
}
- (void)loadSegement
{
    MySegmentedControl* segment  = [MySegmentedControl new];
//    MySegmentedControl* segment = [[MySegmentedControl alloc]initWithItems:@[NSLocalizedString(@"数据传输", nil),NSLocalizedString(@"IP直连", nil),NSLocalizedString(@"扫码链接", nil)]];
//    segment.cornerRadius = 44.0*0.5;
//    segment.layer.masksToBounds = YES;
//    segment.selectedSegmentIndex = 0;
//    segment.backgroundColor = [[UIColor whiteColor]colorWithAlphaComponent:0.3];
    [self.view addSubview:segment];
//    segment.tintColor = [UIColor whiteColor];;
//    [segment setTitleTextAttributes:@{NSForegroundColorAttributeName:[UIColor colorWithHexString:@"#3b9af0" alpha:1],NSFontAttributeName:[UIFont systemFontOfSize:14 weight:UIFontWeightMedium]} forState:UIControlStateSelected];
//    [segment setTitleTextAttributes:@{NSForegroundColorAttributeName:[UIColor whiteColor],NSFontAttributeName:[UIFont systemFontOfSize:14 weight:UIFontWeightMedium]} forState:UIControlStateNormal];
    [segment mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo((kDeviceWidth-16*2)*1.0/3*segment.numberOfSegments);
        make.height.mas_equalTo(0);
        make.top.mas_equalTo(self.natMainSendView.mas_bottom).offset(20);
        make.centerX.mas_equalTo(0);
    }];
//    
//    [segment bk_addEventHandler:^(id  _Nonnull sender) {
//        switch (segment.selectedSegmentIndex) {
//            case 0:
//            {
//                [MobClick event:@"Connect" attributes:@{@"source":@"点击链接"}];
//                [self.view bringSubviewToFront:self.mainView];
//                
//            }
//                break;
//            case 1:
//            {
//                [MobClick event:@"Connect" attributes:@{@"source":@"IP链接"}];
//                [self.view bringSubviewToFront:self.ipView];
//                
//            }
//                break;
//            case 2:
//            {
//                [MobClick event:@"Connect" attributes:@{@"source":@"扫码连接"}];
//                [self.view bringSubviewToFront:self.scanView];
//                
//            }
//                break;
//                
//            default:
//                break;
//        }
//        [self.view bringSubviewToFront:self.tipLabel];
//    } forControlEvents:UIControlEventValueChanged];
    self.mysegment = segment;
}


- (UIView*)addipview
{
    UIView*bgview = [UIView new];
    UIImage *image = [UIImage imageNamed:@"home_bg"];
    UIGraphicsBeginImageContextWithOptions(self.view.frame.size, NO, 0.f);
    [image drawInRect:self.view.bounds];
        UIImage *lastImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    bgview.backgroundColor = [UIColor colorWithPatternImage:lastImage];
    
    
//    bgview.backgroundColor = [UIColor colorWithHexString:@"#4DD0A7" alpha:1];
    [self.view addSubview:bgview];
    [bgview mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.bottom.mas_equalTo(0);
        make.top.mas_equalTo(self.mysegment.mas_bottom);
    }];
    
    UIView* card = [UIView new];
    card.backgroundColor = [UIColor whiteColor];
    card.layer.cornerRadius = 16;
    card.layer.masksToBounds = YES;
    [bgview addSubview:card];
    [card mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(16);
        make.right.mas_equalTo(-16);
        make.top.mas_equalTo(20);
        make.height.mas_equalTo(217);
    }];
    UITextField* f = [UITextField new];
    if(self.type == 0)
    {
        f = [UITextField new];
        f.font = [UIFont systemFontOfSize:14];
        f.backgroundColor = [UIColor colorWithHexString:@"#F1F1F1" alpha:1];
        f.layer.cornerRadius = 11;
        f.layer.masksToBounds = YES;
        f.leftView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, 20, 56)];
        f.rightView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, 20, 56)];
        f.placeholder = local(@"请输入连接设备等IP地址");
        f.leftViewMode = UITextFieldViewModeAlways;
        f.rightViewMode = UITextFieldViewModeAlways;
        [card addSubview:f];
        [f mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(32);
            make.left.mas_equalTo(24);
            make.right.mas_equalTo(-24);
            make.height.mas_equalTo(56);
        }];
        
        UIButton* btn = [UIButton createButtonWithTitle:local(@"链接") color:[UIColor whiteColor] font:[UIFont systemFontOfSize:12]];
        btn.layer.cornerRadius = 8;
        btn.layer.masksToBounds = YES;
        btn.backgroundColor = [UIColor colorWithHexString:@"#3b9af0" alpha:1];
        [card addSubview:btn];
        [btn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.mas_equalTo(-32);
            make.left.mas_equalTo(24);
            make.right.mas_equalTo(-24);
            make.height.mas_equalTo(36);
        }];
        [btn bk_whenTapped:^{
            [MobClick event:@"Connect" attributes:@{@"IP_connection":@"点击链接"}];
            [self.view endEditing:YES];
            [self joinHttpServer:f.text];
        }];
    }
    
    UILabel* la = [UILabel new];
    la.font = [UIFont systemFontOfSize:12];
    la.text = [NSString stringWithFormat:local(@"我的IP地址：http://%@:8888"),[DHIPAdress deviceIPAdress]];
    la.textColor = [UIColor colorWithHexString:@"333333" alpha:1];
    [card addSubview:la];
    [la mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(0);
        make.left.mas_equalTo(40);
    }];
    
    
    
    UIButton* btn = [UIButton createButtonWithTitle:local(@"复制") color:[UIColor whiteColor] font:[UIFont systemFontOfSize:10 weight:UIFontWeightMedium]];
    btn.backgroundColor = [UIColor colorWithHexString:@"#3b9af0" alpha:1];
    btn.layer.cornerRadius = 2;
    btn.layer.masksToBounds = YES;
    [card  addSubview:btn];
    [btn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(la.mas_right).offset(20);
        make.centerY.mas_equalTo(la);
        make.width.mas_equalTo(36);
        make.height.mas_equalTo(16);
    }];
    
    if(self.type==1)
    {
        la.font = [UIFont systemFontOfSize:14 weight:UIFontWeightMedium];
        [la mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(-30);
            make.centerX.mas_equalTo(0);
        }];
        [card mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(16);
            make.right.mas_equalTo(-16);
            make.top.mas_equalTo(70);
            make.height.mas_equalTo(160);
        }];
        [btn mas_remakeConstraints:^(MASConstraintMaker *make) {
            
            make.centerX.mas_equalTo(la);
            make.top.mas_equalTo(la.mas_bottom).offset(30);
            make.width.mas_equalTo(120);
            make.height.mas_equalTo(36);
        }];
    }
    
    [btn bk_whenTapped:^{
        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
        [pasteboard setString:[NSString stringWithFormat:@"http://%@:8888",[DHIPAdress deviceIPAdress]]];
        [SVProgressHUD showSuccessWithStatus:local(@"复制成功")];
    }];
    
    
    return bgview;
}

- (UIView*)addQRView
{
    UIView*bgview = [UIView new];
    UIImage *image = [UIImage imageNamed:@"home_bg"];
    UIGraphicsBeginImageContextWithOptions(self.view.frame.size, NO, 0.f);
    [image drawInRect:self.view.bounds];
        UIImage *lastImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    bgview.backgroundColor = [UIColor colorWithPatternImage:lastImage];
    [self.view addSubview:bgview];
    [bgview mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.bottom.mas_equalTo(0);
        make.top.mas_equalTo(self.mysegment.mas_bottom);
    }];
    
    UIView* cardbac = [UIView new];
    cardbac.backgroundColor = [UIColor whiteColor];
    cardbac.layer.cornerRadius = 16;
    cardbac.layer.masksToBounds = YES;
    [bgview addSubview:cardbac];
    [cardbac mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(kDeviceWidth-18*2);
        make.centerX.mas_equalTo(0);
        make.top.mas_equalTo(20);
        make.height.mas_equalTo(kDeviceWidth-18*2);
    }];
    
    
    
    UIView* card = [UIView new];
    card.backgroundColor = [UIColor whiteColor];
    card.layer.cornerRadius = 16;
    card.layer.masksToBounds = YES;
    [cardbac addSubview:card];
    [card mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(kDeviceWidth-18*2);
        make.centerX.mas_equalTo(0);
        make.top.mas_equalTo(0);
        make.height.mas_equalTo(kDeviceWidth-18*2);
    }];
    [card.superview layoutIfNeeded];
    self.qrcard = card;
//    UIButton* btn1 = [UIButton createButtonWithTitle:local(@"扫码") color:[UIColor colorWithHexString:@"#3b9af0" alpha:1] font:[UIFont systemFontOfSize:14 weight:UIFontWeightMedium]];
//    [cardbac addSubview:btn1];
//    [btn1 mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.left.mas_equalTo(24);
//        make.centerY.mas_equalTo(card.mas_bottom).offset(30);
//    }];
//    
//    
//    UIButton* btn2 = [UIButton createButtonWithTitle:local(@"我的二维码") color:[UIColor colorWithHexString:@"#3b9af0" alpha:1] font:[UIFont systemFontOfSize:14 weight:UIFontWeightMedium]];
//    [cardbac addSubview:btn2];
//    [btn2 mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.right.mas_equalTo(-24);
//        make.centerY.mas_equalTo(card.mas_bottom).offset(30);
//    }];
    
    UIView* capview = [UIView new];
    capview.backgroundColor = [UIColor whiteColor];
    capview.frame = card.bounds;
    self.qrcapview = capview;
    [card addSubview:capview];
        self.captureSession = [[AVCaptureSession alloc] init];
        AVCaptureDevice *device = [AVCaptureDevice defaultDeviceWithMediaType:AVMediaTypeVideo];
        NSError *error = nil;
        
        AVCaptureDeviceInput *input = [AVCaptureDeviceInput deviceInputWithDevice:device error:&error];
        if (input) {
            [self.captureSession addInput:input];
        } else {
            NSLog(@"Error: %@", error);
        }
        
        AVCaptureMetadataOutput *metadataOutput = [[AVCaptureMetadataOutput alloc] init];
        [self.captureSession addOutput:metadataOutput];
        
        [metadataOutput setMetadataObjectsDelegate:self queue:dispatch_get_main_queue()];
        [metadataOutput setMetadataObjectTypes:@[AVMetadataObjectTypeQRCode]];
        
        AVCaptureVideoPreviewLayer *previewLayer = [AVCaptureVideoPreviewLayer layerWithSession:self.captureSession];
        previewLayer.frame = card.bounds;
        previewLayer.videoGravity = AVLayerVideoGravityResizeAspectFill;
        [capview.layer addSublayer:previewLayer];
        
        [self.captureSession startRunning];
    
        UIImage *qrCodeImage = [self creatQrcode:[NSString stringWithFormat:@"http://%@:8888",[DHIPAdress deviceIPAdress]]];
        
        UIImageView *imageView = [[UIImageView alloc] initWithImage:qrCodeImage];
        imageView.frame = card.bounds; // Set the frame as needed
        imageView.contentMode = UIViewContentModeScaleAspectFit;
        [card addSubview:imageView];
    
    self.qrimageView = imageView;
    [self.captureSession stopRunning];
//    [btn1 bk_whenTapped:^{
//        [card bringSubviewToFront:capview];
//        [self.captureSession startRunning];
//    }];
//    [btn2 bk_whenTapped:^{
//        [card bringSubviewToFront:imageView];
//        [self.captureSession stopRunning];
//    }];
    
    return bgview;
}


- (UIImage *)creatQrcode:(NSString *)urlString {
    // Create a data object from the URL string
    NSData *data = [urlString dataUsingEncoding:NSUTF8StringEncoding];
    
    // Create a CIFilter with the QR code generator filter
    CIFilter *qrFilter = [CIFilter filterWithName:@"CIQRCodeGenerator"];
    [qrFilter setValue:data forKey:@"inputMessage"];
    [qrFilter setValue:@"Q" forKey:@"inputCorrectionLevel"];
    
    // Convert the CIImage to UIImage
    CIImage *ciImage = qrFilter.outputImage;
    UIImage *qrCodeImage = [self createNonInterpolatedUIImageFromCIImage:ciImage withScale:2*[[UIScreen mainScreen] scale]];
    
    return qrCodeImage;
}

- (UIImage *)createNonInterpolatedUIImageFromCIImage:(CIImage *)image withScale:(CGFloat)scale {
    CGRect rect = CGRectIntegral(image.extent);
    CGSize size = CGSizeMake(CGRectGetWidth(rect) * scale, CGRectGetHeight(rect) * scale);
    UIGraphicsBeginImageContextWithOptions(size, NO, 0.0f);
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextSetInterpolationQuality(context, kCGInterpolationNone);
    CIContext *ciContext = [CIContext contextWithOptions:nil];
    CGImageRef cgImage = [ciContext createCGImage:image fromRect:rect];
    CGContextDrawImage(context, CGContextGetClipBoundingBox(context), cgImage);
    UIImage *scaledImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    CGImageRelease(cgImage);
    
    return scaledImage;
}


- (void)captureOutput:(AVCaptureOutput *)captureOutput didOutputMetadataObjects:(NSArray *)metadataObjects fromConnection:(AVCaptureConnection *)connection {
    if (metadataObjects != nil && [metadataObjects count] > 0) {
        AVMetadataMachineReadableCodeObject *metadataObj = [metadataObjects objectAtIndex:0];
        if ([[metadataObj type] isEqualToString:AVMetadataObjectTypeQRCode]) {
            // 找到二维码
            NSString *qrCodeString = [metadataObj stringValue];
            NSLog(@"QR Code: %@", qrCodeString);
            [self.view endEditing:YES];
            
            if([qrCodeString containsString:@"connections"]&&self.from==3)
            {
                UIDevice *currentDevice = [UIDevice currentDevice];
                NSString *deviceName = currentDevice.name;
                NSString* token = [tokenTool creatToken];
                NSDictionary* dic  = @{@"host":[DHIPAdress deviceIPAdress],@"token":token,@"deviceName":deviceName};
                NSLog(@"%@",dic);
                [[httpNework shareNetwork]sendPUTRequestWithURL:qrCodeString parameters:dic completed:^(id  _Nonnull responseObject, NSError * _Nonnull error) {
                    if(1)
                    {
                        dispatch_async(dispatch_get_main_queue(), ^{
                        AppDelegate* app = APPDELEGATE;
                        app.currentLinkUrl = qrCodeString;
                        app.currentLinkToken =  token;
                        app.currentDeviceName  = @"PC";
                        [app.whiteList addObject:app.currentLinkToken];
                        
                        
                            self.FileDateCompelete(@"PC");
                            // 在主线程上执行界面操作
                            // 您的布局引擎修改代码应该放在这里
//                            FileDateViewController* file = [FileDateViewController new];
//                            file.targetName  = @"PC";
//                            [self presentViewController:file animated:YES completion:^{
//                                
//                            }];
                        });
                    }
                }];
                
                [self.captureSession stopRunning];
            }
            else
            {
                if(_from!=3&&![qrCodeString containsString:@"connections"])
                {
                    [MobClick event:@"Connect" attributes:@{@"Transmission_mode":@"扫码连接"}];
                    [self joinHttpServer:qrCodeString];
                    [MobClick event:@"Connect" attributes:@{@"QR_Connect":@"成功链接"}];
                    // 停止会话
                    [self.captureSession stopRunning];
                }
            }
            // 这里可以根据需要处理二维码内容
            // ...
        }
    }
}

- (void)openserver
{
    [[httpNework shareNetwork]openServer];
    
}

#pragma 握手过程
//发送方
//第一步获取名字显示
- (void)getOtherNames:(NSString*)url  compeletBlock:(CompletionHandler)block
{
    
    [[httpNework shareNetwork]sendGETRequestWithURL:[NSString stringWithFormat:@"%@/getServerName",url] parameters:nil completed:^(id  _Nonnull responseObject, NSError * _Nonnull error) {
        block(responseObject,error);
    }];
}
//第二步获取到名字申请join
- (void)joinHttpServer:(NSString*)url
{
    
    UIDevice *currentDevice = [UIDevice currentDevice];
    NSString *deviceName = currentDevice.name;
    NSDictionary* dic = @{@"deviceName":deviceName};
    AppDelegate* app = APPDELEGATE;
    [SVProgressHUD show];
    [[httpNework shareNetwork]sendGETRequestWithURL:[NSString stringWithFormat:@"%@/joinServer",url] parameters:dic completed:^(id  _Nonnull responseObject, NSError * _Nonnull error) {
        if(!error)
        {
            [SVProgressHUD showInfoWithStatus:local(@"请求已经发送请等待回复")];
        }
        else
        {
            [SVProgressHUD showInfoWithStatus:local(@"请求失败")];
            [self.captureSession startRunning];
            
        }
    }];
}
//第三步收到返回的通知
- (void)receiveHttpJoin:(NSNotification*)noti
{
    NSDictionary* dic = noti.object;
    if(![dic[@"status"] boolValue])
    {
        [SVProgressHUD showInfoWithStatus:[NSString stringWithFormat:local(@"%@拒绝您的请求"),dic[@"deviceName"]]];
        [self.captureSession startRunning];
    }
    else
    {
        [SVProgressHUD showInfoWithStatus:[NSString stringWithFormat:local(@"%@接收您的请求"),dic[@"deviceName"]]];
        AppDelegate* app = APPDELEGATE;
        app.currentLinkUrl = dic[@"url"];
        app.currentLinkToken =  dic[@"authorizationToken"];
        app.currentDeviceName  = dic[@"deviceName"];
        [app.whiteList addObject:app.currentLinkToken];
        [MobClick event:@"Connect" attributes:@{@"IP_connection":@"成功链接"}];

        dispatch_async(dispatch_get_main_queue(), ^{
            // 在主线程上执行界面操作
            // 您的布局引擎修改代码应该放在这里
            self.FileDateCompelete(app.currentDeviceName);
//            FileDateViewController* file = [FileDateViewController new];
//            file.targetName  = app.currentDeviceName;
//            [self presentViewController:file animated:YES completion:^{
//                
//            }];
        });
    }
}


//接收方
//收到加入请求
- (void)receiveJoinHttpServer:(NSNotification*)noti
{
    dispatch_async(dispatch_get_main_queue(), ^{
        NSDictionary* dic = noti.object;
        UIAlertController *alertController = [UIAlertController alertControllerWithTitle:[NSString stringWithFormat:local(@"%@请求加入您的文件传输"),dic[@"deviceName"]] message:nil preferredStyle:UIAlertControllerStyleAlert];
        UIDevice *currentDevice = [UIDevice currentDevice];
        NSString *deviceName = currentDevice.name;
        //增加取消按钮；
        [alertController addAction:[UIAlertAction actionWithTitle:NSLocalizedString(@"接受",nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action){
            NSString* token =  [tokenTool creatToken];
            NSDictionary* dict = @{@"deviceName":deviceName,@"status":@(1),@"authorizationToken":token};
            NSString* url = dic[@"url"];
            [[httpNework shareNetwork]sendGETRequestWithURL:[NSString stringWithFormat:@"%@/receiveJoin",url] parameters:dict completed:^(id  _Nonnull responseObject, NSError * _Nonnull error) {
                AppDelegate* app = APPDELEGATE;
                [app.whiteList addObject:token];
                app.currentLinkUrl = url;
                app.currentDeviceName = dic[@"deviceName"];
                app.currentLinkToken =  token;
                
                dispatch_async(dispatch_get_main_queue(), ^{
                    // 在主线程上执行界面操作
                    // 您的布局引擎修改代码应该放在这里
                    self.FileDateCompelete(app.currentDeviceName);
//                    FileDateViewController* file = [FileDateViewController new];
//                    file.targetName  = app.currentDeviceName;
//                    [self presentViewController:file animated:YES completion:^{
//                        
//                    }];
                });
                 
            }];
        }]];
        [alertController addAction:[UIAlertAction actionWithTitle:NSLocalizedString(@"拒绝",nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action){
            NSDictionary* dict = @{@"deviceName":deviceName,@"status":@(0)};
            NSString* url = dic[@"url"];
            [[httpNework shareNetwork]sendGETRequestWithURL:[NSString stringWithFormat:@"%@/receiveJoin",url] parameters:dict completed:^(id  _Nonnull responseObject, NSError * _Nonnull error) {
                
            }];
            
        }]];
        [self presentViewController:alertController animated:true completion:nil];
    });
    
}

-(void)viewWillDisappear:(BOOL)animated
{
    [[NSNotificationCenter defaultCenter]removeObserver:self];
    self.navigationController.navigationBar.hidden = NO;
}

/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */

@end
