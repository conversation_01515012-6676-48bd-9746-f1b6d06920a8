//
//  BSLinkButton.m
//  PhoneCloneV2
//
//  Created by fs0011 on 2024/2/19.
//  Copyright © 2024 PhoneClone. All rights reserved.
//

#import "BSLinkButton.h"

@implementation BSLinkButton
{
    NSString* _name;
    UIImageView* _icon;
    UILabel* _title;
    UIImageView* _nextView;
}
- initWithName:(NSString*)name
{
    if(self == [super init])
    {
        _name = name;
        self.backgroundColor = [UIColor colorWithHexString:@"#83C2F9" alpha:1];
        UIImageView* icon = [UIImageView new];
        icon.image = [UIImage imageNamed:name];
        [self addSubview:icon];
        [icon mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(14);
            make.width.height.mas_equalTo(26);
            make.centerY.mas_equalTo(0);
        }];
        _icon = icon;
        
        UILabel* la = [UILabel createLabelWithTitle:local(name) textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentLeft font:[UIFont systemFontOfSize:15]];
        [self addSubview:la];
        [la mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(53);
            make.centerY.mas_equalTo(0);
        }];
        _title = la;
        
        UIImageView* next = [UIImageView new];
        next.image = [UIImage imageNamed:@"next"];
        [self addSubview:next];
        [next mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.mas_equalTo(-28);
            make.width.mas_equalTo(7);
            make.height.mas_equalTo(14);
            make.centerY.mas_equalTo(0);
        }];
        _nextView = next;
        
        
    }
    return self;
}
/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

- (void)setChoose:(BOOL)isChoose {
    self.backgroundColor = isChoose?[UIColor whiteColor]:[UIColor colorWithHexString:@"#83C2F9" alpha:1];
    _icon.image = isChoose?[UIImage imageNamed:[NSString stringWithFormat:@"选中%@",_name]]:[UIImage imageNamed:_name];
    _title.textColor = isChoose?[UIColor colorWithHexString:@"#83C2F9" alpha:1]:[UIColor whiteColor];
    _nextView.image = isChoose?[UIImage imageNamed:@"选中next"]:[UIImage imageNamed:@"next"];
    
}

@end
