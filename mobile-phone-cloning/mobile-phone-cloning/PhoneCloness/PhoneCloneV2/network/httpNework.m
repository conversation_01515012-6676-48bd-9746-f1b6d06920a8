//
//  httpNework.m
//  mobiletransfer
//
//  Created by fs0011 on 2023/12/5.
//  Copyright © 2023 <PERSON><PERSON><PERSON>. All rights reserved.
//

#import "httpNework.h"
#import "MyHTTPConnection.h"

@implementation httpNework
+ (instancetype)shareNetwork;
{
    static httpNework *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[httpNework alloc] init];
        instance.sessionManager = [[AFHTTPSessionManager alloc] init];
                // 根据需要配置sessionManager，例如设置responseSerializer
        instance.sessionManager.responseSerializer = [AFJSONResponseSerializer serializer];
        
       
    });
    return instance;
}

- (void)openServer
{
    self.server = [[HTTPServer alloc] init];
    [self.server setType:@"_http._tcp."];
    // webPath是server搜寻HTML等文件的路径
    __weak typeof(self) weakSelf = self;  // prevent retain cycle

    void (^webPathHandler)(id);           // predefinition

    webPathHandler = ^(id sender) {
        if (sender) {
            [weakSelf.server setDocumentRoot:sender];
            [weakSelf.server setConnectionClass:[MyHTTPConnection class]];
            [weakSelf.server setPort:8888];
            NSError *err = nil;
            NSString *IPAdress = [DHIPAdress deviceIPAdress];
            
            if ([weakSelf.server start:&err] && [weakSelf.server isRunning]) {
                NSLog(@"http://%@:%hu", IPAdress, [weakSelf.server listeningPort]);
            }
            else{
                NSLog(@"%@", err);
                [weakSelf GetWebpath:webPathHandler]; // get webPath again when fail
            }
        }
        else {
            [weakSelf GetWebpath:webPathHandler]; // get webPath again when fail
        }
    };

    [self GetWebpath:webPathHandler];
    
    
}
- (void)GetWebpath:(ArgumentCallBack)callback
{
// 获取应用程序包的路径
    NSString *documentsFolderPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) firstObject]; // 获取沙盒中Documents目录路径
    NSString *distFolderPath = [documentsFolderPath stringByAppendingPathComponent:@"dist"]; // 创建dist文件夹在沙盒中

    // 检查dist文件夹是否已存在
    
        AFHTTPSessionManager *_sessionManager = [AFHTTPSessionManager manager];;
        _sessionManager.requestSerializer = [AFJSONRequestSerializer serializer];
        [_sessionManager.requestSerializer setValue:@"application/json" forHTTPHeaderField:@"Accept"];
        [_sessionManager GET:webVersionJsonUrl parameters:nil headers:nil progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
            NSDictionary* dic = responseObject;
            //检查版本
            NSString* version = dic[@"version"];
           NSDictionary* localVerSion = [self readWebVersionLocalFile];
            if ([[NSFileManager defaultManager]fileExistsAtPath:distFolderPath]&&localVerSion&&[dic[@"version"] isEqualToString:localVerSion[@"version"]]) {
                [self changeIP:distFolderPath];
                callback(distFolderPath);
            }
            else
            {
                NSURL *fileURL = [NSURL URLWithString:webZipUrl];
                NSString* zipPath = [documentsFolderPath stringByAppendingPathComponent:@"dist.zip"];
                // 设置下载路径
                NSURLSessionDownloadTask *downloadTask = [_sessionManager downloadTaskWithRequest:[NSURLRequest requestWithURL:fileURL] progress:^(NSProgress *downloadProgress) {
                    NSLog(@"Download progress: %.2f%%", downloadProgress.fractionCompleted * 100);
                } destination:^NSURL * _Nonnull(NSURL * _Nonnull targetPath, NSURLResponse * _Nonnull response) {
                    return [NSURL fileURLWithPath:zipPath];
                } completionHandler:^(NSURLResponse *response, NSURL *filePath, NSError *error) {
                    if (!error) {
                        NSLog(@"Error downloading file: %@", error);
                        [SSZipArchive unzipFileAtPath:filePath.path toDestination:documentsFolderPath progressHandler:^(NSString * _Nonnull entry, unz_file_info zipInfo, long entryNumber, long total) {
                            NSLog(@"Unzipping file: %@", entry);
                        } completionHandler:^(NSString * _Nonnull path, BOOL succeeded, NSError * _Nullable error) {
                            if (succeeded) {
                                    NSLog(@"Unzip succeeded. Files extracted to: %@", path);
                                [self changeIP:distFolderPath];
                                // 创建一个包含JSON数据的NSDictionary
                                NSDictionary *jsonDictionary = @{
                                    @"version": version
                                };

                                // 将NSDictionary转换为NSData格式
                                NSError *error;
                                NSData *jsonData = [NSJSONSerialization dataWithJSONObject:jsonDictionary options:NSJSONWritingPrettyPrinted error:&error];

                                if (error) {
                                    NSLog(@"Error creating JSON data: %@", error);
                                } else {
                                    // 指定路径来保存JSON文件
                                    
                                    NSString *documentsFolderPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) firstObject]; // 获取沙盒中Documents目录路径
                                    NSString *webVersionJson = [documentsFolderPath stringByAppendingPathComponent:@"easync_web_html_version.json"];
                                    NSString *filePath = webVersionJson;
                                    
                                    // 写入JSON数据到指定路径的文件
                                    BOOL success = [jsonData writeToFile:filePath options:NSDataWritingAtomic error:&error];
                                    
                                    if (success) {
                                        NSLog(@"JSON data saved to: %@", filePath);
                                    } else {
                                        NSLog(@"Error saving JSON data: %@", error);
                                    }
                                }
                                callback(distFolderPath);
                                } else {
                                    callback(nil);
                                    NSLog(@"Unzip failed with error: %@", error);
                            }
                        }];
                        
                    } else {
                        callback(nil);
                        NSLog(@"File downloaded to: %@", filePath);
                    }
                }];
                [downloadTask resume];
            }
            
            
        } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
            callback(nil);
        }];
        
    
    
}

- (NSDictionary *)readWebVersionLocalFile {
    // 获取文件路径
    NSString *documentsFolderPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) firstObject]; // 获取沙盒中Documents目录路径
    NSString *webVersionJson = [documentsFolderPath stringByAppendingPathComponent:@"easync_web_html_versions.json"];
    
    // 将文件数据化
    NSData *data = [[NSData alloc] initWithContentsOfFile:webVersionJson];
    // 对数据进行JSON格式化并返回字典形式
    if(data)
    {
        return [NSJSONSerialization JSONObjectWithData:data options:kNilOptions error:nil];
    }else
    {
        return nil;
    }
}


- (void)changeIP:(NSString*)distFolderPath
{
    
    NSString *filePathInSandbox = [distFolderPath stringByAppendingPathComponent:@"static/myIP.json"];
    NSError *error;
    NSData *jsonData = [NSData dataWithContentsOfFile:filePathInSandbox options:NSDataReadingMappedIfSafe error:&error];
    if (error) {
        NSLog(@"Error reading file: %@", error.localizedDescription);
        
    }

    NSError *jsonError;
    NSMutableDictionary *jsonDict = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingMutableContainers error:&jsonError];
    if (jsonError) {
        NSLog(@"Error parsing JSON: %@", jsonError.localizedDescription);
       
    }

    jsonDict[@"ip"] = [DHIPAdress deviceIPAdress];
    UIDevice *currentDevice = [UIDevice currentDevice];
    NSString *deviceName = currentDevice.name;
    jsonDict[@"deviceName"] = deviceName;

    NSData *newJsonData = [NSJSONSerialization dataWithJSONObject:jsonDict options:NSJSONWritingPrettyPrinted error:&jsonError];
    if (jsonError) {
        NSLog(@"Error converting JSON to Data: %@", jsonError.localizedDescription);
        
    }

    NSError *removeError;
    if (![[NSFileManager defaultManager] removeItemAtPath:filePathInSandbox error:&removeError]) {
        NSLog(@"Failed to delete file: %@", removeError.localizedDescription);
      
    }

    if (![newJsonData writeToFile:filePathInSandbox atomically:YES]) {
        NSLog(@"Error writing file");
    } else {
        NSLog(@"JSON file updated successfully.");
    }
    
    
    
    
}


- (void)closeServer
{
    [self.server stop];
}

- (void)sendGETRequestWithURL:(NSString *)urlString
                   parameters:(NSDictionary *)parameters
                    completed:(CompletionHandler)completionBlock {
    self.sessionManager.responseSerializer = [AFJSONResponseSerializer serializer];
    NSMutableSet *acceptableContentTypes = [NSMutableSet setWithSet:self.sessionManager.responseSerializer.acceptableContentTypes];
    [acceptableContentTypes addObject:@"text/html"];
    [acceptableContentTypes addObject:@"text/plain"];
    self.sessionManager.responseSerializer.acceptableContentTypes = acceptableContentTypes;
    NSCharacterSet *allowedCharacterSet = [NSCharacterSet URLQueryAllowedCharacterSet];

    // 对 URL 字符串进行编码
    NSString *encodedUrlString = [urlString stringByAddingPercentEncodingWithAllowedCharacters:allowedCharacterSet];
    [self.sessionManager GET:encodedUrlString parameters:parameters headers:nil progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        if (completionBlock) {
            completionBlock(responseObject, nil);
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        if (completionBlock) {
            completionBlock(nil, error);
        }
    }];
}


- (void)sendPOSTRequestWithURL:(NSString *)urlString
                    parameters:(NSDictionary *)parameters
                     completed:(CompletionHandler)completionBlock {
    self.sessionManager.responseSerializer = [AFJSONResponseSerializer serializer];
    NSMutableSet *acceptableContentTypes = [NSMutableSet setWithSet:self.sessionManager.responseSerializer.acceptableContentTypes];
    [acceptableContentTypes addObject:@"text/html"];
    [acceptableContentTypes addObject:@"text/plain"];
    self.sessionManager.responseSerializer.acceptableContentTypes = acceptableContentTypes;
    NSCharacterSet *allowedCharacterSet = [NSCharacterSet URLQueryAllowedCharacterSet];

    // 对 URL 字符串进行编码
    NSString *encodedUrlString = [urlString stringByAddingPercentEncodingWithAllowedCharacters:allowedCharacterSet];
    [self.sessionManager POST:encodedUrlString parameters:parameters headers:nil constructingBodyWithBlock:^(id<AFMultipartFormData>  _Nonnull formData) {
        
    } progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        if (completionBlock) {
            completionBlock(responseObject, nil);
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        if (completionBlock) {
            completionBlock(nil, error);
        }
    }];
     
     

}


- (void)sendPUTRequestWithURL:(NSString *)urlString
                   parameters:(NSDictionary *)parameters
                    completed:(void (^)(id responseObject, NSError *error))completionBlock
{
    NSURL *url = [NSURL URLWithString:urlString];

    // 创建一个请求对象
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url];

    // 设置请求方法为 PUT
    [request setHTTPMethod:@"PUT"];

    // 设置请求头，如 Content-Type
    [request setValue:@"application/json" forHTTPHeaderField:@"Content-Type"];

    // 序列化参数为 JSON 数据
    NSError *serializationError = nil;
    NSData *requestData = [NSJSONSerialization dataWithJSONObject:parameters options:0 error:&serializationError];

    // 检查序列化是否成功
    if (serializationError) {
        if (completionBlock) {
            completionBlock(nil, serializationError);
        }
        return;
    }

    // 设置请求体
    [request setHTTPBody:requestData];

    // 创建一个 NSURLSession
    NSURLSession *session = [NSURLSession sharedSession];

    // 创建一个数据任务
    NSURLSessionDataTask *dataTask = [session dataTaskWithRequest:request completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        if (error) {
            // 处理错误情况
            if (completionBlock) {
                completionBlock(nil, error);
            }
        } else {
            // 处理成功情况
            NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
            NSError *jsonError = nil;
            id responseObject = [NSJSONSerialization JSONObjectWithData:data options:0 error:&jsonError];

            if (jsonError) {
                if (completionBlock) {
                    completionBlock(nil, jsonError);
                }
            } else {
                if (completionBlock) {
                    completionBlock(responseObject, nil);
                }
            }
        }
    }];

    // 开始任务
    [dataTask resume];
}

@end
