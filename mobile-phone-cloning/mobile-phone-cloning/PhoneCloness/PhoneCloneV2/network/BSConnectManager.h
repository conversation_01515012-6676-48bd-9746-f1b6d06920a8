//
//  BSConnectManager.h
//  PhoneCloneV2
//
//  Created by fs0011 on 2024/1/15.
//  Copyright © 2024 PhoneClone. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
NS_ASSUME_NONNULL_BEGIN

@protocol BSConnectManagerDelegate<NSObject>
- (void)failQRcode;
- (void)connectSuccess;
@end


@interface BSConnectManager : NSObject
+ (instancetype)shareManager;
@property UIViewController<BSConnectManagerDelegate>* delegate;

- (void)joinHttpServer:(NSString*)url;

@end

NS_ASSUME_NONNULL_END
