//
//  httpNework.h
//  mobiletransfer
//
//  Created by fs0011 on 2023/12/5.
//  Copyright © 2023 <PERSON><PERSON><PERSON>. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "AFHTTPSessionManager.h"
#import "HTTPServer.h"
NS_ASSUME_NONNULL_BEGIN
typedef void (^CompletionHandler)(id responseObject, NSError *error);

@interface httpNework : NSObject
@property (strong, nonatomic) AFHTTPSessionManager *sessionManager;
@property (strong, nonatomic) HTTPServer* server;
+ (instancetype)shareNetwork;
- (void)sendGETRequestWithURL:(NSString *)urlString
                   parameters:(NSDictionary *)parameters
                    completed:(CompletionHandler)completionBlock;
- (void)sendPOSTRequestWithURL:(NSString *)urlString
                    parameters:(NSDictionary *)parameters
                     completed:(CompletionHandler)completionBlock;
- (void)sendPUTRequestWithURL:(NSString *)urlString
              parameters:(NSDictionary *)parameters
                    completed:(CompletionHandler)completionBlock;
- (void)openServer;
- (void)closeServer;
@end

NS_ASSUME_NONNULL_END
