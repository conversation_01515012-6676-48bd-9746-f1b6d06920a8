//
//  BSConnectManager.m
//  PhoneCloneV2
//
//  Created by fs0011 on 2024/1/15.
//  Copyright © 2024 PhoneClone. All rights reserved.
//

#import "BSConnectManager.h"
#import "tokenTool.h"

@implementation BSConnectManager
#pragma 握手过程


+ (instancetype)shareManager;
{
    static BSConnectManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[BSConnectManager alloc] init];
        [[NSNotificationCenter defaultCenter]addObserver:instance selector:@selector(receiveHttpJoin:) name:receiveJoin object:nil];
        [[NSNotificationCenter defaultCenter]addObserver:instance selector:@selector(receiveJoinHttpServer:) name:joinServer object:nil];
       
    });
    return instance;
}
//发送方
//第一步获取名字显示
- (void)getOtherNames:(NSString*)url  compeletBlock:(CompletionHandler)block
{
    
    [[httpNework shareNetwork]sendGETRequestWithURL:[NSString stringWithFormat:@"%@/getServerName",url] parameters:nil completed:^(id  _Nonnull responseObject, NSError * _Nonnull error) {
        block(responseObject,error);
    }];
}
//第二步获取到名字申请join
- (void)joinHttpServer:(NSString*)url
{
    UIDevice *currentDevice = [UIDevice currentDevice];
    NSString *deviceName = currentDevice.name;
    NSDictionary* dic = @{@"deviceName":deviceName};
    AppDelegate* app = APPDELEGATE;
    [SVProgressHUD show];
    [[httpNework shareNetwork]sendGETRequestWithURL:[NSString stringWithFormat:@"%@/joinServer",url] parameters:dic completed:^(id  _Nonnull responseObject, NSError * _Nonnull error) {
        if(!error)
        {
            [SVProgressHUD showInfoWithStatus:local(@"请求已经发送请等待回复")];
        }
        else
        {
            [SVProgressHUD showInfoWithStatus:local(@"请求失败")];
//            [self.captureSession startRunning];
            if([self.delegate respondsToSelector:@selector(failQRcode)])
            {
                [self.delegate failQRcode];
            }
        }
    }];
}
//第三步收到返回的通知
- (void)receiveHttpJoin:(NSNotification*)noti
{
    NSDictionary* dic = noti.object;
    if(![dic[@"status"] boolValue])
    {
        [SVProgressHUD showInfoWithStatus:[NSString stringWithFormat:local(@"%@拒绝您的请求"),dic[@"deviceName"]]];
//        [self.captureSession startRunning];
        if([self.delegate respondsToSelector:@selector(failQRcode)])
        {
            [self.delegate failQRcode];
        }
    }
    else
    {
        [SVProgressHUD showInfoWithStatus:[NSString stringWithFormat:local(@"%@接收您的请求"),dic[@"deviceName"]]];
        AppDelegate* app = APPDELEGATE;
        app.currentLinkUrl = dic[@"url"];
        app.currentLinkToken =  dic[@"authorizationToken"];
        app.currentDeviceName  = dic[@"deviceName"];
        [app.whiteList addObject:app.currentLinkToken];
        
        [MobClick event:@"Connect" attributes:@{@"Click_Connect":@"连接成功"}];
        dispatch_async(dispatch_get_main_queue(), ^{
            // 在主线程上执行界面操作
            // 您的布局引擎修改代码应该放在这里
//            FileDateViewController* file = [FileDateViewController new];
//            file.targetName  = app.currentDeviceName;
//            [self presentViewController:file animated:YES completion:^{
//                
//            }];
            [self.delegate connectSuccess];
        });
    }
}


//接收方
//收到加入请求
- (void)receiveJoinHttpServer:(NSNotification*)noti
{
    dispatch_async(dispatch_get_main_queue(), ^{
        NSDictionary* dic = noti.object;
        UIAlertController *alertController = [UIAlertController alertControllerWithTitle:[NSString stringWithFormat:local(@"%@请求加入您的文件传输"),dic[@"deviceName"]] message:nil preferredStyle:UIAlertControllerStyleAlert];
        UIDevice *currentDevice = [UIDevice currentDevice];
        NSString *deviceName = currentDevice.name;
        //增加取消按钮；
        [alertController addAction:[UIAlertAction actionWithTitle:NSLocalizedString(@"接受",nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action){
            NSString* token =  [tokenTool creatToken];
            NSDictionary* dict = @{@"deviceName":deviceName,@"status":@(1),@"authorizationToken":token};
            NSString* url = dic[@"url"];
            [[httpNework shareNetwork]sendGETRequestWithURL:[NSString stringWithFormat:@"%@/receiveJoin",url] parameters:dict completed:^(id  _Nonnull responseObject, NSError * _Nonnull error) {
                AppDelegate* app = APPDELEGATE;
                [app.whiteList addObject:token];
                app.currentLinkUrl = url;
                app.currentDeviceName = dic[@"deviceName"];
                app.currentLinkToken =  token;
                
                dispatch_async(dispatch_get_main_queue(), ^{
                    // 在主线程上执行界面操作
                    // 您的布局引擎修改代码应该放在这里
                    
                });
                 
            }];
        }]];
        [alertController addAction:[UIAlertAction actionWithTitle:NSLocalizedString(@"拒绝",nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action){
            NSDictionary* dict = @{@"deviceName":deviceName,@"status":@(0)};
            NSString* url = dic[@"url"];
            [[httpNework shareNetwork]sendGETRequestWithURL:[NSString stringWithFormat:@"%@/receiveJoin",url] parameters:dict completed:^(id  _Nonnull responseObject, NSError * _Nonnull error) {
                
            }];
            
        }]];
        if([self.delegate isKindOfClass:[UIViewController class]])
        {
            [self.delegate presentViewController:alertController animated:true completion:nil];
        }
    });
    
}



/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */
@end
