//
//  scannner.m
//  WifiTransferFiles
//
//  Created by fs0011 on 2023/11/29.
//  Copyright © 2023 zhangdinghao. All rights reserved.
//


#import "scannner.h"
#import "DHIPAdress.h"

@implementation scannner

- (instancetype)init {
    self = [super init];
    if (self) {
        self.reachableIPs = [NSMutableArray array];
    }
    return self;
}

- (void)scanPort:(NSInteger)port {
    PNetMLanScanner *lanScanner = [PNetMLanScanner shareInstance];
    self.arr = [NSMutableArray array];
    lanScanner.delegate =  self;
    
    self.scanner = lanScanner;
    
    NSString *IPAdress = [DHIPAdress deviceIPAdress];
    NSArray *serveriparr = [IPAdress componentsSeparatedByString:@"."];
    
    
    if (serveriparr.count == 4) {
        NSString *beginStr = [IPAdress stringByReplacingOccurrencesOfString:serveriparr.lastObject withString:@""];
        NSMutableArray *addressIds = [NSMutableArray array];
        for (int i = 0; i <= 255; i++) {
            [addressIds addObject:[beginStr stringByAppendingFormat:@"%d", i]];
        }
        for (NSString* url in addressIds) {
            [self getOtherNames:url compeletBlock:^(id  _Nonnull responseObject, NSError * _Nonnull error) {
                if(!error)
                {
                    NSLog(@"找到了%@",responseObject[@"data"][@"url"]);
                    if(![responseObject[@"data"][@"url"] isEqualToString:IPAdress]&&![responseObject[@"data"][@"url"]  isKindOfClass:[NSNull class]]&&responseObject[@"data"][@"url"])
                    {
                        [self.reachableIPs addObject:responseObject[@"data"][@"url"]];
                        self.compelet();
                    }
                }
            }];
        }
    }
}



- (void)getOtherNames:(NSString*)url  compeletBlock:(CompletionHandler)block
{
    
    [[httpNework shareNetwork]sendGETRequestWithURL:[NSString stringWithFormat:@"http://%@:8888/getServerName",url] parameters:nil completed:^(id  _Nonnull responseObject, NSError * _Nonnull error) {
        block(responseObject,error);
    }];
}
- (void) scanMLan:(PNetMLanScanner *)scanner activeIp:(NSString *)ip
{
    [self.arr addObject:ip];
    

}
- (void)scanMlan:(PNetMLanScanner *)scanner percent:(float)percent
{
    NSLog(@"IPpercent = %lf",percent);
}
- (void)finishedScanMlan:(PNetMLanScanner *)scanner
{
    
    
//    NSString *IPAdress = [DHIPAdress deviceIPAdress];
//       NSArray *serveriparr = [IPAdress componentsSeparatedByString:@"."];
//    if (serveriparr.count == 4) {
//        NSString *beginStr = [IPAdress stringByReplacingOccurrencesOfString:serveriparr.lastObject withString:@""];
//        NSMutableArray *addressIds = [NSMutableArray array];
//        for (int i = 0; i <= 255; i++) {
//            [addressIds addObject:[beginStr stringByAppendingFormat:@"%d", i]];
//        }
//        
//        
//        
//        
//        
//        for (NSString* idStr in addressIds) {
//            [[PhoneNetManager shareInstance] netPortScan:idStr beginPort:8888 endPort:8888 completeHandler:^(NSString * _Nullable port, BOOL isOpen, PNError * _Nullable sdkError) {
//                // your processing logic
//                NSLog(@"%@ %d",idStr,isOpen);
//            }];
//        }
//    }
}
@end
