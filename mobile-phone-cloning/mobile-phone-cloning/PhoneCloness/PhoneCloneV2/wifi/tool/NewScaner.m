//
//  NewScaner.m
//  mobiletransfer
//
//  Created by fs0011 on 2023/12/4.
//  Copyright © 2023 <PERSON><PERSON><PERSON>. All rights reserved.
//

#import "NewScaner.h"
#import <CocoaAsyncSocket/GCDAsyncUdpSocket.h>
@interface NewScaner () <GCDAsyncUdpSocketDelegate>
@property (strong, nonatomic) GCDAsyncUdpSocket *udpSocket;
@end

@implementation NewScaner
- (void)setupAndStartBroadcast {
    
    
    self.udpSocket = [[GCDAsyncUdpSocket alloc] initWithDelegate:self delegateQueue:dispatch_get_main_queue()];
    
    NSError *error = nil;
    if (![self.udpSocket enableBroadcast:YES error:&error]) {
        NSLog(@"%@",error);
            return;
    }
    
    
    if (![self.udpSocket bindToPort:7777 error:&error]) {
        NSLog(@"Error binding: %@", error);
        return;
    }
    
    if (![self.udpSocket enableBroadcast:YES error:&error]) {
        NSLog(@"Error enabling broadcast: %@", error);
        return;
    }

    if (![self.udpSocket beginReceiving:&error]) {
        NSLog(@"Error starting to receive: %@", error);
        return;
    }
    
    NSData *data = [@"Your broadcast message" dataUsingEncoding:NSUTF8StringEncoding];
    [self.udpSocket sendData:data toHost:@"***************" port:7777 withTimeout:-1 tag:0];
}
- (void)udpSocket:(GCDAsyncUdpSocket *)sock didSendDataWithTag:(long)tag
{
    NSLog(@"发送信息成功");
}

- (void)udpSocket:(GCDAsyncUdpSocket *)sock didNotSendDataWithTag:(long)tag dueToError:(NSError *)error
{
    NSLog(@"发送信息失败");
}
// GCDAsyncUdpSocketDelegate 方法
- (void)udpSocket:(GCDAsyncUdpSocket *)sock didReceiveData:(NSData *)data fromAddress:(NSData *)address withFilterContext:(id)filterContext {
    NSString *msg = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    if (msg) {
        NSString *host = nil;
        uint16_t port = 0;
        [GCDAsyncUdpSocket getHost:&host port:&port fromAddress:address];
        NSLog(@"收到来自 %@:%hu 的消息: %@", host, port, msg);
    }
}

@end
