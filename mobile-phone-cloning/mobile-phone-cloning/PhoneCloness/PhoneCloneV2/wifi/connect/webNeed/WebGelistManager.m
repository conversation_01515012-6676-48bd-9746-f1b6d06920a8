//
//  WebGelistManager.m
//  mobiletransfer
//
//  Created by fs0011 on 2023/12/22.
//  Copyright © 2023 Qizhang Chen. All rights reserved.
//

#import "WebGelistManager.h"

#import <MediaPlayer/MediaPlayer.h>
#import <Photos/Photos.h>
@implementation WebGelistManager
- (NSArray*)getAllLbumsl
{
    
    PHFetchOptions *options = [[PHFetchOptions alloc] init];
    NSMutableArray *allAlbumArray = [NSMutableArray array];

    // 修复新版本iOS兼容性问题：创建一个虚拟的"所有照片"相册
    // 直接获取所有照片，不依赖智能相册
    PHFetchOptions *allPhotosOptions = [[PHFetchOptions alloc] init];
    allPhotosOptions.predicate = [NSPredicate predicateWithFormat:@"mediaType == %d", PHAssetMediaTypeImage];
    allPhotosOptions.sortDescriptors = @[[NSSortDescriptor sortDescriptorWithKey:@"creationDate" ascending:NO]];

    PHFetchResult *allPhotosResult = [PHAsset fetchAssetsWithOptions:allPhotosOptions];

    // 创建一个虚拟相册来包含所有照片
    PHAssetCollection *cameraRoll = nil;
    if (allPhotosResult.count > 0) {
        // 尝试获取用户相册，如果失败则使用nil（我们会直接使用allPhotosResult）
        @try {
            PHFetchResult *userAlbums = [PHAssetCollection fetchAssetCollectionsWithType:PHAssetCollectionTypeAlbum subtype:PHAssetCollectionSubtypeAny options:nil];
            if (userAlbums.count > 0) {
                cameraRoll = userAlbums.firstObject; // 使用第一个用户相册作为占位符
            }
        } @catch (NSException *exception) {
            NSLog(@"获取用户相册失败: %@", exception);
        }
    }
    NSMutableArray* resultArry = [NSMutableArray array];

    // 首先添加"所有照片"虚拟相册
    if (allPhotosResult.count > 0) {
        NSMutableDictionary *allPhotosInfo = [NSMutableDictionary dictionary];
        allPhotosInfo[@"title"] = @"所有照片";
        allPhotosInfo[@"photoCount"] = @(allPhotosResult.count);
        allPhotosInfo[@"albumID"] = @"ALL_PHOTOS"; // 使用特殊标识符
        PHAsset *firstAsset = allPhotosResult.firstObject;
        allPhotosInfo[@"localID"] = firstAsset.localIdentifier;
        [resultArry addObject:allPhotosInfo];
    }

    // 然后添加用户创建的相册
    @try {
        PHFetchResult *albums = [PHAssetCollection fetchAssetCollectionsWithType:PHAssetCollectionTypeAlbum subtype:PHAssetCollectionSubtypeAlbumRegular options:options];
        for (PHAssetCollection *collection in albums) {
            PHFetchOptions *assetOptions = [[PHFetchOptions alloc] init];
            assetOptions.predicate = [NSPredicate predicateWithFormat:@"mediaType == %d", PHAssetMediaTypeImage];
            PHFetchResult *assets = [PHAsset fetchAssetsInAssetCollection:collection options:assetOptions];

            if (assets.count > 0) { // 只添加包含照片的相册
                NSMutableDictionary *info = [NSMutableDictionary dictionary];
                info[@"title"] = collection.localizedTitle ?: @"未命名相册";
                info[@"photoCount"] = @(assets.count);
                info[@"albumID"] = collection.localIdentifier;
                PHAsset *firstAsset = assets.firstObject;
                info[@"localID"] = firstAsset.localIdentifier;
                [resultArry addObject:info];
            }
        }
    } @catch (NSException *exception) {
        NSLog(@"获取用户相册失败: %@", exception);
    }
    return resultArry;
}

- (NSMutableArray*)fetchPhotosInAlbum:(NSString *)albumID forPage:(NSInteger)page {
    NSInteger photosPerPage = 50;
    NSInteger startIndex = (page-1) * photosPerPage;
    NSInteger endIndex = startIndex + photosPerPage;

    PHFetchResult *result = nil;

    if ([albumID isEqualToString:@"ALL_PHOTOS"]) {
        // 处理"所有照片"的特殊情况
        PHFetchOptions *options = [[PHFetchOptions alloc] init];
        options.predicate = [NSPredicate predicateWithFormat:@"mediaType == %d", PHAssetMediaTypeImage];
        options.sortDescriptors = @[[NSSortDescriptor sortDescriptorWithKey:@"creationDate" ascending:NO]];
        result = [PHAsset fetchAssetsWithOptions:options];
    } else {
        // 处理普通相册
        PHAssetCollection *collection = [PHAssetCollection fetchAssetCollectionsWithLocalIdentifiers:@[albumID] options:nil].firstObject;
        if (collection) {
            PHFetchOptions *options = [[PHFetchOptions alloc] init];
            options.predicate = [NSPredicate predicateWithFormat:@"mediaType == %d", PHAssetMediaTypeImage];
            options.sortDescriptors = @[[NSSortDescriptor sortDescriptorWithKey:@"creationDate" ascending:NO]];
            result = [PHAsset fetchAssetsInAssetCollection:collection options:options];
        }
    }

       NSMutableArray *photos = [NSMutableArray array];
    
    int idx = 0;
    for (PHAsset * asset in result) {
        if (idx >= startIndex && idx < endIndex) {
            NSMutableDictionary* dic = [NSMutableDictionary dictionary];
            dic[@"localID"] = asset.localIdentifier;
            [photos addObject:dic];
        }
        if (idx >= endIndex) {
            break;
        }
        idx++;
    }
       

    return photos;
}

- (NSMutableArray *)fetchVideosForPage:(NSInteger)page {
    NSInteger videosPerPage = 50;
    NSInteger startIndex = (page-1) * videosPerPage;
    NSInteger endIndex = startIndex + videosPerPage;

    PHFetchOptions *options = [[PHFetchOptions alloc] init];
    options.predicate = [NSPredicate predicateWithFormat:@"mediaType == %d", PHAssetMediaTypeVideo];
    options.sortDescriptors = @[[NSSortDescriptor sortDescriptorWithKey:@"creationDate" ascending:YES]];

    PHFetchResult *allVideos = [PHAsset fetchAssetsWithOptions:options];

    NSMutableArray *videoIdentifiers = [NSMutableArray array];
    NSInteger currentIndex = 0;
    for (PHAsset *asset in allVideos) {
        if (currentIndex >= startIndex && currentIndex < endIndex) {
            NSMutableDictionary* dic = [NSMutableDictionary dictionary];
            dic[@"localID"] = asset.localIdentifier;
            [videoIdentifiers addObject:dic];
        }
        if (currentIndex >= endIndex) {
            break;
        }
        currentIndex++;
    }

    return videoIdentifiers;
}


- (NSMutableArray*)fetchMusicLibrary {
    NSMutableArray *musicArray = [NSMutableArray array];

    
       
            MPMediaQuery *query = [MPMediaQuery songsQuery];
            NSArray *songs = [query items];
            for (MPMediaItem *song in songs) {
                NSMutableDictionary *musicInfo = [NSMutableDictionary dictionary];
                musicInfo[@"title"] = [song valueForProperty:MPMediaItemPropertyTitle];
                musicInfo[@"artist"] = [song valueForProperty:MPMediaItemPropertyArtist];
                musicInfo[@"albumName"] = [song valueForProperty:MPMediaItemPropertyAlbumTitle];
                musicInfo[@"localID"]= [[song valueForProperty:MPMediaItemPropertyPersistentID] stringValue];

                [musicArray addObject:musicInfo];
            }
       


    return musicArray;
}



- (void)fetchPhotoDataWithLocalID:(NSString *)localID completion:(void (^)(NSData *photoData))completion {
    PHFetchResult *result = [PHAsset fetchAssetsWithLocalIdentifiers:@[localID] options:nil];

    if (result.count > 0) {
        PHAsset *asset = [result firstObject];

        PHImageRequestOptions *options = [[PHImageRequestOptions alloc] init];
        options.synchronous = YES;

        [[PHImageManager defaultManager] requestImageDataForAsset:asset options:options resultHandler:^(NSData *imageData, NSString *dataUTI, UIImageOrientation orientation, NSDictionary *info) {
            completion(imageData);
        }];
    } else {
        completion(nil); // 未找到照片
    }
}

- (void)fetchThumbnailImageDataForAssetWithLocalID:(NSString *)localID completion:(void (^)(NSData *thumbnailImageData))completion {
    PHFetchResult *result = [PHAsset fetchAssetsWithLocalIdentifiers:@[localID] options:nil];

    if (result.count > 0) {
        PHAsset *asset = [result firstObject];
        
        // 指定缩略图的尺寸
        CGSize thumbnailSize = CGSizeMake(100, 100); // 根据需要设置缩略图的尺寸
        
        PHImageRequestOptions *options = [[PHImageRequestOptions alloc] init];
        options.resizeMode = PHImageRequestOptionsResizeModeExact;
        options.deliveryMode = PHImageRequestOptionsDeliveryModeFastFormat;

        [[PHImageManager defaultManager] requestImageForAsset:asset
                                                   targetSize:thumbnailSize
                                                  contentMode:PHImageContentModeAspectFit
                                                      options:options
                                                resultHandler:^(UIImage * _Nullable result, NSDictionary * _Nullable info) {
            if (result) {
                NSData *thumbnailImageData = UIImageJPEGRepresentation(result, 1.0);
                completion(thumbnailImageData);
            } else {
                completion(nil);
            }
        }];
    } else {
        completion(nil); // 未找到图片
    }
}

- (void)fetchVideoDataWithLocalID:(NSString *)localID completion:(void (^)(NSData *videoData))completion {
    PHFetchResult *result = [PHAsset fetchAssetsWithLocalIdentifiers:@[localID] options:nil];

    if (result.count > 0) {
        PHAsset *asset = [result firstObject];

        PHVideoRequestOptions *options = [[PHVideoRequestOptions alloc] init];
        options.version = PHVideoRequestOptionsVersionOriginal;

        [[PHImageManager defaultManager] requestAVAssetForVideo:asset options:options resultHandler:^(AVAsset *avAsset, AVAudioMix *audioMix, NSDictionary *info) {
            if ([avAsset isKindOfClass:[AVURLAsset class]]) {
                AVURLAsset *urlAsset = (AVURLAsset *)avAsset;
                NSData *videoData = [NSData dataWithContentsOfURL:urlAsset.URL];
                completion(videoData);
            }
        }];
    } else {
        completion(nil); // 未找到视频
    }
}

- (void)fetchThumbnailImageDataForVideoWithLocalID:(NSString *)localID completion:(void (^)(NSData *thumbnailImageData))completion {
    PHFetchResult *result = [PHAsset fetchAssetsWithLocalIdentifiers:@[localID] options:nil];

        if (result.count > 0) {
            PHAsset *asset = [result firstObject];
            
            // 创建 AVAsset 对象
            PHVideoRequestOptions *videoOptions = [[PHVideoRequestOptions alloc] init];
            videoOptions.version = PHVideoRequestOptionsVersionCurrent;

            [[PHImageManager defaultManager] requestAVAssetForVideo:asset options:videoOptions resultHandler:^(AVAsset * _Nullable avAsset, AVAudioMix * _Nullable audioMix, NSDictionary * _Nullable info) {
                if (avAsset && [avAsset isKindOfClass:[AVURLAsset class]]) {
                    AVURLAsset *urlAsset = (AVURLAsset *)avAsset;
                    AVAssetImageGenerator *imageGenerator = [AVAssetImageGenerator assetImageGeneratorWithAsset:urlAsset];
                    imageGenerator.appliesPreferredTrackTransform = YES;
                    
                    // 获取第一帧截图
                    NSError *error;
                    CMTime time = CMTimeMakeWithSeconds(0.0, 1); // 获取第0秒的截图
                    CGImageRef cgImage = [imageGenerator copyCGImageAtTime:time actualTime:NULL error:&error];
                    
                    if (cgImage) {
                        UIImage *thumbnailImage = [UIImage imageWithCGImage:cgImage];
                        NSData *thumbnailImageData = UIImageJPEGRepresentation(thumbnailImage, 1.0);
                        completion(thumbnailImageData);
                        CGImageRelease(cgImage);
                    } else {
                        completion(nil);
                    }
                } else {
                    completion(nil);
                }
            }];
        } else {
            completion(nil); // 未找到视频
        }
}

- (void)fetchMusicDataWithLocalID:(NSString *)localID completion:(void (^)(NSData *musicData))completion {
    MPMediaPropertyPredicate *predicate = [MPMediaPropertyPredicate predicateWithValue:localID forProperty:MPMediaItemPropertyPersistentID];
    MPMediaQuery *query = [[MPMediaQuery alloc] init];
    [query addFilterPredicate:predicate];

    NSArray *items = [query items];

    if (items.count > 0) {
        MPMediaItem *mediaItem = [items firstObject];
        NSLog(@"%d",mediaItem.hasProtectedAsset);
        NSURL *musicURL = [mediaItem valueForProperty:MPMediaItemPropertyAssetURL];
        NSData *musicData = [NSData dataWithContentsOfURL:musicURL];
        
        NSURL *url = [mediaItem valueForProperty:MPMediaItemPropertyAssetURL];
        AVURLAsset *songAsset = [AVURLAsset URLAssetWithURL:url options:nil];

        AVAssetExportSession *exporter = [[AVAssetExportSession alloc] initWithAsset:songAsset presetName:AVAssetExportPresetAppleM4A];
        exporter.outputFileType = AVFileTypeAppleM4A;

        // 定义导出文件的路径
        NSString *documentsDirectory = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) firstObject];
        NSString *exportFileName = [NSString stringWithFormat:@"exportedFile.m4a"];
        NSString *exportPath = [documentsDirectory stringByAppendingPathComponent:exportFileName];

        // 使用 NSFileManager 来处理文件存在性
        NSFileManager *fileManager = [NSFileManager defaultManager];
        if ([fileManager fileExistsAtPath:exportPath]) {
            NSError *removeError;
            [fileManager removeItemAtPath:exportPath error:&removeError];
            if (removeError) {
                NSLog(@"Unable to remove file at path %@: %@", exportPath, removeError);
                return;
            }
        }

        exporter.outputURL = [NSURL fileURLWithPath:exportPath];

        [exporter exportAsynchronouslyWithCompletionHandler:^{
            // 检查导出状态
            if (exporter.status == AVAssetExportSessionStatusCompleted) {
                NSLog(@"Export completed: %@", exportPath);
                NSData* data = [NSData dataWithContentsOfFile:exportPath];
                completion(data);
                // 这里可以处理导出后的操作
            } else {
                NSLog(@"Export failed with error: %@", exporter.error.localizedDescription);
            }
        }];
        
        
    } else {
        completion(nil); // 未找到音乐
    }
}





// 递归搜索文件的方法
- (void)searchFilesInDirectory:(NSString *)directoryPath completion:(void (^)(NSArray<NSDictionary *> *matchedFiles))completion {
    // 创建用于存储结果的数组
    NSMutableArray<NSDictionary *> *matchedFiles = [NSMutableArray array];

    // 在一个私有队列中递归搜索以避免阻塞主线程
    dispatch_queue_t queue = dispatch_queue_create("fileSearchQueue", DISPATCH_QUEUE_SERIAL);

    // 在队列中异步执行搜索
    dispatch_async(queue, ^{
        [self recursiveSearchFilesInDirectory:directoryPath matchedFiles:matchedFiles];

        // 在递归完成后回到主队列执行完成回调
        dispatch_async(dispatch_get_main_queue(), ^{
            if (completion) {
                completion([matchedFiles copy]);
            }
        });
    });
}

- (void)recursiveSearchFilesInDirectory:(NSString *)directoryPath matchedFiles:(NSMutableArray<NSDictionary *> *)matchedFiles {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSArray<NSString *> *commonDocumentExtensions = @[@"txt", @"pdf", @"doc", @"docx", @"xls", @"xlsx", @"ppt", @"pptx", @"json"];

    // 获取目录下的所有文件和子目录
    NSArray<NSString *> *contents = [fileManager contentsOfDirectoryAtPath:directoryPath error:nil];

    for (NSString *item in contents) {
        NSString *itemPath = [directoryPath stringByAppendingPathComponent:item];
        BOOL isDirectory;

        // 检查是否为文件夹
        if ([fileManager fileExistsAtPath:itemPath isDirectory:&isDirectory]) {
            if (isDirectory) {
                // 如果是文件夹，递归搜索子文件夹
                [self recursiveSearchFilesInDirectory:itemPath matchedFiles:matchedFiles];
            } else {
                // 如果是文件，检查文件后缀是否在常见文档后缀列表中
                NSString *fileExtension = itemPath.pathExtension.lowercaseString;
                if ([commonDocumentExtensions containsObject:fileExtension]) {
                    // 添加文档相对于文档目录的路径
                    NSString *relativePath = [itemPath stringByReplacingOccurrencesOfString:NSHomeDirectory() withString:@""];
                    NSDictionary* fileDict = @{@"path": relativePath};
                    @synchronized(matchedFiles) {
                        [matchedFiles addObject:fileDict];
                    }
                }
            }
        }
    }
}
@end
