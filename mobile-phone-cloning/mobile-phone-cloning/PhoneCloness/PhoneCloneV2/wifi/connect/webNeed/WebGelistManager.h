//
//  WebGelistManager.h
//  mobiletransfer
//
//  Created by fs0011 on 2023/12/22.
//  Copyright © 2023 <PERSON><PERSON><PERSON>. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN


@interface WebGelistManager : NSObject

@property (nonatomic, strong) NSMutableArray<NSDictionary *> *matchedFiles;


- (NSArray*)getAllLbumsl;
- (NSMutableArray*)fetchPhotosInAlbum:(NSString *)albumID forPage:(NSInteger)page;
- (NSMutableArray *)fetchVideosForPage:(NSInteger)page;
- (NSMutableArray*)fetchMusicLibrary;

- (void)fetchPhotoDataWithLocalID:(NSString *)localID completion:(void (^)(NSData *photoData))completion;
- (void)fetchThumbnailImageDataForAssetWithLocalID:(NSString *)localID completion:(void (^)(NSData *thumbnailImageData))completion;
- (void)fetchVideoDataWithLocalID:(NSString *)localID completion:(void (^)(NSData *videoData))completion;
- (void)fetchThumbnailImageDataForVideoWithLocalID:(NSString *)localID completion:(void (^)(NSData *thumbnailImageData))completion;
- (void)fetchMusicDataWithLocalID:(NSString *)localID completion:(void (^)(NSData *musicData))completion;


- (void)searchFilesInDirectory:(NSString *)directoryPath completion:(void (^)(NSArray<NSDictionary *> *matchedFiles))completion;

@end

NS_ASSUME_NONNULL_END
