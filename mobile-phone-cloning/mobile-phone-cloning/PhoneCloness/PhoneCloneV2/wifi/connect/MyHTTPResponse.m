//
//  MyHTTPResponse.m
//  mobiletransfer
//
//  Created by fs0011 on 2023/12/29.
//  Copyright © 2023 <PERSON><PERSON><PERSON>. All rights reserved.
//

#import "MyHTTPResponse.h"

@implementation MyHTTPResponse
- (NSDictionary *)httpHeaders {
    // 允许跨域处理
    NSMutableDictionary* dic = [NSMutableDictionary dictionary];
    [dic setObject:@"*" forKey:@"Access-Control-Allow-Origin"];
    [dic setObject:@"Origin, X-Api-Key, X-Requested-With, Content-Type, Accept, Token" forKey:@"Access-Control-Allow-Headers"];
    [dic setObject:@"POST, GET, PUT, OPTIONS" forKey:@"Access-Control-Allow-Methods"];
    return [dic copy];
}

@end
