//
//  MyHTTPConnection.m
//  iRead
//
//  Created by 张丁豪 on 2017/5/5.
//  Copyright © 2017年 zhangdinghao. All rights reserved.
//


#import "MyHTTPConnection.h"
#import "HTTPMessage.h"
#import "HTTPDataResponse.h"
#import "HTTPErrorResponse.h"
#import "DDNumber.h"
#import "HTTPLogging.h"

#import "MultipartFormDataParser.h"
#import "MultipartMessageHeaderField.h"
#import "HTTPDynamicFileResponse.h"
#import "HTTPFileResponse.h"
#import <UIKit/UIKit.h>
#import "GCDAsyncSocket.h"
#import "WebGelistManager.h"
#import "MyCustomDataResponse.h"
#import "MyHTTPResponse.h"
#import "WebGelistManager.h"
// Log levels : off, error, warn, info, verbose
// Other flags: trace
static const int httpLogLevel = HTTP_LOG_LEVEL_VERBOSE; // | HTTP_LOG_FLAG_TRACE;


/**
 * All we have to do is override appropriate methods in HTTPConnection.
 **/

@implementation MyHTTPConnection

- (BOOL)supportsMethod:(NSString *)method atPath:(NSString *)path
{
    HTTPLogTrace();
    
    // Add support for POST
    
    if ([method isEqualToString:@"POST"])
    {
        if ([path containsString:@"/upload"])
        {
            return YES;
        }
    }
    if ([method isEqualToString:@"get"]) {
        if([path isEqualToString:@"getServerName"]||[path isEqualToString:@"joinServer"]||[path isEqualToString:@"receiveJoin"]||[path isEqualToString:@"heart"])
        {
            return YES;
        }
    }
    return [super supportsMethod:method atPath:path];
}

- (BOOL)expectsRequestBodyFromMethod:(NSString *)method atPath:(NSString *)path
{
    HTTPLogTrace();
    
    // Inform HTTP server that we expect a body to accompany a POST request
    
    if([method isEqualToString:@"POST"] && [path containsString:@"/upload"]) {
        // here we need to make sure, boundary is set in header
        NSString* contentType = [request headerField:@"Content-Type"];
        NSUInteger paramsSeparator = [contentType rangeOfString:@";"].location;
        if( NSNotFound == paramsSeparator ) {
            return NO;
        }
        if( paramsSeparator >= contentType.length - 1 ) {
            return NO;
        }
        NSString* type = [contentType substringToIndex:paramsSeparator];
        if( ![type isEqualToString:@"multipart/form-data"] ) {
            // we expect multipart/form-data content type
            return NO;
        }
        
        // enumerate all params in content-type, and find boundary there
        NSArray* params = [[contentType substringFromIndex:paramsSeparator + 1] componentsSeparatedByString:@";"];
        for( NSString* param in params ) {
            paramsSeparator = [param rangeOfString:@"="].location;
            if( (NSNotFound == paramsSeparator) || paramsSeparator >= param.length - 1 ) {
                continue;
            }
            NSString* paramName = [param substringWithRange:NSMakeRange(1, paramsSeparator-1)];
            NSString* paramValue = [param substringFromIndex:paramsSeparator+1];
            
            if( [paramName isEqualToString: @"boundary"] ) {
                // let's separate the boundary from content-type, to make it more handy to handle
                [request setHeaderField:@"boundary" value:paramValue];
            }
        }
        // check if boundary specified
        if( nil == [request headerField:@"boundary"] )  {
            return NO;
        }
        return YES;
    }
    return [super expectsRequestBodyFromMethod:method atPath:path];
}
static NSMutableDictionary*  data;
- (NSObject<HTTPResponse> *)httpResponseForMethod:(NSString *)method URI:(NSString *)path
{
    HTTPLogTrace();
    
    NSString *ipAddress = [asyncSocket connectedHost];
    NSLog(@"Client IP Address: %@", ipAddress);
    if ([method isEqualToString:@"OPTIONS"]) {
           // 创建一个HTTP响应，状态码为200
           HTTPMessage *response = [[HTTPMessage alloc] initResponseWithStatusCode:200 description:nil version:HTTPVersion1_1];
           
           // 添加CORS响应头
           [response setHeaderField:@"Access-Control-Allow-Origin" value:@"*"]; // 允许所有来源
           [response setHeaderField:@"Access-Control-Allow-Methods" value:@"GET, POST, PUT, DELETE, OPTIONS"]; // 允许的HTTP方法
           [response setHeaderField:@"Access-Control-Allow-Headers" value:@"Content-Type, X-Requested-With"]; // 允许的请求头
           [response setHeaderField:@"Access-Control-Max-Age" value:@"86400"]; // 预检请求的缓存时间（秒）
        [response setHeaderField:@"Access-Control-Allow-Private-Network" value:@"true"];
           
           // 将HTTPMessage转换为NSData，并返回一个HTTPDataResponse对象
           NSData *responseData = [response messageData];
           return [[HTTPDataResponse alloc] initWithData:responseData];
       }
    
    
    if ([method isEqualToString:@"POST"] && [path containsString:@"/upload"])
    {
        
        // this method will generate response with links to uploaded file
        NSMutableString* filesStr = [[NSMutableString alloc] init];
        NSString *postBody = [[NSString alloc] initWithData:request.messageData encoding:NSUTF8StringEncoding];
        // 获取请求的参数
        
        for( NSString* filePath in uploadedFiles ) {
            //generate links
            [filesStr appendFormat:@"<a href=\"%@\"> %@ </a><br/>",filePath, [filePath lastPathComponent]];
        }
        NSString* templatePath = [[config documentRoot] stringByAppendingPathComponent:@"upload"];
        NSDictionary* replacementDict = [NSDictionary dictionaryWithObject:filesStr forKey:@"MyFiles"];
        // use dynamic file response to apply our links to response template
        //        return [[HTTPDynamicFileResponse alloc] initWithFilePath:templatePath forConnection:self separator:@"%" replacementDictionary:replacementDict];
        
        //        NSLog(@"Device Name: %@", deviceName);
        NSString *headerValue = [request headerField:@"authorizationToken"];
        AppDelegate* app = APPDELEGATE;
//        if([app.whiteList containsObject:headerValue])
//        {
            NSURL *url = [NSURL URLWithString:path];
            NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
            NSArray<NSURLQueryItem *> *queryItems = components.queryItems;
            
            // 用于存储解析后的参数
            NSMutableDictionary *params = [NSMutableDictionary dictionary];
            
            
            for (NSURLQueryItem *item in queryItems) {
                params[item.name] = item.value;
            }
            NSDictionary* dic = @{@"data":@{}};
            NSError *error;
            NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dic options:0 error:&error];
            if (!jsonData) {
                // 处理错误
                NSLog(@"Error converting dictionary to JSON: %@", error);
                return nil; // 或者返回一个错误响应
            } else {
                // 创建HTTPDataResponse对象并返回
                if(data)
                {
                    data[@"deviceName"] = [request headerField:@"deviceName"];
                    data[@"fileType"] = [request headerField:@"fileType"];
                    
                }
                
                return [[MyHTTPResponse alloc] initWithData:jsonData];
            }
//        }
//        else
//        {
//            [self stop];
//            return [[HTTPErrorResponse alloc] initWithErrorCode:400];
//        }
        
    }
    if( [method isEqualToString:@"GET"] && [path hasPrefix:@"/upload/"] ) {
        // let download the uploaded files
        return [[HTTPFileResponse alloc] initWithFilePath: [[config documentRoot] stringByAppendingString:path] forConnection:self];
    }
    if([method isEqualToString:@"GET"] && [path hasPrefix:@"/getServerName"])
    {
        
        UIDevice *currentDevice = [UIDevice currentDevice];
        NSString *deviceName = currentDevice.name;
        
        NSLog(@"Device Name: %@", deviceName);
        NSDictionary* dic = @{@"data":@{@"deviceName":deviceName,@"url":[DHIPAdress deviceIPAdress]}};
        NSError *error;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dic options:0 error:&error];
        
        if (!jsonData) {
            // 处理错误
            NSLog(@"Error converting dictionary to JSON: %@", error);
            return nil; // 或者返回一个错误响应
        } else {
            // 创建HTTPDataResponse对象并返回
            return [[HTTPDataResponse alloc] initWithData:jsonData];
        }
    }
    if([method isEqualToString:@"GET"] && [path hasPrefix:@"/joinServer"])
    {
        NSURL *url = [NSURL URLWithString:path];
        NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
        NSArray<NSURLQueryItem *> *queryItems = components.queryItems;
        
        // 用于存储解析后的参数
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        for (NSURLQueryItem *item in queryItems) {
            params[item.name] = item.value;
        }
        params[@"url"] = [NSString stringWithFormat:@"http://%@:8888",ipAddress];
        if(params[@"deviceName"])
        {
            NSLog(@"name = %@",params[@"deviceName"]);
            [[NSNotificationCenter defaultCenter]postNotificationName:joinServer object:params];
        }
        UIDevice *currentDevice = [UIDevice currentDevice];
        NSString *deviceName = currentDevice.name;
        
        NSLog(@"Device Name: %@", deviceName);
        NSDictionary* dic = @{@"data":@{}};
        NSError *error;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dic options:0 error:&error];
        
        if (!jsonData) {
            // 处理错误
            NSLog(@"Error converting dictionary to JSON: %@", error);
            return nil; // 或者返回一个错误响应
        } else {
            // 创建HTTPDataResponse对象并返回
            return [[HTTPDataResponse alloc] initWithData:jsonData];
        }
    }
    if ([method isEqualToString:@"GET"] && [path hasPrefix:@"/receiveJoin"]) {
        NSURL *url = [NSURL URLWithString:path];
        NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
        NSArray<NSURLQueryItem *> *queryItems = components.queryItems;
        
        // 用于存储解析后的参数
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        for (NSURLQueryItem *item in queryItems) {
            params[item.name] = item.value;
        }
        params[@"url"] = [NSString stringWithFormat:@"%@:8888",ipAddress];
        if(params[@"status"])
        {
            NSLog(@"status = %@",params[@"status"]);
            [[NSNotificationCenter defaultCenter]postNotificationName:receiveJoin object:params];
        }
        UIDevice *currentDevice = [UIDevice currentDevice];
        NSString *deviceName = currentDevice.name;
        
        NSLog(@"Device Name: %@", deviceName);
        NSDictionary* dic = @{@"data":@{}};
        NSError *error;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dic options:0 error:&error];
        
        if (!jsonData) {
            // 处理错误
            NSLog(@"Error converting dictionary to JSON: %@", error);
            return nil; // 或者返回一个错误响应
        } else {
            // 创建HTTPDataResponse对象并返回
            return [[HTTPDataResponse alloc] initWithData:jsonData];
        }
    }
    if ([method isEqualToString:@"GET"] && [path hasPrefix:@"/receiveJoin"]) {
        NSURL *url = [NSURL URLWithString:path];
        NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
        NSArray<NSURLQueryItem *> *queryItems = components.queryItems;
        
        // 用于存储解析后的参数
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        for (NSURLQueryItem *item in queryItems) {
            params[item.name] = item.value;
        }
        params[@"url"] = [NSString stringWithFormat:@"%@:8888",ipAddress];
        if(params[@"status"])
        {
            NSLog(@"status = %@",params[@"status"]);
            [[NSNotificationCenter defaultCenter]postNotificationName:receiveJoin object:params];
        }
        UIDevice *currentDevice = [UIDevice currentDevice];
        NSString *deviceName = currentDevice.name;
        
        NSLog(@"Device Name: %@", deviceName);
        NSDictionary* dic = @{@"data":@{}};
        NSError *error;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dic options:0 error:&error];
        
        if (!jsonData) {
            // 处理错误
            NSLog(@"Error converting dictionary to JSON: %@", error);
            return nil; // 或者返回一个错误响应
        } else {
            // 创建HTTPDataResponse对象并返回
            return [[HTTPDataResponse alloc] initWithData:jsonData];
        }
    }
    if ( [path hasPrefix:@"/albums"]&&![path containsString:@"/photos"])
    {
        NSURL *url = [NSURL URLWithString:path];
        NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
        NSArray<NSURLQueryItem *> *queryItems = components.queryItems;
        
        // 用于存储解析后的参数
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        for (NSURLQueryItem *item in queryItems) {
            params[item.name] = item.value;
        }
        WebGelistManager * manager = [WebGelistManager new];
        NSArray* arr = [manager getAllLbumsl];
        NSDictionary* dic = @{@"data":@{@"list":arr}};
        NSError *error;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dic options:0 error:&error];
        
        if (!jsonData) {
            // 处理错误
            NSLog(@"Error converting dictionary to JSON: %@", error);
            return nil; // 或者返回一个错误响应
        } else {
            // 创建HTTPDataResponse对象并返回
            return [[MyHTTPResponse alloc] initWithData:jsonData];
        }
        
    }
    if ( [path hasPrefix:@"/albums"]&&[path containsString:@"/photos"])
    {
        NSURL *url = [NSURL URLWithString:path];
        NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
        NSArray<NSURLQueryItem *> *queryItems = components.queryItems;
        
        // 用于存储解析后的参数
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        for (NSURLQueryItem *item in queryItems) {
            params[item.name] = item.value;
        }
        // 确保两个字符串都被找到
        NSRange rangeOfAlbums = [path rangeOfString:@"/albums/"];
        NSRange rangeOfPhotos = [path rangeOfString:@"/photos"];
        if (rangeOfAlbums.location != NSNotFound && rangeOfPhotos.location != NSNotFound) {
            // 计算开始位置和长度
            NSUInteger startIndex = rangeOfAlbums.location + rangeOfAlbums.length;
            NSUInteger length = rangeOfPhotos.location - startIndex;
            
            // 提取字符串
            params[@"albumID"] = [path substringWithRange:NSMakeRange(startIndex, length)];
            
        } else {
            NSLog(@"String format not as expected");
        }
        
        
        
        //        params[@"albumID"] = pathArr[pathArr.count-2];
        WebGelistManager * manager = [WebGelistManager new];
        NSArray* arr = [manager fetchPhotosInAlbum:params[@"albumID"] forPage:[params[@"page"] intValue]];
        NSDictionary* dic = @{@"data":@{@"list":arr}};
        NSError *error;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dic options:0 error:&error];
        
        if (!jsonData) {
            // 处理错误
            NSLog(@"Error converting dictionary to JSON: %@", error);
            return [[HTTPErrorResponse alloc]initWithErrorCode:404]; // 或者返回一个错误响应
        } else {
            // 创建HTTPDataResponse对象并返回
            return [[MyHTTPResponse alloc] initWithData:jsonData];
        }
        
    }
    if ( [path hasPrefix:@"/videos"] && [path containsString:@"page"])
    {
        NSURL *url = [NSURL URLWithString:path];
        NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
        NSArray<NSURLQueryItem *> *queryItems = components.queryItems;
        
        // 用于存储解析后的参数
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        for (NSURLQueryItem *item in queryItems) {
            params[item.name] = item.value;
        }
        WebGelistManager * manager = [WebGelistManager new];
        NSArray* arr = [manager fetchVideosForPage:[params[@"page"] intValue]];
        NSDictionary* dic = @{@"data":@{@"list":arr}};
        NSError *error;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dic options:0 error:&error];
        
        if (!jsonData) {
            // 处理错误
            NSLog(@"Error converting dictionary to JSON: %@", error);
            return nil; // 或者返回一个错误响应
        } else {
            // 创建HTTPDataResponse对象并返回
            return [[MyHTTPResponse alloc] initWithData:jsonData];
        }
        
    }
    if ( [path hasSuffix:@"/musics"])
    {
        NSURL *url = [NSURL URLWithString:path];
        NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
        NSArray<NSURLQueryItem *> *queryItems = components.queryItems;
        
        // 用于存储解析后的参数
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        for (NSURLQueryItem *item in queryItems) {
            params[item.name] = item.value;
        }
        WebGelistManager * manager = [WebGelistManager new];
        NSArray* arr = [manager fetchMusicLibrary];
        NSDictionary* dic = @{@"data":@{@"list":arr}};
        NSError *error;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dic options:0 error:&error];
        
        if (!jsonData) {
            // 处理错误
            NSLog(@"Error converting dictionary to JSON: %@", error);
            return nil; // 或者返回一个错误响应
        } else {
            // 创建HTTPDataResponse对象并返回
            return [[MyHTTPResponse alloc] initWithData:jsonData];
        }
        
    }
    if ( [path hasPrefix:@"/photos"]&&![path containsString:@"thumbnail"]) {
        NSURL *url = [NSURL URLWithString:path];
        NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
        NSArray<NSURLQueryItem *> *queryItems = components.queryItems;
        
        // 用于存储解析后的参数
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        for (NSURLQueryItem *item in queryItems) {
            params[item.name] = item.value;
        }
        
        NSRange rangeOfAlbums = [path rangeOfString:@"/photos/"];
        if (rangeOfAlbums.location != NSNotFound) {
            // 计算开始位置
            NSUInteger startIndex = rangeOfAlbums.location + rangeOfAlbums.length;
            
            // 从startIndex开始截取到字符串的末尾
            NSString *albumContent = [path substringFromIndex:startIndex];
            
            params[@"localID"] = albumContent;
        } else {
            NSLog(@"String format not as expected");
        }
        
        
        
        
        // 使用 dispatch_semaphore 创建一个信号量
        dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
        
        WebGelistManager* manager = [WebGelistManager new];
        __block NSData *photoData = nil;
        
        [manager fetchPhotoDataWithLocalID:params[@"localID"] completion:^(NSData * _Nonnull data) {
            photoData = data;
            // 发送信号，告诉主线程数据已准备好
            dispatch_semaphore_signal(semaphore);
        }];
        
        // 等待信号，直到数据准备好
        dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        
        if (photoData) {
            
            return [[MyHTTPResponse alloc] initWithData:photoData];;
        } else {
            // 如果无法获取照片数据，可以返回适当的错误响应，例如404 Not Found
            return [[HTTPErrorResponse alloc] initWithErrorCode:404];
        }
        
    }
    if ( [path hasPrefix:@"/photos"]&&[path containsString:@"thumbnail"]) {
        NSURL *url = [NSURL URLWithString:path];
        NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
        NSArray<NSURLQueryItem *> *queryItems = components.queryItems;
        
        // 用于存储解析后的参数
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        for (NSURLQueryItem *item in queryItems) {
            params[item.name] = item.value;
        }
        
        NSRange rangeOfAlbums = [path rangeOfString:@"/photos/"];
        NSRange rangeOfPhotos = [path rangeOfString:@"/thumbnail"];
        if (rangeOfAlbums.location != NSNotFound && rangeOfPhotos.location != NSNotFound) {
            // 计算开始位置和长度
            NSUInteger startIndex = rangeOfAlbums.location + rangeOfAlbums.length;
            NSUInteger length = rangeOfPhotos.location - startIndex;
            
            // 提取字符串
            params[@"localID"] = [path substringWithRange:NSMakeRange(startIndex, length)];
            
        } else {
            NSLog(@"String format not as expected");
        }
        
        dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
        
        WebGelistManager* manager = [WebGelistManager new];
        __block NSData *photoData = nil;
        
        [manager fetchThumbnailImageDataForAssetWithLocalID:params[@"localID"] completion:^(NSData * _Nonnull data) {
            photoData = data;
            // 发送信号，告诉主线程数据已准备好
            dispatch_semaphore_signal(semaphore);
        }];
        
        // 等待信号，直到数据准备好
        dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        
        if (photoData) {
            
            return [[MyHTTPResponse alloc] initWithData:photoData];;
        } else {
            // 如果无法获取照片数据，可以返回适当的错误响应，例如404 Not Found
            return [[HTTPErrorResponse alloc] initWithErrorCode:404];
        }
        
    }
    if ( [path hasPrefix:@"/videos"]&&[path containsString:@"thumbnail"]) {
        NSURL *url = [NSURL URLWithString:path];
        NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
        NSArray<NSURLQueryItem *> *queryItems = components.queryItems;
        
        // 用于存储解析后的参数
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        for (NSURLQueryItem *item in queryItems) {
            params[item.name] = item.value;
        }
        NSRange rangeOfAlbums = [path rangeOfString:@"/videos/"];
        NSRange rangeOfPhotos = [path rangeOfString:@"/thumbnail"];
        if (rangeOfAlbums.location != NSNotFound && rangeOfPhotos.location != NSNotFound) {
            // 计算开始位置和长度
            NSUInteger startIndex = rangeOfAlbums.location + rangeOfAlbums.length;
            NSUInteger length = rangeOfPhotos.location - startIndex;
            
            // 提取字符串
            params[@"localID"] = [path substringWithRange:NSMakeRange(startIndex, length)];
            
        } else {
            NSLog(@"String format not as expected");
        }
        
        
        dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
        
        WebGelistManager* manager = [WebGelistManager new];
        __block NSData *photoData = nil;
        
        [manager fetchThumbnailImageDataForVideoWithLocalID:params[@"localID"] completion:^(NSData * _Nonnull thumbnailImageData)  {
            photoData = thumbnailImageData;
            // 发送信号，告诉主线程数据已准备好
            dispatch_semaphore_signal(semaphore);
        }];
        
        // 等待信号，直到数据准备好
        dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        
        if (photoData) {
            
            return [[MyHTTPResponse alloc] initWithData:photoData];;
        } else {
            // 如果无法获取照片数据，可以返回适当的错误响应，例如404 Not Found
            return [[HTTPErrorResponse alloc] initWithErrorCode:404];
        }
        
    }
    
    if ( [path hasPrefix:@"/videos"]&&![path containsString:@"thumbnail"]) {
        NSURL *url = [NSURL URLWithString:path];
        NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
        NSArray<NSURLQueryItem *> *queryItems = components.queryItems;
        
        // 用于存储解析后的参数
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        for (NSURLQueryItem *item in queryItems) {
            params[item.name] = item.value;
        }
        NSRange rangeOfAlbums = [path rangeOfString:@"/videos/"];
        if (rangeOfAlbums.location != NSNotFound) {
            // 计算开始位置
            NSUInteger startIndex = rangeOfAlbums.location + rangeOfAlbums.length;
            
            // 从startIndex开始截取到字符串的末尾
            NSString *albumContent = [path substringFromIndex:startIndex];
            
            params[@"localID"] = albumContent;
        } else {
            NSLog(@"String format not as expected");
        }
        dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
        
        WebGelistManager* manager = [WebGelistManager new];
        __block NSData *photoData = nil;
        
        [manager fetchVideoDataWithLocalID:params[@"localID"] completion:^(NSData * _Nonnull data) {
            photoData = data;
            // 发送信号，告诉主线程数据已准备好
            dispatch_semaphore_signal(semaphore);
        }];
        
        // 等待信号，直到数据准备好
        dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        
        if (photoData) {
            
            return [[MyHTTPResponse alloc] initWithData:photoData];;
        } else {
            // 如果无法获取照片数据，可以返回适当的错误响应，例如404 Not Found
            return [[HTTPErrorResponse alloc] initWithErrorCode:404];
        }
        
        
    }
    if ([path hasPrefix:@"/musics"]) {
        NSURL *url = [NSURL URLWithString:path];
        NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
        NSArray<NSURLQueryItem *> *queryItems = components.queryItems;
        
        // 用于存储解析后的参数
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        for (NSURLQueryItem *item in queryItems) {
            params[item.name] = item.value;
        }
        NSRange rangeOfAlbums = [path rangeOfString:@"/musics/"];
        if (rangeOfAlbums.location != NSNotFound) {
            // 计算开始位置
            NSUInteger startIndex = rangeOfAlbums.location + rangeOfAlbums.length;
            
            // 从startIndex开始截取到字符串的末尾
            NSString *albumContent = [path substringFromIndex:startIndex];
            
            params[@"localID"] = albumContent;
        } else {
            NSLog(@"String format not as expected");
        }
        
        
        dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
        
        WebGelistManager* manager = [WebGelistManager new];
        __block NSData *photoData = nil;
        
        [manager fetchMusicDataWithLocalID:params[@"localID"] completion:^(NSData * _Nonnull data) {
            photoData = data;
            // 发送信号，告诉主线程数据已准备好
            dispatch_semaphore_signal(semaphore);
        }];
        
        // 等待信号，直到数据准备好
        dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        
        if (photoData) {
            
            return [[MyHTTPResponse alloc] initWithData:photoData];;
        } else {
            // 如果无法获取照片数据，可以返回适当的错误响应，例如404 Not Found
            return [[HTTPErrorResponse alloc] initWithErrorCode:404];
        }
        
    }
    if ( [path hasSuffix:@"/documents"]) {
        NSURL *url = [NSURL URLWithString:path];
        NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
        NSArray<NSURLQueryItem *> *queryItems = components.queryItems;
        
        // 用于存储解析后的参数
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        for (NSURLQueryItem *item in queryItems) {
            params[item.name] = item.value;
        }
        
        
        
        dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
        
        WebGelistManager* manager = [WebGelistManager new];
        __block NSArray *photoData = nil;
        
        [manager searchFilesInDirectory:NSHomeDirectory() completion:^(NSArray<NSDictionary *> * _Nonnull matchedFiles) {
            photoData = matchedFiles;
            // 发送信号，告诉主线程数据已准备好
            dispatch_semaphore_signal(semaphore);
        }];
        
        // 等待信号，直到数据准备好
        dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        
        if (photoData) {
            NSDictionary* dic = @{@"data":@{@"list":photoData}};
            NSError *error;
            NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dic options:0 error:&error];
            return [[MyHTTPResponse alloc] initWithData:jsonData];;
        } else {
            // 如果无法获取照片数据，可以返回适当的错误响应，例如404 Not Found
            return [[HTTPErrorResponse alloc] initWithErrorCode:404];
        }
        
        
        
    }
    if ( [path hasPrefix:@"/documents"]) {
        NSURL *url = [NSURL URLWithString:path];
        NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
        NSArray<NSURLQueryItem *> *queryItems = components.queryItems;
        
        // 用于存储解析后的参数
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        
        for (NSURLQueryItem *item in queryItems) {
            params[item.name] = item.value;
        }
        NSRange rangeOfAlbums = [path rangeOfString:@"/documents/"];
        if (rangeOfAlbums.location != NSNotFound) {
            // 计算开始位置
            NSUInteger startIndex = rangeOfAlbums.location + rangeOfAlbums.length;
            
            // 从startIndex开始截取到字符串的末尾
            NSString *albumContent = [path substringFromIndex:startIndex];
            
            params[@"path"] = albumContent;
        } else {
            NSLog(@"String format not as expected");
        }
        
        NSURL* fileUrl = [NSURL fileURLWithPath:[NSHomeDirectory() stringByAppendingPathComponent:params[@"path"]]];
        NSData* data = [NSData dataWithContentsOfURL:fileUrl];
        if (data) {
            
            return [[MyHTTPResponse alloc] initWithData:data];;
        } else {
            // 如果无法获取照片数据，可以返回适当的错误响应，例如404 Not Found
            return [[HTTPErrorResponse alloc] initWithErrorCode:404];
        }
        
        
    }
    if ( [path containsString:@"apks"]) {
        
        return [[HTTPErrorResponse alloc] initWithErrorCode:404];
        
        
        
    }
    
    
    return [super httpResponseForMethod:method URI:path];
}

- (NSString *)getPostValueForField:(NSString *)fieldName fromPostBody:(NSString *)postBody
{
    NSDictionary *postParams = [self parseParams:postBody];
    NSString *fieldValue = postParams[fieldName];
    return fieldValue;
}

- (NSDictionary *)parseParams:(NSString *)paramString
{
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    NSArray *paramPairs = [paramString componentsSeparatedByString:@"&"];
    
    for (NSString *pair in paramPairs) {
        NSArray *keyValue = [pair componentsSeparatedByString:@"="];
        if (keyValue.count == 2) {
            NSString *key = keyValue[0];
            NSString *value = [keyValue[1] stringByRemovingPercentEncoding];
            params[key] = value;
        }
    }
    
    return [params copy];
}

- (void)prepareForBodyWithSize:(UInt64)contentLength
{
    HTTPLogTrace();
    
    // set up mime parser
    NSString* boundary = [request headerField:@"boundary"];
    parser = [[MultipartFormDataParser alloc] initWithBoundary:boundary formEncoding:NSUTF8StringEncoding];
    parser.delegate = self;
    
    uploadedFiles = [[NSMutableArray alloc] init];
}

//- (void)processBodyData:(NSData *)postDataChunk
//{
//    HTTPLogTrace();
//    // append data to the parser. It will invoke callbacks to let us handle
//    // parsed data.
//    NSString *postBody = [[NSString alloc] initWithData:postDataChunk encoding:NSUTF8StringEncoding];
//    [parser appendData:postDataChunk];
//}


//-----------------------------------------------------------------
#pragma mark multipart form data parser delegate
- (void) processStartOfPartWithHeader:(MultipartMessageHeader*) header {
    // in this sample, we are not interested in parts, other then file parts.
    // check content disposition to find out filename
    if(!data)
    {
        data = [NSMutableDictionary dictionary];
    }
    MultipartMessageHeaderField* disposition = [header.fields objectForKey:@"Content-Disposition"];
    NSString *filename = [[disposition.params objectForKey:@"filename"] lastPathComponent];
    
    if ( (nil == filename) || [filename isEqualToString: @""] ) {
        // it's either not a file part, or
        // an empty form sent. we won't handle it.
        return;
    }
    //    NSString* uploadDirPath = [[config documentRoot] stringByAppendingPathComponent:@"upload"];
    NSString *uploadDirPath = [[NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject]stringByAppendingPathComponent:@"receive"] ;
    
    BOOL isDir = YES;
    if (![[NSFileManager defaultManager]fileExistsAtPath:uploadDirPath isDirectory:&isDir ]) {
        [[NSFileManager defaultManager]createDirectoryAtPath:uploadDirPath withIntermediateDirectories:YES attributes:nil error:nil];
    }
    
    NSString* filePath = [uploadDirPath stringByAppendingPathComponent: filename];
    data[@"file"] = filePath;
    if( [[NSFileManager defaultManager] fileExistsAtPath:filePath] ) {
        storeFile = nil;
    }
    else {
        //        HTTPLogVerbose(@"Saving file to %@", filePath);
        
        if(![[NSFileManager defaultManager] createDirectoryAtPath:uploadDirPath withIntermediateDirectories:true attributes:nil error:nil]) {
            //            HTTPLogError(@"Could not create directory at path: %@", filePath);
        }
        
        if(![[NSFileManager defaultManager] createFileAtPath:filePath contents:nil attributes:nil]) {
            //            HTTPLogError(@"Could not create file at path: %@", filePath);
        }
        
        storeFile = [NSFileHandle fileHandleForWritingAtPath:filePath];
        [uploadedFiles addObject: [NSString stringWithFormat:@"/upload/%@", filename]];
    }
}


- (void) processContent:(NSData*) data WithHeader:(MultipartMessageHeader*) header
{
    // here we just write the output from parser to the file.
    if( storeFile ) {
        [storeFile writeData:data];
    }
}

- (void) processEndOfPartWithHeader:(MultipartMessageHeader*) header
{
    // as the file part is over, we close the file.
    if(data)
    {
        [[FileManager share]addReceiveFile:data];
        
        [[FileManager share]completeGetFile:data];
        
        [[NSNotificationCenter defaultCenter]postNotificationName:@"updateFileList" object:nil];//通知接收列表更新
        
        [SVProgressHUD showSuccessWithStatus:[NSString stringWithFormat:local(@"收到文件：%@"),[data[@"file"]lastPathComponent]]];
    }
    
    [storeFile closeFile];
    
    storeFile = nil;
    data = nil;
}
- (void)processBodyData:(NSData *)postDataChunk
{
    HTTPLogTrace();
    // append data to the parser. It will invoke callbacks to let us handle
    // parsed data.
    [parser appendData:postDataChunk];
}
- (void) processPreambleData:(NSData*) data
{
    NSString *postBody = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    NSLog(@"PreambleData: %@",postBody);
    // if we are interested in preamble data, we could process it here.
}

- (void) processEpilogueData:(NSData*) data
{
    NSString *postBody = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    NSLog(@"pilogueDat: %@",postBody);
    // if we are interested in epilogue data, we could process it here.
    
}
static NSInteger fileDataStartIndex=0;
//处理POST请求提交的数据流(下面方法是改自 Andrew Davidson的类)


//
//- (void)printBinaryData:(NSData *)data {
//    const unsigned char *bytes = [data bytes];
//    NSUInteger length = [data length];
//
//    NSMutableString *output = [NSMutableString stringWithString:@"Binary Data:\n"];
//
//    for (NSUInteger i = 0; i < length; i++) {
//        unsigned char byte = bytes[i];
//
//        // 打印字节的十六进制表示
//        [output appendFormat:@"%02X ", byte];
//
//        // 如果字节是可打印字符，打印对应的字符
//        if (byte >= 32 && byte < 127) {
//            [output appendFormat:@"%c", byte];
//        } else {
//            // 否则，打印一个点来表示不可打印字符
//            [output appendString:@"."];
//        }
//
//        // 在适当的位置添加换行符，以使输出更易读
//        if ((i + 1) % 16 == 0) {
//            [output appendString:@"\n"];
//        } else if ((i + 1) % 8 == 0) {
//            [output appendString:@"  "];
//        }
//    }
//
//    NSLog(@"%@", output);
//}
//
//
//- (NSString*)paserDateArray:(NSArray*)dataarray key:(NSString*)key
//{
//    NSString* postInfo = nil;
//    for (int i  =0 ;i<multipartData.count;i++) {
//        NSString* str = [[NSString alloc] initWithBytes:[[multipartData objectAtIndex:i] bytes]
//                                                      length:[[multipartData objectAtIndex:i] length]
//                                                    encoding:NSUTF8StringEncoding];
//        if([str rangeOfString:key].length>0&&i!=multipartData.count-1)
//        {
//            postInfo = [str stringByAppendingString: [[NSString alloc] initWithBytes:[[multipartData objectAtIndex:i+1] bytes]
//                                                                              length:[[multipartData objectAtIndex:i+1] length]
//                                                                            encoding:NSUTF8StringEncoding]];
//            NSLog(@"%d->%@",i,postInfo);
//        }
//        if([str rangeOfString:key].length>0&&i==multipartData.count-1)
//        {
//            postInfo = str ;
//        }
//    }
//
//    NSLog(@"postInfo is:%@", postInfo);
//    NSArray* postInfoComponents = [postInfo componentsSeparatedByString:[NSString stringWithFormat:@"; %@=",key]];
//    postInfoComponents = [[postInfoComponents lastObject] componentsSeparatedByString:@"\""];
//    NSLog(@"postInfoComponents0 :%@",postInfoComponents);
//    if ([postInfoComponents count]<2)
//    {
//        return @"";
//    }
//
//
//    if([key isEqualToString:@"filename"])
//    {
//        postInfoComponents = [[postInfoComponents objectAtIndex:1] componentsSeparatedByString:@"\\"];
//        NSString *documentPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
//        NSString* filename = [documentPath stringByAppendingPathComponent:[postInfoComponents lastObject]];
//        return filename;
//    }
//    else
//    {
//        return postInfoComponents[[postInfoComponents indexOfObject:key]+1];
//    }
//}
////检查是否已经处理完了multipart/form-data表单中的表单变量
//- (BOOL) isBeginOfOctetStream
//{
//NSString *octetStreamFlag = @"Content-Disposition: form-data";
//NSString *findData = [[NSString alloc] initWithData:(NSData *)[multipartData lastObject] encoding:NSUTF8StringEncoding];
//
//for (NSData *d in multipartData) {
//    NSString *temp = [[NSString alloc] initWithData:d encoding:NSUTF8StringEncoding] ;
//    NSLog(@"multipartData items: %@", temp);
//}
//    //如果已经处理完了multipart/form-data表单中的表单变量
//if ( findData != nil && [findData length] > 0 )
//{
//    NSLog(@"findData is :%@\n octetStreamFlag is :%@", findData, octetStreamFlag);
//    if ([findData containsString:@"filename="]) {
//        NSLog(@"multipart/form-data 变量值域数据处理完毕");
//
//        return YES;
//    }
//
//    return NO;
//}
//return NO;
//
//}
@end

