//
//  MyCustomDataResponse.m
//  mobiletransfer
//
//  Created by fs0011 on 2023/12/22.
//  Copyright © 2023 <PERSON><PERSON><PERSON>. All rights reserved.
//

#import "MyCustomDataResponse.h"

@implementation MyCustomDataResponse
- (instancetype)initWithDataBlock:(NSData *(^)(void))dataBlock {
    self = [super init];
    if (self) {
        _dataBlock = dataBlock;
    }
    return self;
}

- (UInt64)contentLength {
    NSData *data = self.dataBlock();
    return (UInt64)data.length;
}

- (UInt64)offset {
    return 0;
}

- (void)setOffset:(UInt64)offset {
    // Not used in this custom response
}

- (NSData *)readDataOfLength:(NSUInteger)length {
    NSData *data = self.dataBlock();
    if (data) {
        NSUInteger remainingLength = MIN(length, data.length);
        NSData *chunk = [data subdataWithRange:NSMakeRange(0, remainingLength)];
        self.dataBlock = ^{
            return [data subdataWithRange:NSMakeRange(remainingLength, data.length - remainingLength)];
        };
        return chunk;
    } else {
        return nil;
    }
}
@end
