//
//  AppDelegate.m
//  PhoneClone
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "AppDelegate.h"
#import "BSLaunchScreenVC.h"
#import "BSTabBarController.h"
#import "BSNavigationController.h"
#import "StoreIAPManager.h"
#import "UpdateManager.h"
#import <AppTrackingTransparency/AppTrackingTransparency.h>
#import <UMAPM/UMAPMConfig.h>
#import <UMAPM/UMCrashConfigure.h>
@interface AppDelegate ()<BUSplashAdDelegate>
@property (nonatomic, strong) BUSplashAd *splashAd;
@property BOOL isShow;
@end

@implementation AppDelegate


- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    self.whiteList = [NSMutableArray array];
    self.window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
    self.window.backgroundColor = [UIColor whiteColor];
    [StoreIAPManager sharedManager];
    
    
    
    //如果[打开app状态]为[用户已经进入过一次主界面],即直接进入[主页],否则进入[引导界面]
//    if([BSUserDefaultManager getOpenAppState] == Goto_Main_Interface){
//        BSTabBarController *mainTabBarCtl = [[BSTabBarController alloc] init];
//        [self.window setRootViewController:mainTabBarCtl];
//        [self.window makeKeyAndVisible];
//        return YES;
//    }
    BSLaunchScreenVC *launchScreenVc = [[BSLaunchScreenVC alloc] init];
    UINavigationController *launchScreenNav = [[UINavigationController alloc] initWithRootViewController:launchScreenVc];
    [self.window setRootViewController:launchScreenNav];
    [self.window makeKeyAndVisible];
    
    
    [[NSURLCache sharedURLCache] removeAllCachedResponses];

    AFHTTPSessionManager *_sessionManager = [AFHTTPSessionManager manager];;
    _sessionManager.requestSerializer = [AFJSONRequestSerializer serializer];
    [_sessionManager.requestSerializer setValue:@"application/json" forHTTPHeaderField:@"Accept"];
    _sessionManager.requestSerializer.cachePolicy = NSURLRequestReloadIgnoringLocalCacheData;
    [_sessionManager GET:updateJsonUrl parameters:nil headers:nil progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        NSDictionary* dic = responseObject;
        if([dic isKindOfClass:[NSDictionary class]])
        {
           
            self.showGuide = [dic[@"showGuide"] boolValue];
//            self.showGuide = YES;
            self.showWebLink = [dic[@"showWebLink"] boolValue];
//            self.showWebLink = YES;
            self.window.rootViewController = [BSLaunchScreenVC new];
            [self.window makeKeyAndVisible];
//            self.isAnyOK =  YES;
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        self.showGuide = NO;
        self.window.rootViewController = [BSLaunchScreenVC new];
        [self.window makeKeyAndVisible];
    }];
    
    self.allowUseCount = 2;//允许发送的次数
    UpdateManager* date = [UpdateManager shareManagerWith:updateJsonUrl];
    date.complete = ^(id  _Nonnull sender) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if(!self.isShow)
            {
                [self showSplashAd];
            }
        });
    };
    
    NSInteger timers = [W_PERSISTENT_GET_OBJECT(startWithADTimes) integerValue]+1;
        W_PERSISTENT_SET_OBJECT(@(timers), startWithADTimes);
    
    if(W_PERSISTENT_GET_OBJECT(@"ad_count"))
        date.AD_count = [W_PERSISTENT_GET_OBJECT(@"ad_count") intValue];
    else
        date.AD_count = 3;
    BUAdSDKConfiguration *configuration = [BUAdSDKConfiguration configuration];
//        configuration.territory = BUAdSDKTerritory_CN;
    
        configuration.appID = AD_APP_ID;//除appid外，其他参数配置按照项目实际需求配置即可。
    [BUAdSDKManager startWithAsyncCompletionHandler:^(BOOL success, NSError *error) {
        if(success)
        {
            [self showSplashAd];
        }
    }];
   
    
    
    [UMConfigure setEncryptEnabled:NO];
    [UMConfigure setLogEnabled:NO];
    [UMConfigure setLogEnabled:NO];
    UMAPMConfig* config = [UMAPMConfig defaultConfig];

    config.crashAndBlockMonitorEnable = YES;
    config.memMonitorEnable = YES;
    config.oomMonitorEnable = YES;
    config.launchMonitorEnable = YES;
    config.networkEnable = YES;
    config.pageMonitorEnable = YES;
    
    [UMCrashConfigure setAPMConfig:config];
    
    
    [UMConfigure initWithAppkey:@"65b7589c95b14f599d1e6eff" channel:@"App Store"];
//    [MobClick setScenarioType:E_UM_NORMAL];//支持普通场景
    [MobClick setAutoPageEnabled:YES];
    
//    [BUAdTestMeasurementConfiguration configuration].debugMode = YES;
    return YES;
}

- (void)showSplashAd
{
    BOOL isZH = [self getCurrentLanguageIsZH];
    if ([W_PERSISTENT_GET_OBJECT(startWithADTimes) integerValue]>=[UpdateManager shareManagerWith:updateJsonUrl].AD_count) {
        self.splashAd = [[BUSplashAd alloc] initWithSlotID:AD_SplashAd adSize:[UIScreen mainScreen].bounds.size];
        self.splashAd.delegate = self;
        [self.splashAd loadAdData];
    }
}

- (void)dismissSplashAd
{
    if (self.splashAd) {
        self.splashAd = nil;
    }
    [[NSNotificationCenter defaultCenter] postNotificationName:@"DismissSplashAdNotification" object:nil userInfo:nil];
}

- (void)splashAdLoadSuccess:(BUSplashAd *)splashAd
{
    NSLog(@"splashAdLoadSuccess");
    if (splashAd == self.splashAd) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.splashAd showSplashViewInRootViewController:self.window.rootViewController];
            self.isShow = YES;
        });
    }
}

/// This method is called when material load failed
- (void)splashAdLoadFail:(BUSplashAd *)splashAd error:(BUAdError *_Nullable)error
{
    NSLog(@"splashAdLoadFail");
    if (splashAd == self.splashAd) {
        [self dismissSplashAd];
    }
}

/// This method is called when splash view render successful
- (void)splashAdRenderSuccess:(BUSplashAd *)splashAd
{
    NSLog(@"splashAdRenderSuccess");
}

/// This method is called when splash view render failed
- (void)splashAdRenderFail:(BUSplashAd *)splashAd error:(BUAdError *_Nullable)error
{
    NSLog(@"splashAdRenderFail");
    if (splashAd == self.splashAd) {
        [self dismissSplashAd];
    }
}

/// This method is called when splash view will show
- (void)splashAdWillShow:(BUSplashAd *)splashAd
{
    NSLog(@"splashAdWillShow");
}

/// This method is called when splash view did show
- (void)splashAdDidShow:(BUSplashAd *)splashAd
{
    NSLog(@"splashAdDidShow");
}

/// This method is called when splash view is clicked.
- (void)splashAdDidClick:(BUSplashAd *)splashAd
{
    NSLog(@"splashAdDidClick");
    if (splashAd == self.splashAd) {
        [self dismissSplashAd];
    }
}

/// This method is called when splash view is closed.
- (void)splashAdDidClose:(BUSplashAd *)splashAd closeType:(BUSplashAdCloseType)closeType
{
    NSLog(@"splashAdDidClose");
    if (splashAd == self.splashAd) {
        [self dismissSplashAd];
    }
}

/// This method is called when splash viewControllr is closed.
- (void)splashAdViewControllerDidClose:(BUSplashAd *)splashAd
{
    NSLog(@"splashAdViewControllerDidClose");
    if (splashAd == self.splashAd) {
        [self dismissSplashAd];
    }
}


- (void)startMonitoringNetworkStatus {
    AFNetworkReachabilityManager *manager = [AFNetworkReachabilityManager sharedManager];

    [manager setReachabilityStatusChangeBlock:^(AFNetworkReachabilityStatus status) {
        switch (status) {
            case AFNetworkReachabilityStatusNotReachable:
                NSLog(@"无网络连接");
                
                break;
            case AFNetworkReachabilityStatusReachableViaWiFi:
                NSLog(@"WiFi连接");

                break;
            case AFNetworkReachabilityStatusReachableViaWWAN:
                NSLog(@"蜂窝数据连接");
                break;
            case AFNetworkReachabilityStatusUnknown:
            default:
                NSLog(@"未知网络状态");
                break;
        }
    }];

    [manager startMonitoring];
}

- (void)applicationWillResignActive:(UIApplication *)application {
    
}


- (void)applicationDidEnterBackground:(UIApplication *)application {
    
    [[NSNotificationCenter defaultCenter] postNotificationName:@"applicationDidEnterBackground" object:nil];
    
}


- (void)applicationWillEnterForeground:(UIApplication *)application {

}


- (void)applicationDidBecomeActive:(UIApplication *)application {
    // Called when the scene has moved from an inactive state to an active state.
    // Use this method to restart any tasks that were paused (or not yet started) when the scene was inactive.
    dispatch_async(dispatch_get_main_queue(), ^{
        if (@available(iOS 14, *)) {
            [ATTrackingManager requestTrackingAuthorizationWithCompletionHandler:^(ATTrackingManagerAuthorizationStatus status) {
                // Tracking authorization completed. Start loading ads here.
                NSLog(@"ATTrackingManagerAuthorizationStatus = %lu", (unsigned long)status);
            }];
        }
    });
}


- (void)applicationWillTerminate:(UIApplication *)application {

}

#pragma mark - Private
+ (instancetype)getAppDelegate {
    return (AppDelegate *)[UIApplication sharedApplication].delegate;
}
// 判断是中文还是其他语言
- (BOOL)getCurrentLanguageIsZH
{
    BOOL isZH = NO;
    
    NSString *currentLanguage1 = [[NSUserDefaults standardUserDefaults] objectForKey:@"AppleLanguages"][0];
    NSString *currentLanguage2 = [[NSBundle mainBundle] preferredLocalizations][0];
    NSLog(@"language1 == %@   language2 == %@", currentLanguage1, currentLanguage2);

    if ([currentLanguage1 containsString:@"zh-Ha"]  ||  [currentLanguage2 containsString:@"zh-Ha"]) {
        isZH = YES;
        NSLog(@"这个是中文");
    }
    
    return isZH;
}
@end
