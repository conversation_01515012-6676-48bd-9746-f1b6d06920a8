//
//  MyAdManager.h
//  FootprintMap
//
//  Created by 叶庭文 on 2022/4/9.
//

#import <Foundation/Foundation.h>
#import "BSBaseViewController.h"
#import <BUAdSDK/BUAdSDK.h>
#define startWithADTimes @"startWithADTimes"
NS_ASSUME_NONNULL_BEGIN

@protocol MyAdManagerDelegate <NSObject>

//
- (void)myAdManagerInterstitialAdCloseRefresh;

- (void)myAdManagerInterstitialAdRenderSuccess;

- (void)myAdManagerLoadInterstitialAdFailed;


//
- (void)myAdManagerFullscreenVideoAdCloseRefresh;

- (void)myAdManagerFullscreenVideoAdDidDownLoadVideo;

- (void)myAdManagerLoadFullscreenVideoAdFailed;


//
- (void)myAdManagerBannerViewCloseRefresh;

- (void)myAdManagerBannerAdViewRenderSuccess;

- (void)myAdManagerLoadBannerViewFailed;


// 判断奖励视频  奖励是否发生
- (void)myAdManagerRewardedVideoAdCloseIsVerify:(BOOL)verify;

- (void)myAdManagerRewardedVideoAdDidDownLoadVideo;

- (void)myAdManagerLoadRewardedVideoAdFailed;

@end

@interface MyAdManager : NSObject

@property (nonatomic, weak) id <MyAdManagerDelegate> delegate;

@property (nonatomic, assign) BOOL isShowRewardedVideoAd;  // 判断当前激励视频广告  是否已经出现
@property (nonatomic, strong) BUNativeExpressBannerView *bannerView;

// 显示插屏广告
- (void)showInterstitialAdWithViewController:(BSBaseViewController *)viewController;

// 显示新插屏广告
- (void)showFullscreenVideoAdWithViewController:(BSBaseViewController *)viewController;

// 显示banner广告
- (void)showBannerAdWithViewController:(BSBaseViewController *)viewController frame:(CGRect)frame size:(CGSize)size;

// 显示激励广告
- (void)showRewardedVideoAdWithViewController:(BSBaseViewController *)viewController;



@end

NS_ASSUME_NONNULL_END
