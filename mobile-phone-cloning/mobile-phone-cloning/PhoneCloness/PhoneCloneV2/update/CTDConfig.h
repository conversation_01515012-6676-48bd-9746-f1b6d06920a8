//
//  CTDConfig.h
//  CPATestDatabase
//
//  Created by fs0011 on 2022/7/7.
//

#ifndef CTDConfig_h
#define CTDConfig_h


#define SCREEN_WIDTH   [UIScreen mainScreen].bounds.size.width
#define SCREENH_HEIGHT [UIScreen mainScreen].bounds.size.height


#define WeakSelf __weak typeof(self) weakSelf = self/*weakSelf*/
#define WeakObj(o) __weak typeof(o) o##Weak = o;




/******************************************************************************
 *    永久存储对象
 *
 *  NSUserDefaults保存的文件在tmp文件夹里
 *
 *    @param    object      需存储的对象
 *    @param    key         对应的key
 */
#define W_PERSISTENT_SET_OBJECT(object, key)                                                                                                 \
({                                                                                                                                             \
NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];                                                                          \
[defaults setObject:object forKey:key];                                                                                                    \
[defaults synchronize];                                                                                                                    \
})

/**
 *    取出永久存储的对象
 *
 *    @param    key     所需对象对应的key
 *    @return    key     所对应的对象
 */
#define W_PERSISTENT_GET_OBJECT(key)  [[NSUserDefaults standardUserDefaults] objectForKey:key]

/**
 *  清除 NSUserDefaults 保存的所有数据
 */
#define W_PERSISTENT_REMOVE_ALLDATA   [[NSUserDefaults standardUserDefaults] removePersistentDomainForName:[[NSBundle mainBundle] bundleIdentifier]]

/**
 *  清除 NSUserDefaults 保存的指定数据
 */
#define W_PERSISTENT_REMOVE(_key)                                         \
({                                                                          \
NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];       \
[defaults removeObjectForKey:_key];                                     \
[defaults synchronize];                                                 \
})
///******************************************************************************
#define themColor [UIColor colorWithHexString:@"#2197D3" alpha:1]
//color
#define seletedColor [UIColor colorWithHexString:@"316DFF" alpha:1]
#define unseletColor [UIColor colorWithHexString:@"AAAAAA" alpha:1]

#define groundTableColor [UIColor colorWithHexString:@"FAFAFA" alpha:1]
#define VIPDetailColor [UIColor colorWithHexString:@"FFE8CC" alpha:1]

#define detailLabelColor [UIColor colorWithHexString:@"999999" alpha:1]
#define darkTextColor [UIColor colorWithHexString:@"333333" alpha:1]
#define darkTableTextColor [UIColor colorWithHexString:@"121212" alpha:1]
#define tableSeparatorColor [UIColor colorWithHexString:@"eeeeee" alpha:1]

//font
#define smallFont [UIFont systemFontOfSize:12]
#define smallestFont [UIFont systemFontOfSize:10]
#define bigMedlumFont [UIFont systemFontOfSize:16 weight:UIFontWeightMedium]
#define standMedlumFont [UIFont systemFontOfSize:14 weight:UIFontWeightMedium]
#define smallMedlumFont [UIFont systemFontOfSize:12 weight:UIFontWeightMedium]
#define detailFont [UIFont systemFontOfSize:14]
#define blodTitleFont [UIFont boldSystemFontOfSize:24]
#define blodLoginTitleFont [UIFont boldSystemFontOfSize:32]
#define naviTitleFont [UIFont boldSystemFontOfSize:20]
//float
#define tableEdge 16.0f
#define cardEdge 24.0f
#define defaultCorner 12.f
#define INTERFACE_IS_IPHONEX (@available(iOS 11.0, *) && ([UIApplication sharedApplication].keyWindow.safeAreaInsets.bottom > 0)?YES:NO)
#define bottomHeight (INTERFACE_IS_IPHONEX ? 20 : 0)
#define nav_Height (INTERFACE_IS_IPHONEX ? 88 : 64)
#define tabBar_Height (INTERFACE_IS_IPHONEX ? 83 : 4)




#endif /* CTDConfig_h */
