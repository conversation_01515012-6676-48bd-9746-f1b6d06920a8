//
//  UpdateManager.h
//  photoTimeMachine
//
//  Created by fs0011 on 2023/6/20.
//

#import <Foundation/Foundation.h>
#import "AFNetworking.h"
typedef void(^ActionCallBack) (void);
typedef void(^ArgumentCallBack) (id sender);
NS_ASSUME_NONNULL_BEGIN

@interface UpdateManager : NSObject
+ (instancetype)shareManagerWith:(NSString*)url;
- (instancetype)initWithUpdateUrl:(NSString*)url;
@property ArgumentCallBack complete;
@property NSInteger AD_count;
- (void)postUrl:(NSString*)url;
@end

NS_ASSUME_NONNULL_END
   
