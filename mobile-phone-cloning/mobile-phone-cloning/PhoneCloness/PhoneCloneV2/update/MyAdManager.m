//
//  MyAdManager.m
//  FootprintMap
//
//  Created by 叶庭文 on 2022/4/9.
//

#import "MyAdManager.h"
#import <BUAdSDK/BUAdSDK.h>

@interface MyAdManager () < BUNativeExpressFullscreenVideoAdDelegate, BUNativeExpressBannerViewDelegate, BUNativeExpressRewardedVideoAdDelegate>

@property (nonatomic, strong) BSBaseViewController *viewController;

//@property (nonatomic, strong) BUNativeExpressInterstitialAd *interstitialAd;

@property (nonatomic, strong) BUNativeExpressFullscreenVideoAd *fullscreenVideoAd;



@property (nonatomic, strong) BUNativeExpressRewardedVideoAd *rewardedAd;

@property (nonatomic, assign) BOOL isVerify;   // 判断当前激励视频广告  是否获得奖励

@property (nonatomic, assign) int interstitialAdCount;

@property (nonatomic, assign) int fullscreenVideoAdCount;

@property (nonatomic, assign) int bannerViewCount;

@property (nonatomic, assign) int rewardedAdCount;

@end

@implementation MyAdManager

- (id)init
{
    self = [super init];
    if (self) {
        self.isShowRewardedVideoAd = NO;
        self.isVerify = NO;
        
        self.interstitialAdCount = 0;
        
        self.fullscreenVideoAdCount = 0;
        
        self.bannerViewCount = 0;
        
        self.rewardedAdCount = 0;
    }
    return self;
}


// FullscreenVideoAd
//- (void)showFullscreenVideoAdWithViewController:(BaseViewController *)viewController
//{
//    self.viewController = viewController;
//    
//    self.fullscreenVideoAdCount = 0;
//    
//    if (self.fullscreenVideoAd == nil) {
//        /*
//        BUAdSlot *adSlot = [[BUAdSlot alloc] init];
//        adSlot.ID = @"949339510";
//        adSlot.AdType = BUAdSlotAdTypeInterstitial;
//        adSlot.position = BUAdSlotPositionMiddle;
//        adSlot.adSize = CGSizeMake(300, 300);
//        BUSize *imgSize = [[BUSize alloc] init];
//        imgSize.width = 300;
//        imgSize.height = 300;
//        adSlot.imgSize = imgSize;    */
//        
//        self.fullscreenVideoAd = [[BUNativeExpressFullscreenVideoAd alloc] initWithSlotID:AD_FullscreenVideoAd];
//        self.fullscreenVideoAd.delegate = self;
//        [self.fullscreenVideoAd loadAdData];
//    }
//}
- (void)showFullscreenVideoAdWithViewController:(BSBaseViewController*)viewController
{
    self.viewController = viewController;
    
    if (self.fullscreenVideoAd == nil) {
        self.fullscreenVideoAd = [[BUNativeExpressFullscreenVideoAd alloc] initWithSlotID:@"955433869"];
//        self.fullscreenVideoAd = [[BUNativeExpressFullscreenVideoAd alloc] initWithSlotID:@"951499981"];
        self.fullscreenVideoAd.delegate = self;
        [self.fullscreenVideoAd loadAdData];
    }
}
- (void)nativeExpressFullscreenVideoAdDidLoad:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd
{
    NSLog(@"nativeExpressFullscreenVideoAdDidLoad");
}

- (void)nativeExpressFullscreenVideoAdViewRenderSuccess:(BUNativeExpressFullscreenVideoAd *)rewardedVideoAd
{
    NSLog(@"nativeExpressFullscreenVideoAdViewRenderSuccess");
}

- (void)nativeExpressFullscreenVideoAd:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd didFailWithError:(NSError *_Nullable)error
{
    NSLog(@"nativeExpressFullscreenVideoAd");
    if (error != nil) {
        if (self.fullscreenVideoAd) {
            
                if ([self.delegate respondsToSelector:@selector(myAdManagerLoadFullscreenVideoAdFailed)]) {
                    [self.delegate myAdManagerLoadFullscreenVideoAdFailed];
                }
            }
        }
    }


- (void)nativeExpressFullscreenVideoAdViewRenderFail:(BUNativeExpressFullscreenVideoAd *)rewardedVideoAd error:(NSError *_Nullable)error
{
    NSLog(@"nativeExpressFullscreenVideoAdViewRenderFail");
    if (error != nil) {
        if (self.fullscreenVideoAd) {
            
                if ([self.delegate respondsToSelector:@selector(myAdManagerLoadFullscreenVideoAdFailed)]) {
                    [self.delegate myAdManagerLoadFullscreenVideoAdFailed];
                }
            }
        }
    }


- (void)reloadFullscreenVideoAd
{
    [self.fullscreenVideoAd loadAdData];
    self.fullscreenVideoAdCount ++;
}

- (void)nativeExpressFullscreenVideoAdDidDownLoadVideo:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd
{
    self.fullscreenVideoAdCount = 100;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.fullscreenVideoAd) {
           [self.fullscreenVideoAd showAdFromRootViewController:self.viewController];
        }
        
        if ([self.delegate respondsToSelector:@selector(myAdManagerFullscreenVideoAdDidDownLoadVideo)]) {
            [self.delegate myAdManagerFullscreenVideoAdDidDownLoadVideo];
        }
    });
}

- (void)nativeExpressFullscreenVideoAdWillVisible:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd
{
    
}

- (void)nativeExpressFullscreenVideoAdDidVisible:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd
{
    
}

- (void)nativeExpressFullscreenVideoAdDidClick:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd
{
    
}

- (void)nativeExpressFullscreenVideoAdDidClickSkip:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd
{
    
}

- (void)nativeExpressFullscreenVideoAdWillClose:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd
{
    
}

- (void)nativeExpressFullscreenVideoAdDidClose:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd
{
    self.fullscreenVideoAd = nil;
}

- (void)nativeExpressFullscreenVideoAdDidPlayFinish:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd didFailWithError:(NSError *_Nullable)error
{
    
}

- (void)nativeExpressFullscreenVideoAdCallback:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd withType:(BUNativeExpressFullScreenAdType) nativeExpressVideoAdType
{
    
}

- (void)nativeExpressFullscreenVideoAdDidCloseOtherController:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd interactionType:(BUInteractionType)interactionType
{
    
}


// BannerAd
- (void)showBannerAdWithViewController:(BSBaseViewController *)viewController frame:(CGRect)frame size:(CGSize)size
{
    self.viewController = viewController;
    
    self.bannerViewCount = 0;
    
    UIWindow *window = nil;
    if ([[UIApplication sharedApplication].delegate respondsToSelector:@selector(window)]) {
        window = [[UIApplication sharedApplication].delegate window];
    }
    if (![window isKindOfClass:[UIView class]]) {
        window = [UIApplication sharedApplication].keyWindow;
    }
    if (!window) {
        window = [[UIApplication sharedApplication].windows objectAtIndex:0];
    }
    CGFloat bottom = 0.0;
    if (@available(iOS 11.0, *)) {
        bottom = window.safeAreaInsets.bottom;
    } else {
        // Fallback on earlier versions
    }
    if (self.bannerView == nil) {
        self.bannerView = [[BUNativeExpressBannerView alloc] initWithSlotID:AD_Banner rootViewController:viewController adSize:size];
        self.bannerView.backgroundColor = [UIColor clearColor];
        self.bannerView.frame = frame;
        self.bannerView.delegate = self;
        [self.bannerView loadAdData];
    }
}

- (void)nativeExpressBannerAdViewRenderSuccess:(BUNativeExpressBannerView *)bannerAdView
{
    //在此回调方法中进行广告的展示，可保证播放流畅和展示流畅，用户体验更好。
    NSLog(@"nativeExpressBannerAdViewRenderSuccess");
    
    self.bannerViewCount = 100;
    
    [self.viewController.view addSubview:self.bannerView];
    
    if ([self.delegate respondsToSelector:@selector(myAdManagerBannerAdViewRenderSuccess)]) {
        [self.delegate myAdManagerBannerAdViewRenderSuccess];
    }
}

- (void)nativeExpressAdView:(BUNativeExpressAdView *)nativeExpressAdView dislikeWithReason:(NSArray<BUDislikeWords *> *)filterWords
{
    //【重要】需要在点击叉以后 在这个回调中移除视图，否则，会出现用户点击叉无效的情况
    NSLog(@"nativeExpressAdView");
    [UIView animateWithDuration:0.3 animations:^{
        self.bannerView.alpha = 0;
    } completion:^(BOOL finished) {
        [self.bannerView removeFromSuperview];
        self.bannerView = nil;
        if ([self.delegate respondsToSelector:@selector(myAdManagerBannerViewCloseRefresh)]) {
            [self.delegate myAdManagerBannerViewCloseRefresh];
        }
    }];
}

- (void)nativeExpressBannerAdViewDidCloseOtherController:(BUNativeExpressBannerView *)bannerAdView interactionType:(BUInteractionType)interactionType
{
    NSLog(@"nativeExpressBannerAdViewDidRemoved");
    [UIView animateWithDuration:0.3 animations:^{
        self.bannerView.alpha = 0;
    } completion:^(BOOL finished) {
        [self.bannerView removeFromSuperview];
        self.bannerView = nil;
        if ([self.delegate respondsToSelector:@selector(myAdManagerBannerViewCloseRefresh)]) {
            [self.delegate myAdManagerBannerViewCloseRefresh];
        }
    }];
}
-(void)nativeExpressBannerAdView:(BUNativeExpressBannerView *)bannerAdView dislikeWithReason:(NSArray<BUDislikeWords *> *)filterwords
{
    NSLog(@"nativeExpressBannerAdViewDidRemoved");
    [UIView animateWithDuration:0.3 animations:^{
        self.bannerView.alpha = 0;
    } completion:^(BOOL finished) {
        [self.bannerView removeFromSuperview];
        self.bannerView = nil;
        if ([self.delegate respondsToSelector:@selector(myAdManagerBannerViewCloseRefresh)]) {
            [self.delegate myAdManagerBannerViewCloseRefresh];
        }
    }];
}
- (void)nativeExpressBannerAdViewDidRemoved:(BUNativeExpressBannerView *)nativeExpressAdView
{
    //【重要】若开发者收到此回调，代表穿山甲会主动关闭掉广告，广告移除后需要开发者对界面进行适配
    NSLog(@"nativeExpressBannerAdViewDidRemoved");
    [UIView animateWithDuration:0.3 animations:^{
        self.bannerView.alpha = 0;
    } completion:^(BOOL finished) {
        [self.bannerView removeFromSuperview];
        self.bannerView = nil;
        if ([self.delegate respondsToSelector:@selector(myAdManagerBannerViewCloseRefresh)]) {
            [self.delegate myAdManagerBannerViewCloseRefresh];
        }
    }];
}

- (void)nativeExpressBannerAdView:(BUNativeExpressBannerView *)bannerAdView didLoadFailWithError:(NSError *_Nullable)error
{
    NSLog(@"nativeExpressBannerAdView");
    
    if (error != nil) {
        if (self.bannerView) {
            if (self.bannerViewCount < 5) {
                [self performSelector:@selector(reloadBannerAdView) withObject:nil afterDelay:1.0];
            } else {
                if ([self.delegate respondsToSelector:@selector(myAdManagerLoadBannerViewFailed)]) {
                    [self.delegate myAdManagerLoadBannerViewFailed];
                }
            }
        }
    }
}

- (void)nativeExpressBannerAdViewRenderFail:(BUNativeExpressBannerView *)bannerAdView error:(NSError * __nullable)error
{
    NSLog(@"nativeExpressBannerAdViewRenderFail");
    
    if (error != nil) {
        if (self.bannerView) {
            if (self.bannerViewCount < 5) {
                [self performSelector:@selector(reloadBannerAdView) withObject:nil afterDelay:1.0];
            } else {
                if ([self.delegate respondsToSelector:@selector(myAdManagerLoadBannerViewFailed)]) {
                    [self.delegate myAdManagerLoadBannerViewFailed];
                }
            }
        }
    }
}

- (void)reloadBannerAdView
{
    [self.bannerView loadAdData];
    self.bannerViewCount ++;
}


// RewardedVideoAd
- (void)showRewardedVideoAdWithViewController:(BSBaseViewController *)viewController
{
    self.viewController = viewController;
    
    self.rewardedAdCount = 0;
    
    BURewardedVideoModel *model = [[BURewardedVideoModel alloc] init];
    model.userId = @"";
    if (self.rewardedAd == nil) {
        self.rewardedAd = [[BUNativeExpressRewardedVideoAd alloc] initWithSlotID:@"949339503" rewardedVideoModel:model];
        self.rewardedAd.delegate = self;
        // optional
        //self.rewardedAd.rewardPlayAgainInteractionDelegate = self.expressRewardedVideoAgainDelegateObj;
        [self.rewardedAd loadAdData];
    }
}

- (void)nativeExpressRewardedVideoAdDidDownLoadVideo:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd
{
    //在此回调方法后进行广告的展示，可保证播放流畅和展示流畅，用户体验更好。
    NSLog(@"nativeExpressRewardedVideoAdDidDownLoadVideo");
    
    self.rewardedAdCount = 100;
    
    dispatch_async(dispatch_get_main_queue(), ^{
//        [SVProgressHUD dismiss];
        if (self.rewardedAd) {
            self.isShowRewardedVideoAd = YES;
            [self.rewardedAd showAdFromRootViewController:self.viewController];
        }
        
        if ([self.delegate respondsToSelector:@selector(myAdManagerRewardedVideoAdDidDownLoadVideo)]) {
            [self.delegate myAdManagerRewardedVideoAdDidDownLoadVideo];
        }
    });
}

- (void)nativeExpressRewardedVideoAdDidLoad:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd
{
    NSLog(@"nativeExpressRewardedVideoAdDidLoad");
}

- (void)nativeExpressRewardedVideoAd:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd didFailWithError:(NSError *_Nullable)error
{
    NSLog(@"nativeExpressRewardedVideoAd");
    
    if (error != nil) {
        if (self.rewardedAd) {
            if (self.rewardedAdCount < 8) {
                [self performSelector:@selector(reloadRewardedVideoAd) withObject:nil afterDelay:1.0];
            } else {
                if ([self.delegate respondsToSelector:@selector(myAdManagerLoadRewardedVideoAdFailed)]) {
                    [self.delegate myAdManagerLoadRewardedVideoAdFailed];
                }
            }
        }
    }
}

- (void)nativeExpressRewardedVideoAdDidPlayFinish:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd didFailWithError:(NSError *_Nullable)error
{
    NSLog(@"nativeExpressRewardedVideoAdDidPlayFinish");
    if (error != nil) {
        if (self.rewardedAd) {
            if (self.rewardedAdCount < 8) {
                [self performSelector:@selector(reloadRewardedVideoAd) withObject:nil afterDelay:1.0];
            } else {
                if ([self.delegate respondsToSelector:@selector(myAdManagerLoadRewardedVideoAdFailed)]) {
                    [self.delegate myAdManagerLoadRewardedVideoAdFailed];
                }
            }
        }
    }
}

- (void)reloadRewardedVideoAd
{
    [self.rewardedAd loadAdData];
    self.rewardedAdCount ++;
}

//依据返回的verify（YES/NO）为依据进行激励视频的发放处理
- (void)nativeExpressRewardedVideoAdServerRewardDidSucceed:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd verify:(BOOL)verify
{
    NSLog(@"nativeExpressRewardedVideoAdServerRewardDidSucceed.verify = %d", verify);
    
    self.isVerify = verify;
}

- (void)nativeExpressRewardedVideoAdDidClose:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd
{
    NSLog(@"nativeExpressRewardedVideoAdDidClose");
    
    self.rewardedAd = nil;
    
    self.isShowRewardedVideoAd = NO;
    if ([self.delegate respondsToSelector:@selector(myAdManagerRewardedVideoAdCloseIsVerify:)]) {
        [self.delegate myAdManagerRewardedVideoAdCloseIsVerify:self.isVerify];
    }
}

- (void)nativeExpressRewardedVideoAdDidClick:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd
{
    NSLog(@"nativeExpressRewardedVideoAdDidClick");
    
    self.rewardedAd = nil;
}

- (void)nativeExpressRewardedVideoAdDidClickSkip:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd
{
    NSLog(@"nativeExpressRewardedVideoAdDidClickSkip");
    
}

- (void)nativeExpressRewardedVideoAdCallback:(BUNativeExpressRewardedVideoAd *)rewardedVideoAd withType:(BUNativeExpressRewardedVideoAdType)nativeExpressVideoType
{
    NSLog(@"nativeExpressRewardedVideoAdCallback");
}


@end
