//
//  ZYELabel.m
//  FullstackDrivingSchool
//
//  Created by fs0011 on 2022/7/25.
//

#import "ZYELabel.h"

@implementation ZYELabel{
    UILongPressGestureRecognizer *longPress;
}
- (instancetype)init {
    if (self = [super init]) {
        _textInsets = UIEdgeInsetsZero;
        self.userInteractionEnabled = YES;
        longPress = [[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(copyText:)];
        self.canCopy = NO;
        
    }
    return self;
}

- (void)setCanCopy:(BOOL)canCopy {
    _canCopy = canCopy;
    if (_canCopy) {
        [self addGestureRecognizer:longPress];
    } else {
        [self removeGestureRecognizer:longPress];
    }
}



- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        _textInsets = UIEdgeInsetsZero;
    }
    return self;
}

- (void)drawTextInRect:(CGRect)rect {
    [super drawTextInRect:UIEdgeInsetsInsetRect(rect, _textInsets)];
}

- (void)copyText:(UILongPressGestureRecognizer *)gesture {
    if (gesture.state == UIGestureRecognizerStateBegan) {
        [self becomeFirstResponder];
        
        UIMenuController *menu = [UIMenuController sharedMenuController];
        [menu setTargetRect:self.bounds inView:self];
        [menu setMenuVisible:YES animated:YES];
    }
}

- (BOOL)canBecomeFirstResponder {
    return YES;
}

- (BOOL)canPerformAction:(SEL)action withSender:(id)sender {
    return (action == @selector(copy:));
}

- (void)copy:(id)sender {
    UIPasteboard.generalPasteboard.string = self.text;
}

/*
 // Only override drawRect: if you perform custom drawing.
 // An empty implementation adversely affects performance during animation.
 - (void)drawRect:(CGRect)rect {
 // Drawing code
 }
 */

@end
