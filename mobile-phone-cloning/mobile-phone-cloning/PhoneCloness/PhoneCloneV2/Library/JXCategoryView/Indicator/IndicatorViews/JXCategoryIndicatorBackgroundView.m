//
//  JXCategoryIndicatorBackgroundView.m
//  JXCategoryView
//
//  Created by jiaxin on 2018/8/17.
//  Copyright © 2018年 jiaxin. All rights reserved.
//

#import "JXCategoryIndicatorBackgroundView.h"
#import "JXCategoryFactory.h"

@implementation JXCategoryIndicatorBackgroundView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.indicatorWidth = JXCategoryViewAutomaticDimension;
        self.indicatorHeight = JXCategoryViewAutomaticDimension;
        self.indicatorCornerRadius = JXCategoryViewAutomaticDimension;
        self.indicatorColor = [UIColor lightGrayColor];
        self.indicatorWidthIncrement = 10;
    }
    return self;
}

#pragma mark - JXCategoryIndicatorProtocol

- (void)jx_refreshState:(JXCategoryIndicatorParamsModel *)model {
    self.layer.cornerRadius = [self indicatorCornerRadiusValue:model.selectedCellFrame];
    self.backgroundColor = self.indicatorColor;

    CGFloat width = [self indicatorWidthValue:model.selectedCellFrame];
    CGFloat height = [self indicatorHeightValue:model.selectedCellFrame];
    CGFloat x = model.selectedCellFrame.origin.x + (model.selectedCellFrame.size.width - width)/2;
    CGFloat y = (model.selectedCellFrame.size.height - height)/2;
    self.frame = CGRectMake(x, y, width, height);
}

- (void)jx_contentScrollViewDidScroll:(JXCategoryIndicatorParamsModel *)model {
    CGRect rightCellFrame = model.rightCellFrame;
    CGRect leftCellFrame = model.leftCellFrame;
    CGFloat percent = model.percent;
    CGFloat targetX = 0;
    CGFloat targetWidth = [self indicatorWidthValue:leftCellFrame];

    if (percent == 0) {
        targetX = leftCellFrame.origin.x + (leftCellFrame.size.width - targetWidth)/2.0;
    }else {
        CGFloat leftWidth = targetWidth;
        CGFloat rightWidth = [self indicatorWidthValue:rightCellFrame];

        CGFloat leftX = leftCellFrame.origin.x + (leftCellFrame.size.width - leftWidth)/2;
        CGFloat rightX = rightCellFrame.origin.x + (rightCellFrame.size.width - rightWidth)/2;

        targetX = [JXCategoryFactory interpolationFrom:leftX to:rightX percent:percent];

        if (self.indicatorWidth == JXCategoryViewAutomaticDimension) {
            targetWidth = [JXCategoryFactory interpolationFrom:leftWidth to:rightWidth percent:percent];
        }
    }

    //允许变动frame的情况：1、允许滚动；2、不允许滚动，但是已经通过手势滚动切换一页内容了；
    if (self.isScrollEnabled == YES || (self.isScrollEnabled == NO && percent == 0)) {
        CGFloat height = [self indicatorHeightValue:leftCellFrame];
        CGFloat y = (leftCellFrame.size.height - height)/2;
        self.frame = CGRectMake(targetX, y, targetWidth, height);
    }
}

- (void)jx_selectedCell:(JXCategoryIndicatorParamsModel *)model {
    CGFloat width = [self indicatorWidthValue:model.selectedCellFrame];
    CGFloat height = [self indicatorHeightValue:model.selectedCellFrame];
    CGFloat x = model.selectedCellFrame.origin.x + (model.selectedCellFrame.size.width - width)/2;
    CGFloat y = (model.selectedCellFrame.size.height - height)/2;
    CGRect toFrame = CGRectMake(x, y, width, height);

    if (self.isScrollEnabled) {
        [UIView animateWithDuration:self.scrollAnimationDuration delay:0 options:UIViewAnimationOptionCurveLinear animations:^{
            self.frame = toFrame;
        } completion:^(BOOL finished) {
        }];
    }else {
        self.frame = toFrame;
    }
}

@end

@implementation JXCategoryIndicatorBackgroundView (JXDeprecated)

@dynamic backgroundViewWidth;
@dynamic backgroundViewHeight;
@dynamic backgroundViewWidthIncrement;
@dynamic backgroundViewCornerRadius;
@dynamic backgroundViewColor;

- (void)setBackgroundViewWidth:(CGFloat)backgroundViewWidth {
    self.indicatorWidth = backgroundViewWidth;
}

- (CGFloat)backgroundViewWidth {
    return self.indicatorWidth;
}

- (void)setBackgroundViewHeight:(CGFloat)backgroundViewHeight {
    self.indicatorHeight = backgroundViewHeight;
}

- (CGFloat)backgroundViewHeight {
    return self.indicatorHeight;
}

- (void)setBackgroundViewCornerRadius:(CGFloat)backgroundViewCornerRadius {
    self.indicatorCornerRadius = backgroundViewCornerRadius;
}

- (CGFloat)backgroundViewCornerRadius {
    return self.indicatorCornerRadius;
}

- (void)setBackgroundViewWidthIncrement:(CGFloat)backgroundViewWidthIncrement {
    self.indicatorWidthIncrement = backgroundViewWidthIncrement;
}

- (CGFloat)backgroundViewWidthIncrement {
    return self.indicatorWidthIncrement;
}

- (void)setBackgroundViewColor:(UIColor *)backgroundViewColor {
    self.indicatorColor = backgroundViewColor;
}

- (UIColor *)backgroundViewColor {
    return self.indicatorColor;
}

@end
