//
//  MySegmentedControl.m
//  mobiletransfer
//
//  Created by fs0011 on 2023/12/4.
//  Copyright © 2023 <PERSON><PERSON><PERSON>. All rights reserved.
//

#import "MySegmentedControl.h"

@implementation MySegmentedControl


- (void)layoutSubviews
{
    [super layoutSubviews];
    self.layer.cornerRadius = self.cornerRadius;
    UIImage* im = [self imageWithRoundedCornersOfSize:CGSizeMake((kDeviceWidth-16*2)*1.0/3,44) color:self.tintColor];

    for (UIView* view in self.subviews) {
        if(view.subviews.count==0)
        {
            
            UIImageView* img = view;
            [img setImage:im];
        }
    }
    
    
    
    
}


- (UIImage *)imageWithRoundedCornersOfSize:(CGSize)size color:(UIColor *)color {
    UIGraphicsBeginImageContextWithOptions(size, NO, 0);
    
    UIBezierPath *path = [UIBezierPath bezierPathWithRoundedRect:CGRectMake(0, 0, size.width, size.height) cornerRadius:self.cornerRadius];
    [color setFill];
    [path fill];
    
    UIImage *roundedImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    return roundedImage;
}
/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
