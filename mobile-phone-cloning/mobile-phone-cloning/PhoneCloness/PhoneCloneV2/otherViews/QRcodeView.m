//
//  QRcodeView.m
//  mobiletransfer
//
//  Created by fs0011 on 2023/12/16.
//  Copyright © 2023 <PERSON><PERSON><PERSON>. All rights reserved.
//

#import "QRcodeView.h"

@implementation QRcodeView
{
    UIImageView* qrimage;
}
- (instancetype)initWithQrString:(NSString*)string
{
    if(self == [super init])
    {
        self.backgroundColor =  [UIColor whiteColor];
        [self mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.width.mas_equalTo(kDeviceWidth-46*2);
            
        }];
        UIImageView* im = [UIImageView new];
        [self addSubview:im];
        [im mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.right.left.bottom.mas_equalTo(0);
        }];
        im.image = [self creatQrcode:string];
        im.contentMode = UIViewContentModeScaleAspectFit;
    }
    return self;
}



- (UIImage *)creatQrcode:(NSString *)urlString {
    // Create a data object from the URL string
    NSData *data = [urlString dataUsingEncoding:NSUTF8StringEncoding];
    
    // Create a CIFilter with the QR code generator filter
    CIFilter *qrFilter = [CIFilter filterWithName:@"CIQRCodeGenerator"];
    [qrFilter setValue:data forKey:@"inputMessage"];
    [qrFilter setValue:@"Q" forKey:@"inputCorrectionLevel"];
    
    // Convert the CIImage to UIImage
    CIImage *ciImage = qrFilter.outputImage;
    UIImage *qrCodeImage = [self createNonInterpolatedUIImageFromCIImage:ciImage withScale:2*[[UIScreen mainScreen] scale]];
    
    return qrCodeImage;
}

- (UIImage *)createNonInterpolatedUIImageFromCIImage:(CIImage *)image withScale:(CGFloat)scale {
    CGRect rect = CGRectIntegral(image.extent);
    CGSize size = CGSizeMake(CGRectGetWidth(rect) * scale, CGRectGetHeight(rect) * scale);
    UIGraphicsBeginImageContextWithOptions(size, NO, 0.0f);
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextSetInterpolationQuality(context, kCGInterpolationNone);
    CIContext *ciContext = [CIContext contextWithOptions:nil];
    CGImageRef cgImage = [ciContext createCGImage:image fromRect:rect];
    CGContextDrawImage(context, CGContextGetClipBoundingBox(context), cgImage);
    UIImage *scaledImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    CGImageRelease(cgImage);
    
    return scaledImage;
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
