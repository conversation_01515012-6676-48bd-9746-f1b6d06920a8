//
//  AppDelegate.h
//  PhoneClone
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <UIKit/UIKit.h>

@interface AppDelegate : UIResponder <UIApplicationDelegate>
#define updateJsonUrl @"https://controls.oss-cn-hangzhou.aliyuncs.com/ios-phone-clone_v2.0.json"
#define webVersionJsonUrl @"https://controls.oss-cn-hangzhou.aliyuncs.com/easync_web_html_version.json"
#define webZipUrl @"https://controls.oss-cn-hangzhou.aliyuncs.com/easync_web_dist.zip"
#define AD_APP_ID @"5467234"
#define AD_SplashAd @"888837428"
#define AD_Banner @"955433872"

//#define AD_APP_ID @"5288195"
//#define AD_SplashAd @"887748348"
//#define AD_Banner @"948397009"
@property (strong, nonatomic) UIWindow *window;

@property (strong, nonatomic) NSMutableArray * whiteList;
@property (copy, nonatomic) NSString * currentLinkUrl;
@property (copy, nonatomic) NSString * currentDeviceName; 
@property (copy, nonatomic) NSString * currentLinkToken;
@property (assign, nonatomic)BOOL showGuide;
@property (assign, nonatomic)BOOL showWebLink;
@property (assign, nonatomic)NSInteger allowUseCount;
+ (instancetype)getAppDelegate;

@end

