//
//  tokenTool.m
//  mobiletransfer
//
//  Created by fs0011 on 2023/12/8.
//  Copyright © 2023 Qi<PERSON><PERSON> Chen. All rights reserved.
//

#import "tokenTool.h"
#import <CommonCrypto/CommonDigest.h>
@implementation tokenTool
+ (NSString*)creatToken
{
    NSDate *currentDate = [NSDate date];
        NSTimeInterval timeInterval = [currentDate timeIntervalSince1970];
        NSString *timestamp = [NSString stringWithFormat:@"%.0f", timeInterval];

        // 生成一个随机字符串（英文字母和数字）
        NSString *randomString = [self randomAlphanumericStringWithLength:10];

        // 拼接字符串
        NSString *combinedString = [NSString stringWithFormat:@"%@%@", timestamp, randomString];

        // MD5加密
        NSString *md5Token = [self MD5FromString:combinedString];

    return md5Token;
}

+ (NSString *)randomAlphanumericStringWithLength:(NSInteger)length {
    NSString *letters = @"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    NSMutableString *randomString = [NSMutableString stringWithCapacity:length];

    for (NSInteger i = 0; i < length; i++) {
        [randomString appendFormat:@"%C", [letters characterAtIndex:arc4random_uniform((uint32_t)[letters length])]];
    }

    return randomString;
}

+ (NSString *)MD5FromString:(NSString *)string {
    const char *pointer = [string UTF8String];
    unsigned char md5Buffer[CC_MD5_DIGEST_LENGTH];

    CC_MD5(pointer, (CC_LONG)strlen(pointer), md5Buffer);

    NSMutableString *md5String = [NSMutableString stringWithCapacity:CC_MD5_DIGEST_LENGTH * 2];
    for (int i = 0; i < CC_MD5_DIGEST_LENGTH; i++)
        [md5String appendFormat:@"%02x", md5Buffer[i]];

    return md5String;
}
@end
