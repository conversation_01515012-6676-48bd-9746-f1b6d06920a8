//
//  UILabel+createLabels.m
//  AudioEdit
//
//  Created by billbill on 2021/4/3.
//

#import "UILabel+createLabels.h"
#import "ZYELabel.h"
@implementation UILabel (createLabels)

+ (instancetype)createLabelWithTitle:(NSString *)title textColor:(UIColor *)textColor textAlignment:(NSTextAlignment)alignment font:(UIFont *)font {
    
    ZYELabel * lbl = [[ZYELabel alloc] init];
    lbl.font = font;
    lbl.text = title;
    lbl.textColor = textColor;
    lbl.textAlignment = alignment;
    
    return lbl;
}


+ (instancetype)createTopLeftLabelWithTitle:(NSString *)title textColor:(UIColor *)textColor textAlignment:(NSTextAlignment)alignment font:(UIFont *)font {
    
    ZYELabel * lbl = [[ZYELabel alloc] init];
    lbl.font = font;
    lbl.text = title;
    lbl.textColor = textColor;
    lbl.textAlignment = alignment;
    
    return lbl;
}


- (void)changeAlignmentRightandLeft{
    
    CGRect textSize = [self.text boundingRectWithSize:CGSizeMake(self.frame.size.width, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingTruncatesLastVisibleLine |NSStringDrawingUsesFontLeading attributes:@{NSFontAttributeName : self.font} context:nil];
    
    CGFloat margin = (self.frame.size.width - textSize.size.width) / (self.text.length - 1);
    
    NSNumber *number = [NSNumber numberWithFloat:margin];
    NSMutableAttributedString *attributeString = [[NSMutableAttributedString alloc]initWithString:self.text];
    [attributeString addAttribute:NSKernAttributeName value:number range:NSMakeRange(0, self.text.length - 1)];
    self.attributedText = attributeString;
    
}
@end
