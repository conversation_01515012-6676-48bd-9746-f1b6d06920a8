//
//  FileManager.m
//  mobiletransfer
//
//  Created by fs0011 on 2023/12/12.
//  Copyright © 2023 <PERSON><PERSON><PERSON>. All rights reserved.
//

#import "FileManager.h"
#import <Contacts/Contacts.h>
#import <ContactsUI/ContactsUI.h>
#import <AddressBook/AddressBook.h>
#import <EventKit/EventKit.h>
#import "CalendarListEventInfo.h"
@implementation FileManager
#define receiveFileList @"receiveFileList"
#define sendFileList @"sendFileList"
+ (instancetype)share;
{
    static FileManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[FileManager alloc] init];
    });
    return instance;
}

- (NSMutableArray*)getreceiveFileList
{
    NSMutableArray* arr = [NSMutableArray array];
    if(W_PERSISTENT_GET_OBJECT(receiveFileList))
    {
        [arr addObjectsFromArray:W_PERSISTENT_GET_OBJECT(receiveFileList)];
    }
    return arr;
}
- (void)addReceiveFile:(NSDictionary*)file
{
    NSMutableArray* arr =  [self getreceiveFileList];
    [arr addObject:file];
    W_PERSISTENT_SET_OBJECT(arr, receiveFileList);
    
}

- (NSMutableArray*)getsendFileListData
{
    NSMutableArray* arr = [NSMutableArray array];
    if(W_PERSISTENT_GET_OBJECT(sendFileList))
    {
        [arr addObjectsFromArray:W_PERSISTENT_GET_OBJECT(sendFileList)];
    }
    return  arr;
}

- (NSMutableArray*)getsendFileList
{
    NSMutableArray* arr = [NSMutableArray array];
    for (NSDictionary* dic in [self getsendFileListData]) {
        File* F = [File yy_modelWithDictionary:dic];
        [arr addObject:F];
    }
    
    
    return arr;
}

- (void)addSendFile:(File*)file
{
    NSMutableArray* dataArray =  [NSMutableArray array];
    NSMutableArray* arr = [self getsendFileList];
    [arr addObject:file];
    for (File* allFile in arr) {
        [dataArray addObject:[allFile yy_modelToJSONObject]];
    }
    W_PERSISTENT_SET_OBJECT(dataArray, sendFileList);
}

- (void)setSendFilelist:(NSMutableArray*)arr
{
    
    
    NSMutableArray* dataArray =  [NSMutableArray array];
    for (File* allFile in arr) {
        [dataArray addObject:[allFile yy_modelToJSONObject]];
    }
    W_PERSISTENT_SET_OBJECT(dataArray, sendFileList);
    
}

- (void)completeGetFile:(NSDictionary*)dic
{
    self.needSaveFilePath = dic[@"file"];
    if([[self.needSaveFilePath pathExtension] isEqualToString:@"jpg"] || [[self.needSaveFilePath pathExtension] isEqualToString:@"png"]){
        //保存图片
        UIImage *saveImage = [UIImage imageWithContentsOfFile:self.needSaveFilePath];
        UIImageWriteToSavedPhotosAlbum(saveImage, self, @selector(image:didFinishSavingWithError:contextInfo:), nil);
        
        return;
    }
    if([[self.needSaveFilePath pathExtension] isEqualToString:@"mov"] || [[self.needSaveFilePath pathExtension] isEqualToString:@"mp4"]){
        //保存视频
        NSURL *url = [NSURL URLWithString:self.needSaveFilePath];
        BOOL compatible = UIVideoAtPathIsCompatibleWithSavedPhotosAlbum([url path]);
        if (compatible)
        {
            //保存相册核心代码
            UISaveVideoAtPathToSavedPhotosAlbum([url path], self, @selector(savedVideoPath:didFinishSavingWithError:contextInfo:), nil);
        }
        return;
    }
    if([[self.needSaveFilePath pathExtension] isEqualToString:@"m4a"]){
        //保存音乐
        return;
    }
    if([[self.needSaveFilePath lastPathComponent] isEqualToString:@"myContacts.json"]){
        //保存联系人
        NSData *data = [[NSData alloc] initWithContentsOfFile:self.needSaveFilePath];
        NSArray *contactsArray = [NSJSONSerialization JSONObjectWithData:data options:kNilOptions error:nil];
        [self saveYdCallAnswerPhoneToAdrBook:contactsArray];
    }
    if([[self.needSaveFilePath lastPathComponent] isEqualToString:@"myCalendarEvent.json"]){
        //保存日历事件
        NSData *data = [[NSData alloc] initWithContentsOfFile:self.needSaveFilePath];
        NSArray *calendarEventArray = [NSJSONSerialization JSONObjectWithData:data options:kNilOptions error:nil];
        [self saveLoadCalendarEventArray:calendarEventArray];
    }
}



//保存联系人
- (void)saveYdCallAnswerPhoneToAdrBook:(NSArray *)contactDicArray{
    for(NSDictionary *dic in contactDicArray){
        NSString *contactName = dic[@"ContactName"];
        NSArray *phoneArray = dic[@"ContactPhotoNumberArray"];
        [self createAddBookRecordByPhoneArr:phoneArray
                                   andTitle:contactName
                                    andNote:nil];
    }
}
// 往通讯录添加一条新联系人
- (void)createAddBookRecordByPhoneArr:(NSArray *)phoneArr andTitle:(NSString *)title andNote:(NSString *)note{
    CFErrorRef error = NULL;
    if (!phoneArr || !title) {return;}
    
    ABAddressBookRef addressBook = ABAddressBookCreateWithOptions(NULL, &error);
    ABRecordRef newRecord = ABPersonCreate();
    
    ABRecordSetValue(newRecord, kABPersonFirstNameProperty, (__bridge CFTypeRef)title, &error);
    
    //创建一个多值属性(电话)
    ABMutableMultiValueRef multi = ABMultiValueCreateMutable(kABMultiStringPropertyType);
    //    ABMultiValueAddValueAndLabel(multi, (__bridge CFTypeRef)obj, (__bridge CFStringRef)@"亿点连接呼转测试", NULL);
    
    [phoneArr enumerateObjectsUsingBlock:^(NSString *phone, NSUInteger idx, BOOL * _Nonnull stop) {
        // 添加手机号码
        ABMultiValueAddValueAndLabel(multi, (__bridge CFTypeRef)phone, kABPersonPhoneMobileLabel, NULL);
    }];
    
    ABRecordSetValue(newRecord, kABPersonPhoneProperty, multi, &error);
    
    //添加email
    //    ABMutableMultiValueRef multiEmail = ABMultiValueCreateMutable(kABMultiStringPropertyType);
    //    ABMultiValueAddValueAndLabel(multiEmail, (__bridge CFTypeRef)([self.infoDic objectForKey:@"email"]), kABWorkLabel, NULL);
    //    ABRecordSetValue(newRecord, kABPersonEmailProperty, multiEmail, &error);
    
    // 添加备注
    ABRecordSetValue(newRecord, kABPersonNoteProperty, (__bridge CFTypeRef)note, &error);
    
    //添加记录到通讯录操作对象
    ABAddressBookAddRecord(addressBook, newRecord, &error);
    
    //保存通讯录操作对象
    ABAddressBookSave(addressBook, &error);
    
    CFRelease(multi);
    CFRelease(newRecord);
    CFRelease(addressBook);
}

//保存日历事件
- (void)saveLoadCalendarEventArray:(NSArray *)eventArray{
    
    for(NSDictionary *dic in eventArray){
        [[CalendarListEventInfo sharedEventCalendar]addNewEventCalendarsDiction:dic];
        
    }
}
- (EKCalendar*) findEKCalendar:(NSString *)calendarName eventStore: (EKEventStore*)eventStore{
    NSArray<EKCalendar *> *calendars = [eventStore calendarsForEntityType:EKEntityTypeEvent];
    if (calendars != nil && calendars.count > 0) {
        for (EKCalendar *thisCalendar in calendars) {
            NSLog(@"Calendar: %@", thisCalendar.title);
            if ([thisCalendar.title isEqualToString:calendarName]) {
                return thisCalendar;
            }
            if ([thisCalendar.calendarIdentifier isEqualToString:calendarName]) {
                return thisCalendar;
            }
        }
    }
    NSLog(@"No match found for calendar with name: %@", calendarName);
    return nil;
}
- (void)image:(UIImage *)image didFinishSavingWithError:(NSError *)error  contextInfo:(void *)contextInfo{
    
    if (error) {
        NSLog(@"保存失败");
    }else{
        NSLog(@"保存成功");
    }
}
////保存视频完成之后的回调
- (void) savedVideoPath:(NSString*)videoPath didFinishSavingWithError: (NSError *)error contextInfo: (void *)contextInfo {
    
    if (error) {
        NSLog(@"保存视频失败%@", error.localizedDescription);
    }
    else {
        NSLog(@"保存视频成功");
    }
}
-(void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary *)change context:(void *)context
{
    NSProgress *progress = (NSProgress *)object;
    dispatch_async(dispatch_get_main_queue(), ^{
        //        [self.receiverBtn setProgressValue:progress.fractionCompleted];
        //        if (progress.fractionCompleted == 1.0) {
        //            self.finishSum =  self.finishSum+1;
        //        }
        //        self.endTranslabel.text = [NSString stringWithFormat:NSLocalizedString(@"上传成功： %d个",nil),self.finishSum];
        //        self.endTranslabel.hidden = NO;
    });
}

@end
