//
//  CalendarListEventInfo.m
//  ReplacePhone
//
//  Created by macbookair on 3/1/2020.
//  Copyright © 2020 macbookair. All rights reserved.
//

#import "CalendarListEventInfo.h"
#import <EventKit/EventKit.h>
#import <UIKit/UIKit.h>


@implementation CalendarListEventInfo
static CalendarListEventInfo *calendar;

+ (instancetype)sharedEventCalendar{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        calendar = [[CalendarListEventInfo alloc] init];
    });
    
    return calendar;
}
 
+(instancetype)allocWithZone:(struct _NSZone *)zone{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        calendar = [super allocWithZone:zone];
    });
    return calendar;
}

- (void)addNewEventCalendarsDiction:(NSDictionary *)eventDiction{
    //__weak typeof(self) weakSelf = self;
    
    EKEventStore *eventStore = [[EKEventStore alloc] init];
    if ([eventStore respondsToSelector:@selector(requestAccessToEntityType:completion:)])
    {
        [eventStore requestAccessToEntityType:EKEntityTypeEvent completion:^(BOOL granted, NSError *error){
            
            dispatch_async(dispatch_get_main_queue(), ^{
                //__strong typeof(weakSelf) strongSelf = weakSelf;
                if (error)
                {
                    //[strongSelf showAlert:@"添加失败，请稍后重试"];
                }else if (!granted){
                    //[strongSelf showAlert:@"不允许使用日历,请在设置中允许此App使用日历"];
                    
                }else{
                    EKEvent *event  = [EKEvent eventWithEventStore:eventStore];
                    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
                    [formatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
                    //NSDate *newDate = [formatter dateFromString:@"2016-12-07 14:06:24 +0800"];
                    //for(NSDictionary *dic in eventArray){
                        event.title = eventDiction[@"title"];
                        NSString *stda = eventDiction[@"startDate"];
                        NSString *edda = eventDiction[@"endDate"];
                        event.startDate = [formatter dateFromString:stda];//[NSDate dateWithString:stda formatString:@"YYYY-MM-dd HH:mm:ss"];
                        event.endDate = [formatter dateFromString:edda];//[NSDate dateWithString:edda formatString:@"YYYY-MM-dd HH:mm:ss"];
                        // 是否设置全天
                        event.allDay = eventDiction[@"isAllDay"];
                        event.location = eventDiction[@"location"];
                        event.notes =  eventDiction[@"notes"];
                        if(eventDiction[@"urlPath"]){
                        event.URL = [NSURL fileURLWithPath:eventDiction[@"urlPath"]];
                        }
                    [event setCalendar:[eventStore defaultCalendarForNewEvents]];
                    NSError *err;
                    [eventStore saveEvent:event span:EKSpanThisEvent error:&err];
                }
            });
        }];
    }
}
@end
