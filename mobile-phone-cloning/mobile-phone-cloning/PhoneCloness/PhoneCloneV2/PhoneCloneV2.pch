//
//  PhoneClone.pch
//  PhoneClone
//
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#ifndef PhoneClone_pch
#define PhoneClone_pch

#ifdef __OBJC__

#import "PCObjcect.h"
#import "httpNework.h"
#import "DHIPAdress.h"
#import "FileManager.h"
#import "BSConnectManager.h"
#import "MyHTTPConnection.h"
#import "BSNewSendServer.h"
#import "MyAdManager.h"

#import "UILabel+createLabels.h"
#import "UIColor+Hex.h"
#import "UIButton+Create.h"
#import "UIButton+CenterImageAndTitle.h"

#import "Macro.h"
#import "AppDelegate.h"
#import "BSSendFileManager.h"
#import "BSReciveFileManager.h"
#import "StoreIAPManager.h"
#import "UpdateManager.h"

#import <YYModel.h>
#import <JKCategories.h>
#import <MBProgressHUD.h>
#import <SVProgressHUD/SVProgressHUD.h>
#import <AFNetworking/AFNetworking.h>
#import <UINavigationController+FDFullscreenPopGesture.h>
#import <BlocksKit/UIView+BlocksKit.h>
#import <BlocksKit/UIControl+BlocksKit.h>
#import <BlocksKit/NSArray+BlocksKit.h>
#import <UMCommon/UMCommon.h>
#import <BUAdSDK/BUAdSDK.h>
#import <SSZipArchive/SSZipArchive.h>
//#import <BUAdTestMeasurement/BUAdTestMeasurement.h>
#define APPDELEGATE (AppDelegate*)[UIApplication sharedApplication].delegate
#define local(x)   NSLocalizedString(x, @"description for this key.")
#define SAVEIMAGEFILE [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject]
#define kDeviceWidth [UIScreen mainScreen].bounds.size.width
#define kDeviceHeight [UIScreen mainScreen].bounds.size.height
#define INTERFACE_IS_IPHONEX (@available(iOS 11.0, *) && ([UIApplication sharedApplication].keyWindow.safeAreaInsets.bottom > 0)?YES:NO)
#define KDNavH (INTERFACE_IS_IPHONEX?20:0) //额外的高度

#endif

#endif
/******************************************************************************
 *    永久存储对象
 *
 *  NSUserDefaults保存的文件在tmp文件夹里
 *
 *    @param    object      需存储的对象
 *    @param    key         对应的key
 */
#define W_PERSISTENT_SET_OBJECT(object, key)                                                                                                 \
({                                                                                                                                             \
NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];                                                                          \
[defaults setObject:object forKey:key];                                                                                                    \
[defaults synchronize];                                                                                                                    \
})

/**
 *    取出永久存储的对象
 *
 *    @param    key     所需对象对应的key
 *    @return    key     所对应的对象
 */
#define W_PERSISTENT_GET_OBJECT(key)  [[NSUserDefaults standardUserDefaults] objectForKey:key]

/**
 *  清除 NSUserDefaults 保存的所有数据
 */
#define W_PERSISTENT_REMOVE_ALLDATA   [[NSUserDefaults standardUserDefaults] removePersistentDomainForName:[[NSBundle mainBundle] bundleIdentifier]]

/**
 *  清除 NSUserDefaults 保存的指定数据
 */
#define W_PERSISTENT_REMOVE(_key)                                         \
({                                                                          \
NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];       \
[defaults removeObjectForKey:_key];                                     \
[defaults synchronize];                                                 \
})
